{"name": "workflow-manager-pro", "private": true, "version": "1.0.0", "description": "Professional N8N workflow management and automation tool", "author": "N8N Lab Team <<EMAIL>>", "license": "Commercial", "homepage": "https://n8nlab.app", "repository": {"type": "git", "url": "https://github.com/n8nlab/workflow-manager.git"}, "keywords": ["workflow", "automation", "n8n", "ai", "productivity", "desktop"], "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.1", "@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-notification": "^2", "@tauri-apps/plugin-os": "^2", "@tauri-apps/plugin-fs": "^2", "@tauri-apps/plugin-dialog": "^2", "axios": "^1.6.2", "react-hot-toast": "^2.4.1", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0", "class-variance-authority": "^0.7.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-toast": "^1.1.5", "lucide-react": "^0.294.0", "reactflow": "^11.10.4", "recharts": "^2.8.0", "date-fns": "^2.30.0"}, "devDependencies": {"@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "typescript": "~5.6.2", "vite": "^6.0.3", "@tauri-apps/cli": "^2", "tailwindcss": "^3.4.0", "postcss": "^8.4.32", "autoprefixer": "^10.4.16", "@types/node": "^20.10.0"}}