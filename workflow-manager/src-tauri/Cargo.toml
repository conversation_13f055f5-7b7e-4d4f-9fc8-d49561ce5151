[package]
name = "workflow-manager"
version = "1.0.0"
description = "Professional N8N workflow management and automation tool with AI-powered workflow generation"
authors = ["N8N Lab Team <<EMAIL>>"]
license = "Commercial"
repository = "https://github.com/n8nlab/workflow-manager"
homepage = "https://n8nlab.app"
keywords = ["workflow", "automation", "n8n", "ai", "productivity"]
categories = ["development-tools"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "workflow_manager_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = ["tray-icon"] }
tauri-plugin-opener = "2"
tauri-plugin-sql = { version = "2", features = ["sqlite"] }
tauri-plugin-store = "2"
tauri-plugin-notification = "2"
tauri-plugin-shell = "2"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
sqlx = { version = "0.8", features = ["runtime-tokio-rustls", "sqlite", "chrono", "uuid"] }
tokio = { version = "1", features = ["full"] }
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.0", features = ["v4", "serde"] }
reqwest = { version = "0.12", features = ["json"] }
anyhow = "1.0"
thiserror = "1.0"
zeroize = { version = "1.7", features = ["derive"] }
aes-gcm = "0.10"
rand = "0.8"
base64 = "0.22"
async-trait = "0.1"
dirs = "5.0"
url = "2.4"
log = "0.4"

[dev-dependencies]
tempfile = "3.8"
tokio-test = "0.4"

[target.'cfg(target_os = "macos")'.dependencies]
security-framework = "2.9"
objc = "0.2"

[target.'cfg(target_os = "windows")'.dependencies]
winapi = { version = "0.3", features = ["wincred", "winbase", "errhandlingapi"] }

