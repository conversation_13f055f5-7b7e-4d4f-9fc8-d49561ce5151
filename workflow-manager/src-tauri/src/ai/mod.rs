use reqwest::{Client, header::{HeaderMap, HeaderValue, AUTHOR<PERSON>ZATION, CONTENT_TYPE}};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, Clone)]
pub enum AiProvider {
    OpenAi,
    <PERSON>,
    DeepSeek,
    Aiml,
    LocalModel,
}

#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct AiClient {
    client: Client,
    provider: AiProvider,
    api_key: Option<String>,
    base_url: String,
    model: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AiMessage {
    pub role: String,
    pub content: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AiRequest {
    pub model: String,
    pub messages: Vec<AiMessage>,
    pub max_tokens: Option<u32>,
    pub temperature: Option<f32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AiResponse {
    pub content: String,
    pub usage: Option<AiUsage>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AiUsage {
    pub prompt_tokens: u32,
    pub completion_tokens: u32,
    pub total_tokens: u32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct WorkflowGenerationRequest {
    pub description: String,
    pub requirements: Vec<String>,
    pub preferred_nodes: Option<Vec<String>>,
    pub complexity: Option<String>, // "simple", "medium", "complex"
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GeneratedWorkflow {
    pub name: String,
    pub description: String,
    pub nodes: serde_json::Value,
    pub connections: serde_json::Value,
    pub settings: Option<serde_json::Value>,
    pub tags: Vec<String>,
}

impl AiClient {
    pub fn new(provider: AiProvider, api_key: Option<String>, model: Option<String>) -> Result<Self, String> {
        let mut headers = HeaderMap::new();
        headers.insert(CONTENT_TYPE, HeaderValue::from_static("application/json"));

        let (base_url, default_model) = match provider {
            AiProvider::OpenAi => {
                if let Some(ref key) = api_key {
                    headers.insert(AUTHORIZATION, HeaderValue::from_str(&format!("Bearer {}", key))
                        .map_err(|e| format!("Invalid OpenAI API key format: {}", e))?);
                }
                ("https://api.openai.com/v1".to_string(), "gpt-4".to_string())
            },
            AiProvider::Claude => {
                if let Some(ref key) = api_key {
                    headers.insert("x-api-key", HeaderValue::from_str(key)
                        .map_err(|e| format!("Invalid Claude API key format: {}", e))?);
                    headers.insert("anthropic-version", HeaderValue::from_static("2023-06-01"));
                }
                ("https://api.anthropic.com".to_string(), "claude-3-sonnet-20240229".to_string())
            },
            AiProvider::DeepSeek => {
                if let Some(ref key) = api_key {
                    headers.insert(AUTHORIZATION, HeaderValue::from_str(&format!("Bearer {}", key))
                        .map_err(|e| format!("Invalid DeepSeek API key format: {}", e))?);
                }
                ("https://api.deepseek.com/v1".to_string(), "deepseek-chat".to_string())
            },
            AiProvider::Aiml => {
                if let Some(ref key) = api_key {
                    headers.insert(AUTHORIZATION, HeaderValue::from_str(&format!("Bearer {}", key))
                        .map_err(|e| format!("Invalid AIML API key format: {}", e))?);
                }
                ("https://api.aimlapi.com/v1".to_string(), "gpt-4".to_string())
            },
            AiProvider::LocalModel => {
                ("http://localhost:11434".to_string(), "llama2".to_string()) // Ollama default
            },
        };

        let client = Client::builder()
            .default_headers(headers)
            .timeout(std::time::Duration::from_secs(120))
            .build()
            .map_err(|e| format!("Failed to create HTTP client: {}", e))?;

        Ok(Self {
            client,
            provider,
            api_key,
            base_url,
            model: model.unwrap_or(default_model),
        })
    }

    pub async fn generate_workflow(&self, request: &WorkflowGenerationRequest) -> Result<GeneratedWorkflow, String> {
        let system_prompt = r#"You are an expert N8N workflow designer. You MUST respond with ONLY valid JSON, no explanations or additional text.

CRITICAL: Your response must be ONLY a valid JSON object with this exact structure:
{
  "name": "Workflow Name",
  "description": "Detailed description",
  "nodes": [],
  "connections": {},
  "settings": {},
  "tags": ["tag1", "tag2"]
}

Do not include any text before or after the JSON. Do not include explanations. Only return the JSON object."#;

        let user_prompt = format!(
            "RESPOND WITH ONLY JSON. Create an N8N workflow for: {}\n\nRequirements: {}\nComplexity: {}\nPreferred nodes: {}\n\nReturn ONLY the JSON object, no explanations:",
            request.description,
            request.requirements.join(", "),
            request.complexity.as_deref().unwrap_or("medium"),
            request.preferred_nodes.as_ref().map(|nodes| nodes.join(", ")).unwrap_or_else(|| "Any suitable nodes".to_string())
        );

        let messages = vec![
            AiMessage {
                role: "system".to_string(),
                content: system_prompt.to_string(),
            },
            AiMessage {
                role: "user".to_string(),
                content: user_prompt,
            },
        ];

        let response = self.chat(&messages, Some(4000), Some(0.7)).await?;

        // Debug: Print the raw AI response
        println!("🤖 Raw AI response from {}: {}",
            match self.provider {
                AiProvider::OpenAi => "OpenAI",
                AiProvider::Claude => "Claude",
                AiProvider::DeepSeek => "DeepSeek",
                AiProvider::Aiml => "AIML",
                AiProvider::LocalModel => "Local",
            },
            &response.content
        );

        // Try to parse the response as JSON, with fallback for text responses
        let workflow: GeneratedWorkflow = match serde_json::from_str(&response.content) {
            Ok(workflow) => workflow,
            Err(_) => {
                println!("❌ Direct JSON parsing failed. Attempting to extract JSON from text response...");

                // Try to find JSON within the response
                let json_content = if let Some(start) = response.content.find('{') {
                    if let Some(end) = response.content.rfind('}') {
                        if end > start {
                            &response.content[start..=end]
                        } else {
                            &response.content
                        }
                    } else {
                        &response.content
                    }
                } else {
                    // If no JSON found, create a basic workflow from the text description
                    println!("🔄 No JSON found in response. Creating basic workflow structure...");
                    return Ok(GeneratedWorkflow {
                        name: "AI Generated Workflow".to_string(),
                        description: response.content.chars().take(200).collect::<String>(),
                        nodes: serde_json::Value::Array(vec![]),
                        connections: serde_json::Value::Object(serde_json::Map::new()),
                        settings: Some(serde_json::Value::Object(serde_json::Map::new())),
                        tags: vec!["ai-generated".to_string()],
                    });
                };

                serde_json::from_str(json_content)
                    .map_err(|e| {
                        println!("❌ JSON extraction also failed. First 500 chars of response: {}",
                            &response.content.chars().take(500).collect::<String>());
                        format!("Failed to parse AI response as workflow JSON: {}", e)
                    })?
            }
        };

        Ok(workflow)
    }

    pub async fn chat(&self, messages: &[AiMessage], max_tokens: Option<u32>, temperature: Option<f32>) -> Result<AiResponse, String> {
        match self.provider {
            AiProvider::OpenAi | AiProvider::DeepSeek | AiProvider::Aiml => {
                self.openai_compatible_chat(messages, max_tokens, temperature).await
            },
            AiProvider::Claude => {
                self.claude_chat(messages, max_tokens, temperature).await
            },
            AiProvider::LocalModel => {
                self.ollama_chat(messages, max_tokens, temperature).await
            },
        }
    }

    async fn openai_compatible_chat(&self, messages: &[AiMessage], max_tokens: Option<u32>, temperature: Option<f32>) -> Result<AiResponse, String> {
        let url = format!("{}/chat/completions", self.base_url);
        
        let mut request_body = serde_json::json!({
            "model": self.model,
            "messages": messages,
        });

        if let Some(max_tokens) = max_tokens {
            request_body["max_tokens"] = serde_json::Value::Number(max_tokens.into());
        }
        if let Some(temperature) = temperature {
            request_body["temperature"] = serde_json::Value::Number(serde_json::Number::from_f64(temperature as f64).unwrap());
        }

        let response = self.client
            .post(&url)
            .json(&request_body)
            .send()
            .await
            .map_err(|e| format!("Chat request failed: {}", e))?;

        if response.status().is_success() {
            let response_json: serde_json::Value = response
                .json()
                .await
                .map_err(|e| format!("Failed to parse chat response: {}", e))?;

            let content = response_json["choices"][0]["message"]["content"]
                .as_str()
                .ok_or("No content in response")?
                .to_string();

            let usage = response_json.get("usage").and_then(|u| {
                Some(AiUsage {
                    prompt_tokens: u["prompt_tokens"].as_u64().unwrap_or(0) as u32,
                    completion_tokens: u["completion_tokens"].as_u64().unwrap_or(0) as u32,
                    total_tokens: u["total_tokens"].as_u64().unwrap_or(0) as u32,
                })
            });

            Ok(AiResponse { content, usage })
        } else {
            Err(format!("Chat request failed with status: {}", response.status()))
        }
    }

    async fn claude_chat(&self, messages: &[AiMessage], max_tokens: Option<u32>, temperature: Option<f32>) -> Result<AiResponse, String> {
        let url = format!("{}/v1/messages", self.base_url);
        
        let mut request_body = serde_json::json!({
            "model": self.model,
            "messages": messages,
            "max_tokens": max_tokens.unwrap_or(4000),
        });

        if let Some(temperature) = temperature {
            request_body["temperature"] = serde_json::Value::Number(serde_json::Number::from_f64(temperature as f64).unwrap());
        }

        let response = self.client
            .post(&url)
            .json(&request_body)
            .send()
            .await
            .map_err(|e| format!("Claude chat request failed: {}", e))?;

        if response.status().is_success() {
            let response_json: serde_json::Value = response
                .json()
                .await
                .map_err(|e| format!("Failed to parse Claude response: {}", e))?;

            let content = response_json["content"][0]["text"]
                .as_str()
                .ok_or("No content in Claude response")?
                .to_string();

            let usage = response_json.get("usage").and_then(|u| {
                Some(AiUsage {
                    prompt_tokens: u["input_tokens"].as_u64().unwrap_or(0) as u32,
                    completion_tokens: u["output_tokens"].as_u64().unwrap_or(0) as u32,
                    total_tokens: (u["input_tokens"].as_u64().unwrap_or(0) + u["output_tokens"].as_u64().unwrap_or(0)) as u32,
                })
            });

            Ok(AiResponse { content, usage })
        } else {
            Err(format!("Claude chat request failed with status: {}", response.status()))
        }
    }

    async fn ollama_chat(&self, messages: &[AiMessage], _max_tokens: Option<u32>, temperature: Option<f32>) -> Result<AiResponse, String> {
        let url = format!("{}/api/chat", self.base_url);
        
        let mut request_body = serde_json::json!({
            "model": self.model,
            "messages": messages,
            "stream": false,
        });

        if let Some(temperature) = temperature {
            request_body["options"] = serde_json::json!({
                "temperature": temperature
            });
        }

        let response = self.client
            .post(&url)
            .json(&request_body)
            .send()
            .await
            .map_err(|e| format!("Ollama chat request failed: {}", e))?;

        if response.status().is_success() {
            let response_json: serde_json::Value = response
                .json()
                .await
                .map_err(|e| format!("Failed to parse Ollama response: {}", e))?;

            let content = response_json["message"]["content"]
                .as_str()
                .ok_or("No content in Ollama response")?
                .to_string();

            Ok(AiResponse { content, usage: None })
        } else {
            Err(format!("Ollama chat request failed with status: {}", response.status()))
        }
    }
}
