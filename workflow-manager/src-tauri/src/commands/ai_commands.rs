use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager};
use crate::{AppState, ai::{<PERSON><PERSON><PERSON>, AiProvider, WorkflowGenerationRequest, AiMessage}, credentials::CredentialManager};

#[tauri::command]
pub async fn debug_ai_test() -> Result<String, String> {
    println!("🔧 debug_ai_test called - Tauri commands are working!");
    Ok("AI commands are working!".to_string())
}

#[tauri::command]
pub async fn generate_workflow_with_ai(
    app_handle: AppHandle,
    provider: String,
    description: String,
    requirements: Vec<String>,
    preferred_nodes: Option<Vec<String>>,
    complexity: Option<String>,
) -> Result<serde_json::Value, String> {
    println!("🚀 generate_workflow_with_ai called with provider: {}, description: {}", provider, description);
    let state = app_handle.state::<AppState>();
    let credential_service = &state.credential_service;

    // Parse provider
    let ai_provider = match provider.to_lowercase().as_str() {
        "openai" => AiProvider::OpenAi,
        "claude" => AiProvider::Claude,
        "deepseek" => AiProvider::DeepSeek,
        "aiml" => AiProvider::Aiml,
        "local" => AiProvider::LocalModel,
        _ => return Err("Unsupported AI provider".to_string()),
    };

    // Get API key if needed
    let api_key = if matches!(ai_provider, AiProvider::LocalModel) {
        None
    } else {
        let key_name = match ai_provider {
            AiProvider::OpenAi => "ai.openai",
            AiProvider::Claude => "ai.claude",
            AiProvider::DeepSeek => "ai.deepseek",
            AiProvider::Aiml => "ai.aiml",
            _ => return Err("Invalid provider for API key lookup".to_string()),
        };

        {
            println!("Looking for credential with key: {}", key_name);
            let result = credential_service
                .retrieve_credential(key_name)
                .await
                .map_err(|e| {
                    println!("Error retrieving credential '{}': {}", key_name, e);
                    format!("Failed to get API key: {}", e)
                })?;

            match &result {
                Some(cred) => {
                    println!("Found credential '{}' with type: {:?}", key_name, cred.metadata.credential_type);
                    result.map(|cred| cred.value.expose_secret().to_string())
                },
                None => {
                    println!("No credential found for key: {}", key_name);
                    // Let's also list all available credentials for debugging
                    match credential_service.list_credentials().await {
                        Ok(all_creds) => {
                            println!("Available credentials:");
                            for cred in all_creds {
                                println!("  - ID: '{}', Type: {:?}, Name: '{}'", cred.id, cred.credential_type, cred.name);
                            }
                        },
                        Err(e) => println!("Failed to list credentials: {}", e),
                    }
                    None
                }
            }
        }
    };

    // Create AI client
    let client = AiClient::new(ai_provider, api_key, None)?;

    // Generate workflow
    let request = WorkflowGenerationRequest {
        description,
        requirements,
        preferred_nodes,
        complexity,
    };

    let generated_workflow = client.generate_workflow(&request).await?;

    // Convert to JSON for frontend
    Ok(serde_json::to_value(generated_workflow)
        .map_err(|e| format!("Failed to serialize workflow: {}", e))?)
}

#[tauri::command]
pub async fn chat_with_ai(
    app_handle: AppHandle,
    provider: String,
    messages: Vec<serde_json::Value>,
    max_tokens: Option<u32>,
    temperature: Option<f32>,
) -> Result<String, String> {
    println!("🚀 chat_with_ai called with provider: {}, messages count: {}", provider, messages.len());
    let state = app_handle.state::<AppState>();
    let credential_service = &state.credential_service;

    // Parse provider
    let ai_provider = match provider.to_lowercase().as_str() {
        "openai" => AiProvider::OpenAi,
        "claude" => AiProvider::Claude,
        "deepseek" => AiProvider::DeepSeek,
        "aiml" => AiProvider::Aiml,
        "local" => AiProvider::LocalModel,
        _ => return Err("Unsupported AI provider".to_string()),
    };

    // Get API key if needed
    let api_key = if matches!(ai_provider, AiProvider::LocalModel) {
        None
    } else {
        let key_name = match ai_provider {
            AiProvider::OpenAi => "ai.openai",
            AiProvider::Claude => "ai.claude",
            AiProvider::DeepSeek => "ai.deepseek",
            AiProvider::Aiml => "ai.aiml",
            _ => return Err("Invalid provider for API key lookup".to_string()),
        };

        {
            println!("Looking for credential with key: {}", key_name);
            let result = credential_service
                .retrieve_credential(key_name)
                .await
                .map_err(|e| {
                    println!("Error retrieving credential '{}': {}", key_name, e);
                    format!("Failed to get API key: {}", e)
                })?;

            match &result {
                Some(cred) => {
                    println!("Found credential '{}' with type: {:?}", key_name, cred.metadata.credential_type);
                    result.map(|cred| cred.value.expose_secret().to_string())
                },
                None => {
                    println!("No credential found for key: {}", key_name);
                    None
                }
            }
        }
    };

    // Create AI client
    let client = AiClient::new(ai_provider, api_key, None)?;

    // Convert messages
    let ai_messages: Result<Vec<AiMessage>, String> = messages
        .into_iter()
        .map(|msg| {
            Ok(AiMessage {
                role: msg["role"].as_str().ok_or("Missing role in message")?.to_string(),
                content: msg["content"].as_str().ok_or("Missing content in message")?.to_string(),
            })
        })
        .collect();

    let ai_messages = ai_messages?;

    // Send chat request
    let response = client.chat(&ai_messages, max_tokens, temperature).await?;

    Ok(response.content)
}

#[tauri::command]
pub async fn get_available_ai_providers() -> Result<Vec<serde_json::Value>, String> {
    Ok(vec![
        serde_json::json!({
            "id": "openai",
            "name": "OpenAI",
            "description": "GPT-4 and other OpenAI models",
            "requires_api_key": true,
            "models": ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo"]
        }),
        serde_json::json!({
            "id": "claude",
            "name": "Anthropic Claude",
            "description": "Claude 3 family models",
            "requires_api_key": true,
            "models": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307"]
        }),
        serde_json::json!({
            "id": "deepseek",
            "name": "DeepSeek",
            "description": "DeepSeek AI models",
            "requires_api_key": true,
            "models": ["deepseek-chat", "deepseek-coder"]
        }),
        serde_json::json!({
            "id": "aiml",
            "name": "AIML API",
            "description": "Access to various AI models via AIML",
            "requires_api_key": true,
            "models": ["gpt-4", "gpt-3.5-turbo", "claude-3-sonnet"]
        }),
        serde_json::json!({
            "id": "local",
            "name": "Local Model",
            "description": "Local models via Ollama",
            "requires_api_key": false,
            "models": ["llama2", "codellama", "mistral", "neural-chat"]
        }),
    ])
}

#[tauri::command]
pub async fn test_ai_connection(
    app_handle: AppHandle,
    provider: String,
    api_key: Option<String>,
) -> Result<bool, String> {
    // Parse provider
    let ai_provider = match provider.to_lowercase().as_str() {
        "openai" => AiProvider::OpenAi,
        "claude" => AiProvider::Claude,
        "deepseek" => AiProvider::DeepSeek,
        "aiml" => AiProvider::Aiml,
        "local" => AiProvider::LocalModel,
        _ => return Err("Unsupported AI provider".to_string()),
    };

    // Create AI client
    let client = AiClient::new(ai_provider, api_key, None)?;

    // Test with a simple message
    let test_messages = vec![
        AiMessage {
            role: "user".to_string(),
            content: "Hello, please respond with 'OK' to confirm the connection is working.".to_string(),
        }
    ];

    match client.chat(&test_messages, Some(10), Some(0.1)).await {
        Ok(_) => Ok(true),
        Err(_) => Ok(false),
    }
}

#[tauri::command]
pub async fn improve_workflow_with_ai(
    app_handle: AppHandle,
    provider: String,
    workflow_json: serde_json::Value,
    improvement_goals: Vec<String>,
) -> Result<serde_json::Value, String> {
    let state = app_handle.state::<AppState>();
    let credential_service = &state.credential_service;

    // Parse provider
    let ai_provider = match provider.to_lowercase().as_str() {
        "openai" => AiProvider::OpenAi,
        "claude" => AiProvider::Claude,
        "deepseek" => AiProvider::DeepSeek,
        "aiml" => AiProvider::Aiml,
        "local" => AiProvider::LocalModel,
        _ => return Err("Unsupported AI provider".to_string()),
    };

    // Get API key if needed
    let api_key = if matches!(ai_provider, AiProvider::LocalModel) {
        None
    } else {
        let key_name = match ai_provider {
            AiProvider::OpenAi => "ai.openai",
            AiProvider::Claude => "ai.claude",
            AiProvider::DeepSeek => "ai.deepseek",
            AiProvider::Aiml => "ai.aiml",
            _ => return Err("Invalid provider for API key lookup".to_string()),
        };

        {
            println!("Looking for credential with key: {}", key_name);
            let result = credential_service
                .retrieve_credential(key_name)
                .await
                .map_err(|e| {
                    println!("Error retrieving credential '{}': {}", key_name, e);
                    format!("Failed to get API key: {}", e)
                })?;

            match &result {
                Some(cred) => {
                    println!("Found credential '{}' with type: {:?}", key_name, cred.metadata.credential_type);
                    result.map(|cred| cred.value.expose_secret().to_string())
                },
                None => {
                    println!("No credential found for key: {}", key_name);
                    None
                }
            }
        }
    };

    // Create AI client
    let client = AiClient::new(ai_provider, api_key, None)?;

    let system_prompt = r#"You are an expert N8N workflow optimizer. Analyze the provided workflow and improve it based on the specified goals. 

Return a JSON object with the improved workflow in the same format as the input, but with optimizations applied.

Focus on:
- Performance improvements
- Error handling
- Best practices
- Security considerations
- Maintainability

Preserve the core functionality while making the requested improvements."#;

    let user_prompt = format!(
        "Please improve this N8N workflow based on the following goals:\n\nGoals:\n{}\n\nCurrent workflow:\n{}",
        improvement_goals.join("\n- "),
        serde_json::to_string_pretty(&workflow_json).unwrap_or_else(|_| "Invalid JSON".to_string())
    );

    let messages = vec![
        AiMessage {
            role: "system".to_string(),
            content: system_prompt.to_string(),
        },
        AiMessage {
            role: "user".to_string(),
            content: user_prompt,
        },
    ];

    let response = client.chat(&messages, Some(4000), Some(0.3)).await?;
    
    // Try to parse the response as JSON
    let improved_workflow: serde_json::Value = serde_json::from_str(&response.content)
        .map_err(|e| format!("Failed to parse AI response as workflow JSON: {}", e))?;

    Ok(improved_workflow)
}
