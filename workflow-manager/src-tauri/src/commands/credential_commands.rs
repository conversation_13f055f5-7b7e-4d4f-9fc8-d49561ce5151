use crate::{
    credentials::{CredentialManager, CredentialType, CredentialMetadata},
    AppState,
};
use serde::{Deserialize, Serialize};
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager};

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateCredentialRequest {
    pub credential_type: CredentialType,
    pub name: String,
    pub value: String,
    pub description: Option<String>,
    pub instance_name: Option<String>, // For N8N instances or local models
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateCredentialRequest {
    pub value: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CredentialInfo {
    pub id: String,
    pub credential_type: CredentialType,
    pub name: String,
    pub description: Option<String>,
    pub created_at: String,
    pub last_used: Option<String>,
    pub is_valid: bool,
    pub has_value: bool, // Don't expose actual values
}

#[derive(Debug, Serialize, Deserialize)]
pub struct StorageBackendInfo {
    pub backend_type: String,
    pub is_keychain_available: bool,
    pub is_using_fallback: bool,
    pub storage_location: Option<String>,
}

impl From<CredentialMetadata> for CredentialInfo {
    fn from(metadata: CredentialMetadata) -> Self {
        Self {
            id: metadata.id,
            credential_type: metadata.credential_type,
            name: metadata.name,
            description: metadata.description,
            created_at: metadata.created_at.to_rfc3339(),
            last_used: metadata.last_used.map(|dt| dt.to_rfc3339()),
            is_valid: metadata.is_valid,
            has_value: true, // If we have metadata, we have a value
        }
    }
}

#[tauri::command]
pub async fn create_credential(
    app_handle: AppHandle,
    request: CreateCredentialRequest,
) -> Result<CredentialInfo, String> {
    let state = app_handle.state::<AppState>();
    let credential_service = &state.credential_service;

    // Generate appropriate credential ID
    let id = if let Some(instance_name) = &request.instance_name {
        crate::credentials::service::CredentialService::generate_credential_id(
            &request.credential_type,
            instance_name,
        )
    } else {
        crate::credentials::service::CredentialService::generate_credential_id(
            &request.credential_type,
            "default",
        )
    };

    credential_service
        .store_credential(
            &id,
            &request.value,
            request.credential_type.clone(),
        )
        .await
        .map_err(|e| format!("Failed to store credential: {}", e))?;

    // Retrieve the stored credential to return metadata
    let stored_credential = credential_service
        .retrieve_credential(&id)
        .await
        .map_err(|e| format!("Failed to retrieve stored credential: {}", e))?
        .ok_or_else(|| "Credential was stored but could not be retrieved".to_string())?;

    Ok(stored_credential.metadata.into())
}

#[tauri::command]
pub async fn get_credential_info(
    app_handle: AppHandle,
    id: String,
) -> Result<Option<CredentialInfo>, String> {
    let state = app_handle.state::<AppState>();
    let credential_service = &state.credential_service;

    let credential = credential_service
        .retrieve_credential(&id)
        .await
        .map_err(|e| format!("Failed to retrieve credential: {}", e))?;

    Ok(credential.map(|c| c.metadata.into()))
}

#[tauri::command]
pub async fn list_credentials(
    app_handle: AppHandle,
) -> Result<Vec<CredentialInfo>, String> {
    let state = app_handle.state::<AppState>();
    let credential_service = &state.credential_service;

    let credentials = credential_service
        .list_credentials()
        .await
        .map_err(|e| format!("Failed to list credentials: {}", e))?;

    Ok(credentials.into_iter().map(|c| c.into()).collect())
}

#[tauri::command]
pub async fn update_credential(
    app_handle: AppHandle,
    id: String,
    request: UpdateCredentialRequest,
) -> Result<CredentialInfo, String> {
    let state = app_handle.state::<AppState>();
    let credential_service = &state.credential_service;

    credential_service
        .update_credential(&id, &request.value)
        .await
        .map_err(|e| format!("Failed to update credential: {}", e))?;

    // Retrieve the updated credential to return metadata
    let updated_credential = credential_service
        .retrieve_credential(&id)
        .await
        .map_err(|e| format!("Failed to retrieve updated credential: {}", e))?
        .ok_or_else(|| "Credential not found after update".to_string())?;

    Ok(updated_credential.metadata.into())
}

#[tauri::command]
pub async fn delete_credential(
    app_handle: AppHandle,
    id: String,
) -> Result<bool, String> {
    let state = app_handle.state::<AppState>();
    let credential_service = &state.credential_service;

    match credential_service.delete_credential(&id).await {
        Ok(result) => Ok(result),
        Err(e) => Err(format!("Failed to delete credential: {}", e))
    }
}

#[tauri::command]
pub async fn validate_credential(
    app_handle: AppHandle,
    id: String,
) -> Result<bool, String> {
    let state = app_handle.state::<AppState>();
    let credential_service = &state.credential_service;

    credential_service
        .validate_credential(&id)
        .await
        .map_err(|e| format!("Failed to validate credential: {}", e))
}

#[tauri::command]
pub async fn test_credential_format(
    app_handle: AppHandle,
    credential_type: CredentialType,
    value: String,
) -> Result<bool, String> {
    let state = app_handle.state::<AppState>();
    let credential_service = &state.credential_service;

    credential_service
        .validate_credential_value(&credential_type, &value)
        .await
        .map_err(|e| format!("Failed to validate credential format: {}", e))
}

#[tauri::command]
pub async fn test_credential_connectivity(
    app_handle: AppHandle,
    credential_type: CredentialType,
    value: String,
) -> Result<bool, String> {
    let state = app_handle.state::<AppState>();
    let credential_service = &state.credential_service;

    credential_service
        .test_credential_connectivity(&credential_type, &value)
        .await
        .map_err(|e| format!("Failed to test credential connectivity: {}", e))
}

#[tauri::command]
pub async fn get_storage_backend_info(
    app_handle: AppHandle,
) -> Result<StorageBackendInfo, String> {
    let state = app_handle.state::<AppState>();
    let credential_service = &state.credential_service;

    let is_keychain_available = credential_service.is_keychain_available().await;
    
    Ok(StorageBackendInfo {
        backend_type: if is_keychain_available {
            "keychain".to_string()
        } else {
            "encrypted_file".to_string()
        },
        is_keychain_available,
        is_using_fallback: !is_keychain_available,
        storage_location: if is_keychain_available {
            None
        } else {
            Some("Local encrypted file".to_string())
        },
    })
}

#[tauri::command]
pub async fn switch_storage_backend(
    app_handle: AppHandle,
    use_keychain: bool,
) -> Result<StorageBackendInfo, String> {
    let state = app_handle.state::<AppState>();
    let credential_service = &state.credential_service;

    if use_keychain {
        credential_service
            .switch_to_primary()
            .await
            .map_err(|e| format!("Failed to switch to keychain: {}", e))?;
    } else {
        credential_service
            .switch_to_fallback()
            .await
            .map_err(|e| format!("Failed to switch to encrypted file storage: {}", e))?;
    }

    get_storage_backend_info(app_handle).await
}

#[tauri::command]
pub async fn mark_credential_used(
    app_handle: AppHandle,
    id: String,
) -> Result<(), String> {
    let state = app_handle.state::<AppState>();
    let credential_service = &state.credential_service;

    credential_service
        .mark_credential_used(&id)
        .await
        .map_err(|e| format!("Failed to mark credential as used: {}", e))
}

// Helper command to generate credential IDs for frontend
#[tauri::command]
pub async fn generate_credential_id(
    credential_type: CredentialType,
    instance_name: String,
) -> Result<String, String> {
    Ok(crate::credentials::service::CredentialService::generate_credential_id(
        &credential_type,
        &instance_name,
    ))
}

// Helper command to get credential types for frontend
#[tauri::command]
pub async fn get_credential_types() -> Result<Vec<CredentialType>, String> {
    Ok(vec![
        CredentialType::N8nApiKey,
        CredentialType::OpenAiApiKey,
        CredentialType::ClaudeApiKey,
        CredentialType::DeepSeekApiKey,
        CredentialType::AimlApiKey,
        CredentialType::LocalModelConfig,
    ])
}
