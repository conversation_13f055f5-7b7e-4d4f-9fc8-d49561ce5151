use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>};
use crate::{AppState, models::*, database::repositories::Repository};
use chrono::Utc;

#[tauri::command]
pub async fn get_execution(
    app_handle: AppHandle,
    id: String,
) -> Result<Option<Execution>, String> {
    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;

    repositories
        .executions
        .get_by_id(&id)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn list_executions(
    app_handle: AppHandle,
    workflow_id: Option<String>,
    limit: Option<i64>,
    offset: Option<i64>,
) -> Result<Vec<Execution>, String> {
    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;

    if let Some(workflow_id) = workflow_id {
        repositories
            .executions
            .get_by_workflow(&workflow_id, limit, offset)
            .await
            .map_err(|e| e.to_string())
    } else {
        repositories
            .executions
            .list(limit, offset)
            .await
            .map_err(|e| e.to_string())
    }
}

#[tauri::command]
pub async fn execute_workflow(
    app_handle: AppHandle,
    workflow_id: String,
    input_data: Option<serde_json::Value>,
) -> Result<Execution, String> {
    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;

    // Create execution directly in database for now
    // TODO: Integrate with N8N API when ready
    let create_data = CreateExecution {
        workflow_id,
        n8n_execution_id: format!("temp_{}", uuid::Uuid::new_v4()),
        mode: "manual".to_string(),
        started_at: Utc::now(),
        data: input_data,
    };

    let execution = Execution::new(create_data);

    repositories
        .executions
        .create(&execution)
        .await
        .map_err(|e| e.to_string())
}


