use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>};
use crate::AppState;
use crate::database::repositories::Repository;

pub mod n8n_instance_commands;
pub mod workflow_commands;
pub mod execution_commands;
pub mod template_commands;
pub mod ai_config_commands;
pub mod seed_commands;
pub mod credential_commands;
pub mod ai_commands;
pub mod system_commands;
pub mod settings_commands;

pub use n8n_instance_commands::*;
pub use workflow_commands::*;
pub use execution_commands::*;
pub use template_commands::*;
pub use ai_config_commands::*;
pub use seed_commands::*;

#[tauri::command]
pub async fn get_app_version() -> String {
    env!("CARGO_PKG_VERSION").to_string()
}

#[tauri::command]
pub async fn get_database_path(app_handle: AppHandle) -> Result<String, String> {
    crate::database::get_database_path(&app_handle)
        .await
        .map_err(|e| e.to_string())
}

#[derive(serde::Serialize)]
pub struct DashboardData {
    pub total_executions: i64,
    pub successful_executions: i64,
    pub failed_executions: i64,
    pub avg_execution_time: f64,
    pub monthly_executions: Vec<MonthlyExecution>,
    pub recent_executions: Vec<RecentExecution>,
    pub top_workflows: Vec<TopWorkflow>,
    pub trends: Option<DashboardTrends>,
    pub data_source: String,
    pub last_sync_message: Option<String>,
    pub data_age: Option<String>,
    pub instance_count: i64,
    pub message: Option<String>,
}

#[derive(serde::Serialize)]
pub struct MonthlyExecution {
    pub name: String,
    pub executions: i64,
}

#[derive(serde::Serialize)]
pub struct RecentExecution {
    pub id: String,
    pub workflow_name: String,
    pub status: String,
    pub started_at: String,
    pub finished_at: Option<String>,
}

#[derive(serde::Serialize)]
pub struct TopWorkflow {
    pub id: String,
    pub name: String,
    pub execution_count: i64,
    pub success_rate: f64,
}

#[derive(serde::Serialize)]
pub struct DashboardTrends {
    pub total_executions: TrendValue,
    pub successful_executions: TrendValue,
    pub failed_executions: TrendValue,
    pub avg_execution_time: TrendValue,
}

#[derive(serde::Serialize)]
pub struct TrendValue {
    pub value: f64,
    pub is_positive: bool,
}

#[tauri::command]
pub async fn get_dashboard_data(app_handle: AppHandle) -> Result<DashboardData, String> {
    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;

    // Get instance count
    let instances = repositories
        .n8n_instances
        .list(None, None)
        .await
        .map_err(|e| e.to_string())?;

    let instance_count = instances.len() as i64;

    // Get real execution data from database
    let executions = repositories
        .executions
        .list(Some(1000), None) // Get last 1000 executions
        .await
        .unwrap_or_default();

    let workflows = repositories
        .workflows
        .list(None, None)
        .await
        .unwrap_or_default();

    // Calculate real metrics
    let total_executions = executions.len() as i64;
    let successful_executions = executions.iter().filter(|e| e.status == "success").count() as i64;
    let failed_executions = executions.iter().filter(|e| e.status == "failed").count() as i64;

    // Calculate average execution time (in milliseconds)
    let avg_execution_time = if !executions.is_empty() {
        let total_time: f64 = executions.iter()
            .filter_map(|e| {
                if let Some(end_time) = &e.finished_at {
                    Some((end_time.timestamp_millis() - e.started_at.timestamp_millis()) as f64)
                } else { None }
            })
            .sum();
        total_time / executions.len() as f64
    } else {
        0.0
    };

    // Generate monthly execution data
    let mut monthly_map = std::collections::HashMap::new();
    for execution in &executions {
        let month_key = execution.started_at.format("%b").to_string();
        *monthly_map.entry(month_key).or_insert(0) += 1;
    }

    let monthly_executions: Vec<MonthlyExecution> = monthly_map
        .into_iter()
        .map(|(name, executions)| MonthlyExecution { name, executions })
        .collect();

    // Get recent executions (last 10)
    let mut recent_executions: Vec<RecentExecution> = executions
        .iter()
        .take(10)
        .map(|e| RecentExecution {
            id: e.id.clone(),
            workflow_name: workflows.iter()
                .find(|w| w.id == e.workflow_id)
                .map(|w| w.name.clone())
                .unwrap_or_else(|| "Unknown Workflow".to_string()),
            status: e.status.clone(),
            started_at: e.started_at.to_rfc3339(),
            finished_at: e.finished_at.map(|dt| dt.to_rfc3339()),
        })
        .collect();

    // Sort by started_at descending
    recent_executions.sort_by(|a, b| b.started_at.cmp(&a.started_at));

    // Calculate top workflows
    let mut workflow_stats = std::collections::HashMap::new();
    for execution in &executions {
        let entry = workflow_stats.entry(execution.workflow_id.clone()).or_insert((0, 0));
        entry.0 += 1; // total count
        if execution.status == "success" {
            entry.1 += 1; // success count
        }
    }

    let top_workflows: Vec<TopWorkflow> = workflow_stats
        .into_iter()
        .filter_map(|(workflow_id, (total, success))| {
            workflows.iter()
                .find(|w| w.id == workflow_id)
                .map(|w| TopWorkflow {
                    id: w.id.clone(),
                    name: w.name.clone(),
                    execution_count: total,
                    success_rate: if total > 0 { (success as f64 / total as f64) * 100.0 } else { 0.0 },
                })
        })
        .collect();

    let dashboard_data = DashboardData {
        total_executions,
        successful_executions,
        failed_executions,
        avg_execution_time,
        monthly_executions,
        recent_executions,
        top_workflows,
        trends: None, // TODO: Calculate trends by comparing with previous period
        data_source: if instance_count > 0 && total_executions > 0 {
            "real_database_data".to_string()
        } else if instance_count > 0 {
            "instances_configured_no_data".to_string()
        } else {
            "no_instances_configured".to_string()
        },
        last_sync_message: if total_executions > 0 {
            Some(format!("Showing data from {} executions across {} workflows", total_executions, workflows.len()))
        } else if instance_count > 0 {
            Some("N8N instances configured. Use sync commands to fetch execution data.".to_string())
        } else {
            Some("No N8N instances configured. Add an instance to get started.".to_string())
        },
        data_age: None,
        instance_count,
        message: if instance_count == 0 {
            Some("Configure your first N8N instance to start tracking workflows and executions.".to_string())
        } else if total_executions == 0 {
            Some(format!("{} N8N instance(s) configured. Sync executions to see real data.", instance_count))
        } else {
            Some(format!("Showing real data from {} executions across {} workflows.", total_executions, workflows.len()))
        },
    };

    Ok(dashboard_data)
}

// Helper function to get repositories from app state
pub fn get_repositories(app_handle: &AppHandle) -> Result<std::sync::Arc<crate::database::repositories::Repositories>, String> {
    Ok(app_handle
        .state::<AppState>()
        .repositories
        .clone())
}
