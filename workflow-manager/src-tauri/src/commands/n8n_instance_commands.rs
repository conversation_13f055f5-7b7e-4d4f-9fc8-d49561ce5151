use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, Emitter};
use crate::{AppState, models::*, database::repositories::Repository, credentials::{CredentialType, CredentialManager}};

#[tauri::command]
pub async fn create_n8n_instance(
    app_handle: AppHandle,
    create_data: CreateN8nInstance,
) -> Result<N8nInstance, String> {
    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;
    let credential_service = &state.credential_service;

    // Create the instance first
    let mut instance = N8nInstance::new(create_data.clone());

    // Store the API key securely
    let credential_id = format!("n8n_instance_{}", instance.id);

    credential_service
        .store_credential(
            &credential_id,
            &create_data.api_key,
            CredentialType::N8nApiKey,
        )
        .await
        .map_err(|e| format!("Failed to store API key: {}", e))?;

    // Save the instance to database
    repositories
        .n8n_instances
        .create(&instance)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn get_n8n_instance(
    app_handle: AppHandle,
    id: String,
) -> Result<Option<N8nInstance>, String> {
    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;
    
    repositories
        .n8n_instances
        .get_by_id(&id)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn get_n8n_instance_with_stats(
    app_handle: AppHandle,
    id: String,
) -> Result<Option<N8nInstanceWithStats>, String> {
    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;
    
    repositories
        .n8n_instances
        .get_with_stats(&id)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn list_n8n_instances(
    app_handle: AppHandle,
    limit: Option<i64>,
    offset: Option<i64>,
) -> Result<Vec<N8nInstance>, String> {
    println!("list_n8n_instances called with limit: {:?}, offset: {:?}", limit, offset);
    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;

    let result = repositories
        .n8n_instances
        .list(limit, offset)
        .await
        .map_err(|e| e.to_string());

    match &result {
        Ok(instances) => println!("Successfully retrieved {} instances", instances.len()),
        Err(e) => println!("Error listing instances: {}", e),
    }

    result
}

#[tauri::command]
pub async fn update_n8n_instance(
    app_handle: AppHandle,
    id: String,
    update_data: UpdateN8nInstance,
) -> Result<N8nInstance, String> {
    println!("update_n8n_instance called with id: {}", id);
    println!("update_data: {:?}", update_data);

    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;
    let credential_service = &state.credential_service;

    let mut instance = repositories
        .n8n_instances
        .get_by_id(&id)
        .await
        .map_err(|e| {
            println!("Error getting instance by id: {}", e);
            e.to_string()
        })?
        .ok_or_else(|| {
            println!("Instance not found with id: {}", id);
            "Instance not found".to_string()
        })?;

    // Update API key if provided
    if let Some(api_key) = &update_data.api_key {
        if !api_key.is_empty() {
            let credential_id = format!("n8n_instance_{}", instance.id);

            credential_service
                .store_credential(
                    &credential_id,
                    api_key,
                    CredentialType::N8nApiKey,
                )
                .await
                .map_err(|e| format!("Failed to update API key: {}", e))?;
        }
    }

    // Update fields
    if let Some(name) = update_data.name {
        instance.name = name;
    }
    if let Some(url) = update_data.url {
        instance.url = url;
    }
    if let Some(description) = update_data.description {
        instance.description = Some(description);
    }
    if let Some(is_default) = update_data.is_default {
        instance.is_default = is_default;
    }
    if let Some(status) = update_data.status {
        instance.set_status(status);
    }

    let result = repositories
        .n8n_instances
        .update(&instance)
        .await
        .map_err(|e| {
            println!("Error updating instance in database: {}", e);
            e.to_string()
        });

    match &result {
        Ok(updated_instance) => {
            println!("Successfully updated instance: {:?}", updated_instance);
        }
        Err(e) => {
            println!("Failed to update instance: {}", e);
        }
    }

    result
}

#[tauri::command]
pub async fn delete_n8n_instance(
    app_handle: AppHandle,
    id: String,
) -> Result<bool, String> {
    println!("🗑️ delete_n8n_instance called with id: {}", id);

    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;
    let credential_service = &state.credential_service;

    // Get the instance first to get its name for credential deletion
    let instance = repositories
        .n8n_instances
        .get_by_id(&id)
        .await
        .map_err(|e| {
            println!("❌ Error getting instance by id: {}", e);
            e.to_string()
        })?
        .ok_or_else(|| {
            println!("❌ Instance not found with id: {}", id);
            "Instance not found".to_string()
        })?;

    println!("📋 Found instance: {} ({})", instance.name, instance.id);

    // Delete the associated API key from credential storage
    // Try both credential ID formats to ensure cleanup
    let credential_id_old = format!("n8n_instance_{}", id);
    let credential_id_new = format!("n8n.{}", instance.name);

    println!("🔑 Attempting to delete credentials with IDs: {} and {}", credential_id_old, credential_id_new);

    let _ = credential_service.delete_credential(&credential_id_old).await; // Try old format
    let _ = credential_service.delete_credential(&credential_id_new).await; // Try new format

    // Delete the instance from database
    println!("🗄️ Deleting instance from database");
    let result = repositories
        .n8n_instances
        .delete(&id)
        .await
        .map_err(|e| {
            println!("❌ Error deleting instance from database: {}", e);
            e.to_string()
        })?;

    println!("✅ Instance deletion completed successfully: {}", result);
    Ok(result)
}

#[tauri::command]
pub async fn sync_workflows_from_n8n(
    app_handle: AppHandle,
    instance_id: String,
) -> Result<Vec<Workflow>, String> {
    use crate::n8n_api::{N8nApiClient, n8n_workflow_to_model};

    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;
    let credential_service = &state.credential_service;

    // Get the N8N instance
    let instance = repositories
        .n8n_instances
        .get_by_id(&instance_id)
        .await
        .map_err(|e| e.to_string())?
        .ok_or("N8N instance not found")?;

    // Get the API key
    let credential_id = format!("n8n_instance_{}", instance_id);
    let api_key = credential_service
        .retrieve_credential(&credential_id)
        .await
        .map_err(|e| format!("Failed to get API key: {}", e))?
        .ok_or("API key not found")?
        .value
        .expose_secret()
        .to_string();

    // Create N8N API client
    let n8n_client = N8nApiClient::new(instance.url.clone(), api_key)
        .map_err(|e| format!("Failed to create N8N client: {}", e))?;

    // Fetch workflows from N8N
    let n8n_workflows = n8n_client
        .list_workflows(None)
        .await
        .map_err(|e| format!("Failed to fetch workflows from N8N: {}", e))?;

    let mut synced_workflows = Vec::new();

    // Convert and store each workflow
    for n8n_workflow in n8n_workflows.data {
        let workflow = n8n_workflow_to_model(&n8n_workflow, &instance_id);

        // Check if workflow already exists
        let existing = repositories
            .workflows
            .get_by_n8n_workflow_id(&workflow.n8n_workflow_id)
            .await
            .map_err(|e| e.to_string())?;

        let saved_workflow = if let Some(mut existing_workflow) = existing {
            // Update existing workflow
            existing_workflow.name = workflow.name;
            existing_workflow.is_active = workflow.is_active;
            existing_workflow.nodes = workflow.nodes;
            existing_workflow.connections = workflow.connections;
            existing_workflow.settings = workflow.settings;
            existing_workflow.static_data = workflow.static_data;
            existing_workflow.updated_at = chrono::Utc::now();

            repositories
                .workflows
                .update(&existing_workflow)
                .await
                .map_err(|e| e.to_string())?
        } else {
            // Create new workflow
            repositories
                .workflows
                .create(&workflow)
                .await
                .map_err(|e| e.to_string())?
        };

        synced_workflows.push(saved_workflow);
    }

    // Emit sync event to update dashboard
    if let Err(e) = app_handle.emit_to("main", "sync_completed", serde_json::json!({
        "total_workflows": synced_workflows.len(),
        "total_executions": 0,
        "timestamp": chrono::Utc::now().to_rfc3339(),
        "manual_sync": true,
        "instance_id": instance_id
    })) {
        eprintln!("Failed to emit sync completed event: {}", e);
    }

    Ok(synced_workflows)
}

#[tauri::command]
pub async fn sync_executions_from_n8n(
    app_handle: AppHandle,
    instance_id: String,
    workflow_id: Option<String>,
    limit: Option<u32>,
) -> Result<Vec<Execution>, String> {
    use crate::n8n_api::{N8nApiClient, n8n_execution_to_model};

    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;
    let credential_service = &state.credential_service;

    // Get the N8N instance
    let instance = repositories
        .n8n_instances
        .get_by_id(&instance_id)
        .await
        .map_err(|e| e.to_string())?
        .ok_or("N8N instance not found")?;

    // Get the API key
    let credential_id = format!("n8n_instance_{}", instance_id);
    let api_key = credential_service
        .retrieve_credential(&credential_id)
        .await
        .map_err(|e| format!("Failed to get API key: {}", e))?
        .ok_or("API key not found")?
        .value
        .expose_secret()
        .to_string();

    // Create N8N API client
    let n8n_client = N8nApiClient::new(instance.url.clone(), api_key)
        .map_err(|e| format!("Failed to create N8N client: {}", e))?;

    // Fetch executions from N8N
    let n8n_executions = n8n_client
        .list_executions(workflow_id.as_deref(), limit)
        .await
        .map_err(|e| format!("Failed to fetch executions from N8N: {}", e))?;

    let mut synced_executions = Vec::new();

    // Convert and store each execution
    for n8n_execution in n8n_executions.data {
        // Find the corresponding workflow in our database
        let workflow = repositories
            .workflows
            .get_by_n8n_workflow_id(&n8n_execution.workflow_id)
            .await
            .map_err(|e| e.to_string())?;

        if let Some(workflow) = workflow {
            let execution = n8n_execution_to_model(&n8n_execution, &instance_id);

            // Check if execution already exists
            let existing = repositories
                .executions
                .get_by_n8n_execution_id(&execution.n8n_execution_id)
                .await
                .map_err(|e| e.to_string())?;

            let saved_execution = if let Some(mut existing_execution) = existing {
                // Update existing execution
                existing_execution.status = execution.status;
                existing_execution.finished_at = execution.finished_at;
                existing_execution.duration = execution.duration;
                existing_execution.data = execution.data;
                existing_execution.error = execution.error;
                existing_execution.updated_at = chrono::Utc::now();

                repositories
                    .executions
                    .update(&existing_execution)
                    .await
                    .map_err(|e| e.to_string())?
            } else {
                // Create new execution
                repositories
                    .executions
                    .create(&execution)
                    .await
                    .map_err(|e| e.to_string())?
            };

            synced_executions.push(saved_execution);
        }
    }

    // Emit sync event to update dashboard
    if let Err(e) = app_handle.emit_to("main", "sync_completed", serde_json::json!({
        "total_workflows": 0,
        "total_executions": synced_executions.len(),
        "timestamp": chrono::Utc::now().to_rfc3339(),
        "manual_sync": true,
        "instance_id": instance_id
    })) {
        eprintln!("Failed to emit sync completed event: {}", e);
    }

    Ok(synced_executions)
}

#[tauri::command]
pub async fn set_default_n8n_instance(
    app_handle: AppHandle,
    id: String,
) -> Result<(), String> {
    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;
    
    repositories
        .n8n_instances
        .set_default(&id)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn get_default_n8n_instance(
    app_handle: AppHandle,
) -> Result<Option<N8nInstance>, String> {
    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;
    
    repositories
        .n8n_instances
        .get_default()
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn get_active_n8n_instances(
    app_handle: AppHandle,
) -> Result<Vec<N8nInstance>, String> {
    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;
    
    repositories
        .n8n_instances
        .get_active_instances()
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn ping_n8n_instance(
    app_handle: AppHandle,
    id: String,
) -> Result<bool, String> {
    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;
    let credential_service = &state.credential_service;

    let mut instance = repositories
        .n8n_instances
        .get_by_id(&id)
        .await
        .map_err(|e| e.to_string())?
        .ok_or("Instance not found")?;

    // Retrieve the API key from credential storage
    let credential_id = format!("n8n_instance_{}", instance.id);
    let credential = credential_service
        .retrieve_credential(&credential_id)
        .await
        .map_err(|e| format!("Failed to retrieve API key: {}", e))?
        .ok_or("API key not found")?;

    let api_key = credential.value.expose_secret().to_string();

    // Create N8N API client and test connection
    let client = crate::n8n_api::N8nApiClient::new(instance.url.clone(), api_key)
        .map_err(|e| format!("Failed to create API client: {}", e))?;

    match client.health_check().await {
        Ok(health) => {
            // Update instance status to active
            instance.ping_success(health.version);
            repositories
                .n8n_instances
                .update(&instance)
                .await
                .map_err(|e| e.to_string())?;
            Ok(true)
        }
        Err(e) => {
            // Update instance status to error
            instance.ping_failed();
            repositories
                .n8n_instances
                .update(&instance)
                .await
                .map_err(|e| e.to_string())?;
            Err(format!("Connection test failed: {}", e))
        }
    }
}

#[tauri::command]
pub async fn get_n8n_instance_api_key(
    app_handle: AppHandle,
    instance_id: String,
) -> Result<Option<String>, String> {
    let state = app_handle.state::<AppState>();
    let credential_service = &state.credential_service;

    let credential_id = format!("n8n_instance_{}", instance_id);

    match credential_service.retrieve_credential(&credential_id).await {
        Ok(Some(credential)) => Ok(Some(credential.value.expose_secret().to_string())),
        Ok(None) => Ok(None),
        Err(_) => Ok(None), // Return None if credential doesn't exist
    }
}
