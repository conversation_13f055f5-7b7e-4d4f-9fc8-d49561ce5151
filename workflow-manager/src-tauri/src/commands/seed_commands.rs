use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>};
use crate::{AppState, models::*, database::repositories::Repository};

#[tauri::command]
pub async fn seed_sample_templates(app_handle: AppHandle) -> Result<String, String> {
    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;

    // Sample template 1: Email Automation
    let template1 = Template {
        id: "template-1".to_string(),
        name: "Email Automation Workflow".to_string(),
        description: Some("Automatically send emails based on triggers".to_string()),
        category: "automation".to_string(),
        tags: Some(r#"["email", "automation", "notifications"]"#.to_string()),
        nodes: r#"[
            {
                "id": "trigger",
                "type": "webhook",
                "name": "Webhook Trigger",
                "position": [100, 100]
            },
            {
                "id": "email",
                "type": "email",
                "name": "Send Email",
                "position": [300, 100]
            }
        ]"#.to_string(),
        connections: r#"{
            "trigger": {
                "main": [
                    [
                        {
                            "node": "email",
                            "type": "main",
                            "index": 0
                        }
                    ]
                ]
            }
        }"#.to_string(),
        settings: Some(r#"{"executionOrder": "v1"}"#.to_string()),
        static_data: None,
        variables: Some(r#"{"email_subject": "Default Subject", "email_body": "Default Body"}"#.to_string()),
        is_public: true,
        usage_count: 0,
        created_at: chrono::Utc::now(),
        updated_at: chrono::Utc::now(),
        created_by: Some("system".to_string()),
    };

    // Sample template 2: Data Processing
    let template2 = Template {
        id: "template-2".to_string(),
        name: "Data Processing Pipeline".to_string(),
        description: Some("Process and transform data from various sources".to_string()),
        category: "data_processing".to_string(),
        tags: Some(r#"["data", "processing", "transformation"]"#.to_string()),
        nodes: r#"[
            {
                "id": "http",
                "type": "http",
                "name": "HTTP Request",
                "position": [100, 100]
            },
            {
                "id": "transform",
                "type": "function",
                "name": "Transform Data",
                "position": [300, 100]
            },
            {
                "id": "database",
                "type": "postgres",
                "name": "Save to Database",
                "position": [500, 100]
            }
        ]"#.to_string(),
        connections: r#"{
            "http": {
                "main": [
                    [
                        {
                            "node": "transform",
                            "type": "main",
                            "index": 0
                        }
                    ]
                ]
            },
            "transform": {
                "main": [
                    [
                        {
                            "node": "database",
                            "type": "main",
                            "index": 0
                        }
                    ]
                ]
            }
        }"#.to_string(),
        settings: Some(r#"{"executionOrder": "v1"}"#.to_string()),
        static_data: None,
        variables: Some(r#"{"api_url": "https://api.example.com", "db_table": "processed_data"}"#.to_string()),
        is_public: true,
        usage_count: 0,
        created_at: chrono::Utc::now(),
        updated_at: chrono::Utc::now(),
        created_by: Some("system".to_string()),
    };

    // Sample template 3: Slack Integration
    let template3 = Template {
        id: "template-3".to_string(),
        name: "Slack Notification Bot".to_string(),
        description: Some("Send automated notifications to Slack channels".to_string()),
        category: "integration".to_string(),
        tags: Some(r#"["slack", "notifications", "bot"]"#.to_string()),
        nodes: r#"[
            {
                "id": "schedule",
                "type": "cron",
                "name": "Schedule Trigger",
                "position": [100, 100]
            },
            {
                "id": "slack",
                "type": "slack",
                "name": "Send Slack Message",
                "position": [300, 100]
            }
        ]"#.to_string(),
        connections: r#"{
            "schedule": {
                "main": [
                    [
                        {
                            "node": "slack",
                            "type": "main",
                            "index": 0
                        }
                    ]
                ]
            }
        }"#.to_string(),
        settings: Some(r#"{"executionOrder": "v1"}"#.to_string()),
        static_data: None,
        variables: Some("{\"slack_channel\": \"#general\", \"message\": \"Automated notification\"}".to_string()),
        is_public: true,
        usage_count: 0,
        created_at: chrono::Utc::now(),
        updated_at: chrono::Utc::now(),
        created_by: Some("system".to_string()),
    };

    // Create templates
    repositories.templates.create(&template1).await.map_err(|e| e.to_string())?;
    repositories.templates.create(&template2).await.map_err(|e| e.to_string())?;
    repositories.templates.create(&template3).await.map_err(|e| e.to_string())?;

    Ok("Sample templates created successfully".to_string())
}
