use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>};
use crate::{AppState, models::*, database::repositories::Repository};
use std::collections::HashSet;

#[tauri::command]
pub async fn get_app_settings(
    app_handle: AppHandle,
) -> Result<AppSettings, String> {
    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;

    repositories
        .app_settings
        .get_settings()
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn update_app_settings(
    app_handle: AppHandle,
    update_data: UpdateAppSettings,
) -> Result<AppSettings, String> {
    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;

    repositories
        .app_settings
        .update_settings(update_data)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn reset_app_settings(
    app_handle: AppHandle,
) -> Result<AppSettings, String> {
    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;

    let default_settings = AppSettings::default();
    repositories
        .app_settings
        .update(&default_settings)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn get_syncing_instances(
    app_handle: AppHandle,
) -> Result<Vec<String>, String> {
    let state = app_handle.state::<AppState>();
    let sync_service = &state.sync_service;

    let syncing_instances = sync_service.get_syncing_instances().await;
    Ok(syncing_instances.into_iter().collect())
}
