use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>};
use crate::system::{SystemManager, SystemNotification, autostart};
use std::collections::HashMap;

#[tauri::command]
pub async fn show_notification(
    app_handle: AppHandle,
    title: String,
    body: String,
    icon: Option<String>,
    tag: Option<String>,
) -> Result<(), String> {
    let system_manager = SystemManager::new(app_handle);
    
    let notification = SystemNotification {
        title,
        body,
        icon,
        tag,
    };

    system_manager.show_notification(notification).await
}

#[tauri::command]
pub async fn get_system_info(app_handle: AppHandle) -> Result<HashMap<String, String>, String> {
    let system_manager = SystemManager::new(app_handle);
    system_manager.get_system_info().await
}

#[tauri::command]
pub fn minimize_to_tray(app_handle: AppHandle) -> Result<(), String> {
    let system_manager = SystemManager::new(app_handle);
    system_manager.minimize_to_tray()
}

#[tauri::command]
pub fn restore_from_tray(app_handle: AppHandle) -> Result<(), String> {
    let system_manager = SystemManager::new(app_handle);
    system_manager.restore_from_tray()
}

#[tauri::command]
pub fn quit_app(app_handle: AppHandle) -> Result<(), String> {
    let system_manager = SystemManager::new(app_handle);
    system_manager.quit_app()
}

#[tauri::command]
pub fn enable_autostart(app_handle: AppHandle) -> Result<(), String> {
    let app_name = &app_handle.package_info().name;
    let app_path = std::env::current_exe()
        .map_err(|e| format!("Failed to get current executable path: {}", e))?;
    
    autostart::enable_autostart(app_name, &app_path)
}

#[tauri::command]
pub fn disable_autostart(app_handle: AppHandle) -> Result<(), String> {
    let app_name = &app_handle.package_info().name;
    autostart::disable_autostart(app_name)
}

#[tauri::command]
pub fn is_autostart_enabled(app_handle: AppHandle) -> Result<bool, String> {
    let app_name = &app_handle.package_info().name;
    autostart::is_autostart_enabled(app_name)
}

#[tauri::command]
pub async fn notify_workflow_execution_started(
    app_handle: AppHandle,
    workflow_name: String,
) -> Result<(), String> {
    let system_manager = SystemManager::new(app_handle);
    let notification = crate::system::notifications::workflow_execution_started(&workflow_name);
    system_manager.show_notification(notification).await
}

#[tauri::command]
pub async fn notify_workflow_execution_completed(
    app_handle: AppHandle,
    workflow_name: String,
    success: bool,
) -> Result<(), String> {
    let system_manager = SystemManager::new(app_handle);
    let notification = crate::system::notifications::workflow_execution_completed(&workflow_name, success);
    system_manager.show_notification(notification).await
}

#[tauri::command]
pub async fn notify_n8n_instance_connected(
    app_handle: AppHandle,
    instance_name: String,
) -> Result<(), String> {
    let system_manager = SystemManager::new(app_handle);
    let notification = crate::system::notifications::n8n_instance_connected(&instance_name);
    system_manager.show_notification(notification).await
}

#[tauri::command]
pub async fn notify_n8n_instance_disconnected(
    app_handle: AppHandle,
    instance_name: String,
) -> Result<(), String> {
    let system_manager = SystemManager::new(app_handle);
    let notification = crate::system::notifications::n8n_instance_disconnected(&instance_name);
    system_manager.show_notification(notification).await
}

#[tauri::command]
pub async fn notify_sync_completed(
    app_handle: AppHandle,
    execution_count: usize,
) -> Result<(), String> {
    let system_manager = SystemManager::new(app_handle);
    let notification = crate::system::notifications::sync_completed(execution_count);
    system_manager.show_notification(notification).await
}

#[tauri::command]
pub async fn notify_ai_workflow_generated(
    app_handle: AppHandle,
    workflow_name: String,
) -> Result<(), String> {
    let system_manager = SystemManager::new(app_handle);
    let notification = crate::system::notifications::ai_workflow_generated(&workflow_name);
    system_manager.show_notification(notification).await
}

// System tray menu actions
#[tauri::command]
pub fn handle_tray_menu_action(
    app_handle: AppHandle,
    action: String,
) -> Result<(), String> {
    match action.as_str() {
        "show" => restore_from_tray(app_handle),
        "hide" => minimize_to_tray(app_handle),
        "quit" => quit_app(app_handle),
        _ => Err(format!("Unknown tray menu action: {}", action)),
    }
}

// Performance monitoring
#[tauri::command]
pub async fn get_performance_metrics(app_handle: AppHandle) -> Result<HashMap<String, serde_json::Value>, String> {
    let mut metrics = HashMap::new();
    
    // Memory usage (approximate)
    #[cfg(target_os = "windows")]
    {
        use std::process::Command;
        if let Ok(output) = Command::new("tasklist")
            .args(&["/FI", &format!("PID eq {}", std::process::id()), "/FO", "CSV"])
            .output()
        {
            if let Ok(output_str) = String::from_utf8(output.stdout) {
                // Parse memory usage from tasklist output
                if let Some(line) = output_str.lines().nth(1) {
                    let parts: Vec<&str> = line.split(',').collect();
                    if parts.len() > 4 {
                        metrics.insert("memory_usage".to_string(), serde_json::Value::String(parts[4].trim_matches('"').to_string()));
                    }
                }
            }
        }
    }

    #[cfg(target_os = "macos")]
    {
        use std::process::Command;
        if let Ok(output) = Command::new("ps")
            .args(&["-o", "rss=", "-p", &std::process::id().to_string()])
            .output()
        {
            if let Ok(output_str) = String::from_utf8(output.stdout) {
                if let Ok(memory_kb) = output_str.trim().parse::<u64>() {
                    metrics.insert("memory_usage_kb".to_string(), serde_json::Value::Number(memory_kb.into()));
                    metrics.insert("memory_usage_mb".to_string(), serde_json::Value::Number((memory_kb / 1024).into()));
                }
            }
        }
    }

    #[cfg(target_os = "linux")]
    {
        use std::fs;
        if let Ok(status) = fs::read_to_string(format!("/proc/{}/status", std::process::id())) {
            for line in status.lines() {
                if line.starts_with("VmRSS:") {
                    if let Some(memory_str) = line.split_whitespace().nth(1) {
                        if let Ok(memory_kb) = memory_str.parse::<u64>() {
                            metrics.insert("memory_usage_kb".to_string(), serde_json::Value::Number(memory_kb.into()));
                            metrics.insert("memory_usage_mb".to_string(), serde_json::Value::Number((memory_kb / 1024).into()));
                        }
                    }
                    break;
                }
            }
        }
    }

    // CPU usage would require more complex implementation
    metrics.insert("process_id".to_string(), serde_json::Value::Number(std::process::id().into()));
    
    // App uptime
    metrics.insert("platform".to_string(), serde_json::Value::String(std::env::consts::OS.to_string()));
    
    Ok(metrics)
}

// Window management
#[tauri::command]
pub fn set_window_always_on_top(app_handle: AppHandle, always_on_top: bool) -> Result<(), String> {
    if let Some(window) = app_handle.get_webview_window("main") {
        window.set_always_on_top(always_on_top)
            .map_err(|e| format!("Failed to set always on top: {}", e))?;
    }
    Ok(())
}

#[tauri::command]
pub fn center_window(app_handle: AppHandle) -> Result<(), String> {
    if let Some(window) = app_handle.get_webview_window("main") {
        window.center()
            .map_err(|e| format!("Failed to center window: {}", e))?;
    }
    Ok(())
}

#[tauri::command]
pub fn set_window_size(app_handle: AppHandle, width: u32, height: u32) -> Result<(), String> {
    if let Some(window) = app_handle.get_webview_window("main") {
        window.set_size(tauri::LogicalSize::new(width, height))
            .map_err(|e| format!("Failed to set window size: {}", e))?;
    }
    Ok(())
}
