use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>};
use crate::{AppState, models::*, database::repositories::Repository};

#[tauri::command]
pub async fn create_template(
    app_handle: AppHandle,
    create_data: CreateTemplate,
) -> Result<Template, String> {
    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;

    let template = Template::new(create_data);

    repositories
        .templates
        .create(&template)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn get_template(
    app_handle: AppHandle,
    id: String,
) -> Result<Option<Template>, String> {
    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;

    repositories
        .templates
        .get_by_id(&id)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn list_templates(
    app_handle: AppHandle,
    category: Option<String>,
    limit: Option<i64>,
    offset: Option<i64>,
) -> Result<Vec<Template>, String> {
    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;

    if let Some(category) = category {
        repositories
            .templates
            .get_by_category(&category, limit, offset)
            .await
            .map_err(|e| e.to_string())
    } else {
        repositories
            .templates
            .get_public_templates(limit, offset)
            .await
            .map_err(|e| e.to_string())
    }
}

#[tauri::command]
pub async fn use_template(
    app_handle: AppHandle,
    template_id: String,
) -> Result<Template, String> {
    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;

    // Increment usage count
    repositories
        .templates
        .increment_usage(&template_id)
        .await
        .map_err(|e| e.to_string())?;

    // Return the template
    repositories
        .templates
        .get_by_id(&template_id)
        .await
        .map_err(|e| e.to_string())?
        .ok_or("Template not found".to_string())
}

#[tauri::command]
pub async fn deploy_template_to_instance(
    app_handle: AppHandle,
    template_id: String,
    instance_id: i64,
) -> Result<String, String> {
    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;
    let credential_service = &state.credential_service;

    // Get the template
    let template = repositories
        .templates
        .get_by_id(&template_id)
        .await
        .map_err(|e| e.to_string())?
        .ok_or("Template not found")?;

    // Get the N8N instance
    let instance = repositories
        .n8n_instances
        .get_by_id(&instance_id.to_string())
        .await
        .map_err(|e| e.to_string())?
        .ok_or("N8N instance not found")?;

    // Get the API key for the instance
    let credential_id = format!("n8n.{}", instance.name);
    let credential = credential_service
        .get_credential(&credential_id)
        .await
        .map_err(|e| format!("Failed to get N8N API key: {}", e))?
        .ok_or("N8N API key not found for instance")?;

    let api_key = credential.value.expose_secret().to_string();

    // Create N8N API client
    let client = crate::n8n_api::N8nApiClient::new(instance.url.clone(), api_key)
        .map_err(|e| format!("Failed to create N8N API client: {}", e))?;

    // Parse template data
    let nodes: serde_json::Value = serde_json::from_str(&template.nodes)
        .map_err(|e| format!("Failed to parse template nodes: {}", e))?;
    let connections: serde_json::Value = serde_json::from_str(&template.connections)
        .map_err(|e| format!("Failed to parse template connections: {}", e))?;
    let settings: Option<serde_json::Value> = template.settings
        .as_ref()
        .map(|s| serde_json::from_str(s))
        .transpose()
        .map_err(|e| format!("Failed to parse template settings: {}", e))?;
    let static_data: Option<serde_json::Value> = template.static_data
        .as_ref()
        .map(|s| serde_json::from_str(s))
        .transpose()
        .map_err(|e| format!("Failed to parse template static_data: {}", e))?;

    // Create workflow data for N8N API
    let workflow_data = serde_json::json!({
        "name": template.name,
        "nodes": nodes,
        "connections": connections,
        "settings": settings,
        "staticData": static_data,
        "active": false // Start inactive by default
    });

    // Deploy to N8N instance
    let n8n_workflow = client
        .create_workflow(workflow_data)
        .await
        .map_err(|e| format!("Failed to deploy workflow to N8N: {}", e))?;

    // Create workflow record in our database
    let create_workflow_data = crate::models::CreateWorkflow {
        n8n_instance_id: instance_id.to_string(),
        name: template.name.clone(),
        description: template.description.clone(),
        nodes: Some(nodes),
        connections: Some(connections),
        settings,
        static_data,
        tags: template.tags
            .as_ref()
            .map(|t| serde_json::from_str::<Vec<String>>(t).unwrap_or_default()),
    };

    let workflow = crate::models::Workflow::new(create_workflow_data, n8n_workflow.id.clone());

    repositories
        .workflows
        .create(&workflow)
        .await
        .map_err(|e| format!("Failed to save workflow to database: {}", e))?;

    // Increment template usage count
    repositories
        .templates
        .increment_usage(&template_id)
        .await
        .map_err(|e| e.to_string())?;

    Ok(n8n_workflow.id)
}
