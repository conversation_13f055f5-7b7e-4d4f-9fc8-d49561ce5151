use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>};
use crate::{AppState, models::*, database::repositories::Repository};
use serde::{Deserialize, Serialize};

// Frontend-specific workflow structure with properly typed fields
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct FrontendWorkflow {
    pub id: String, // Use string ID to match backend
    pub n8n_instance_id: String,
    pub n8n_workflow_id: String,
    pub name: String,
    pub is_active: bool,
    pub tags: Vec<String>, // Properly typed as array
    pub nodes: Option<serde_json::Value>,
    pub connections: Option<serde_json::Value>,
    pub last_sync_at: String,
    pub n8n_instance: Option<FrontendN8nInstance>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct FrontendN8nInstance {
    pub id: String,
    pub name: String,
    pub health_status: String,
    pub url: String,
}

impl From<&Workflow> for FrontendWorkflow {
    fn from(workflow: &Workflow) -> Self {
        Self {
            id: workflow.id.clone(), // Keep string ID as-is
            n8n_instance_id: workflow.n8n_instance_id.clone(),
            n8n_workflow_id: workflow.n8n_workflow_id.clone(),
            name: workflow.name.clone(),
            is_active: workflow.is_active,
            tags: workflow.get_tags(), // Use the existing method that handles JSON parsing
            nodes: workflow.get_nodes(),
            connections: workflow.get_connections(),
            last_sync_at: workflow.updated_at.to_rfc3339(),
            n8n_instance: None, // Will be populated separately if needed
        }
    }
}

#[tauri::command]
pub async fn create_workflow(
    app_handle: AppHandle,
    create_data: CreateWorkflow,
) -> Result<Workflow, String> {
    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;

    // Create workflow directly in database for now
    // TODO: Integrate with N8N API when ready
    let workflow = Workflow::new(create_data, "temp_n8n_id".to_string());

    repositories
        .workflows
        .create(&workflow)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn get_workflow(
    app_handle: AppHandle,
    id: String,
) -> Result<Option<Workflow>, String> {
    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;

    repositories
        .workflows
        .get_by_id(&id)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn list_workflows(
    app_handle: AppHandle,
    instance_id: Option<String>,
    limit: Option<i64>,
    offset: Option<i64>,
) -> Result<Vec<FrontendWorkflow>, String> {
    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;

    let workflows = if let Some(instance_id) = instance_id {
        repositories
            .workflows
            .get_by_instance(&instance_id)
            .await
            .map_err(|e| e.to_string())?
    } else {
        repositories
            .workflows
            .list(limit, offset)
            .await
            .map_err(|e| e.to_string())?
    };

    // Transform to frontend-specific structure and populate instance information
    let mut frontend_workflows: Vec<FrontendWorkflow> = Vec::new();

    for workflow in workflows {
        let mut frontend_workflow = FrontendWorkflow::from(&workflow);

        // Fetch and populate instance information
        if let Ok(Some(instance)) = repositories
            .n8n_instances
            .get_by_id(&workflow.n8n_instance_id)
            .await
        {
            frontend_workflow.n8n_instance = Some(FrontendN8nInstance {
                id: instance.id,
                name: instance.name,
                health_status: instance.status,
                url: instance.url,
            });
        }

        frontend_workflows.push(frontend_workflow);
    }

    Ok(frontend_workflows)
}

#[tauri::command]
pub async fn update_workflow(
    app_handle: AppHandle,
    id: String,
    update_data: UpdateWorkflow,
) -> Result<Workflow, String> {
    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;

    // Get existing workflow first
    let mut workflow = repositories
        .workflows
        .get_by_id(&id)
        .await
        .map_err(|e| e.to_string())?
        .ok_or("Workflow not found")?;

    // Apply updates
    if let Some(name) = update_data.name {
        workflow.name = name;
    }
    if let Some(description) = update_data.description {
        workflow.description = Some(description);
    }
    if let Some(status) = update_data.status {
        workflow.status = String::from(status);
    }
    if let Some(is_active) = update_data.is_active {
        workflow.is_active = is_active;
    }
    if let Some(nodes) = update_data.nodes {
        workflow.nodes = Some(serde_json::to_string(&nodes).unwrap_or_default());
    }
    if let Some(connections) = update_data.connections {
        workflow.connections = Some(serde_json::to_string(&connections).unwrap_or_default());
    }
    if let Some(settings) = update_data.settings {
        workflow.settings = Some(serde_json::to_string(&settings).unwrap_or_default());
    }
    if let Some(static_data) = update_data.static_data {
        workflow.static_data = Some(serde_json::to_string(&static_data).unwrap_or_default());
    }
    if let Some(tags) = update_data.tags {
        workflow.tags = Some(serde_json::to_string(&tags).unwrap_or_default());
    }

    workflow.updated_at = chrono::Utc::now();

    // Update workflow using Repository trait
    repositories
        .workflows
        .update(&workflow)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn delete_workflow(
    app_handle: AppHandle,
    id: String,
) -> Result<bool, String> {
    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;

    // Delete workflow directly from database for now
    // TODO: Integrate with N8N API when ready
    repositories
        .workflows
        .delete(&id)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn toggle_workflow(
    app_handle: AppHandle,
    workflow_id: String,
    is_active: bool,
) -> Result<Workflow, String> {
    let state = app_handle.state::<AppState>();
    let repositories = &state.repositories;

    // Get the existing workflow
    let mut workflow = repositories
        .workflows
        .get_by_id(&workflow_id)
        .await
        .map_err(|e| e.to_string())?
        .ok_or("Workflow not found")?;

    // Update the is_active field
    workflow.is_active = is_active;

    // Save the updated workflow
    repositories
        .workflows
        .update(&workflow)
        .await
        .map_err(|e| e.to_string())
}
