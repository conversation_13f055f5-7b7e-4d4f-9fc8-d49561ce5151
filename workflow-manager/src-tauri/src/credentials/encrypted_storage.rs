use super::{CredentialManager, CredentialMetadata, CredentialType, SecureCredential, SecretString, CredentialError, CredentialResult};
use aes_gcm::{
    aead::{Aead, KeyInit, OsRng},
    Aes256Gcm, Nonce,
};
use anyhow::Result;
use async_trait::async_trait;
use base64::{engine::general_purpose, Engine as _};
use rand::RngCore;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;
use tokio::fs;
use zeroize::{Zeroize, ZeroizeOnDrop};

#[derive(Debug, Serialize, Deserialize)]
struct EncryptedCredentialData {
    pub metadata: CredentialMetadata,
    pub encrypted_value: String,
    pub nonce: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct CredentialStore {
    pub version: u32,
    pub credentials: HashMap<String, EncryptedCredentialData>,
}

struct EncryptionKey {
    key: [u8; 32],
}

impl EncryptionKey {
    fn new() -> Self {
        let mut key = [0u8; 32];
        OsRng.fill_bytes(&mut key);
        Self { key }
    }

    fn from_bytes(bytes: &[u8]) -> CredentialResult<Self> {
        if bytes.len() != 32 {
            return Err(CredentialError::EncryptionFailed {
                message: "Invalid key length".to_string(),
            });
        }
        let mut key = [0u8; 32];
        key.copy_from_slice(bytes);
        Ok(Self { key })
    }

    fn as_bytes(&self) -> &[u8] {
        &self.key
    }
}

impl Drop for EncryptionKey {
    fn drop(&mut self) {
        use zeroize::Zeroize;
        self.key.zeroize();
    }
}

pub struct EncryptedFileStorage {
    storage_path: PathBuf,
    encryption_key: EncryptionKey,
}

impl EncryptedFileStorage {
    pub async fn new(storage_dir: PathBuf) -> Result<Self> {
        // Ensure storage directory exists
        fs::create_dir_all(&storage_dir).await?;

        let key_path = storage_dir.join("encryption.key");
        let storage_path = storage_dir.join("credentials.enc");

        // Load or generate encryption key
        let encryption_key = if key_path.exists() {
            let key_data = fs::read(&key_path).await?;
            EncryptionKey::from_bytes(&key_data)
                .map_err(|e| anyhow::anyhow!("Failed to load encryption key: {}", e))?
        } else {
            let key = EncryptionKey::new();
            fs::write(&key_path, key.as_bytes()).await?;
            key
        };

        Ok(Self {
            storage_path,
            encryption_key,
        })
    }

    async fn load_store(&self) -> Result<CredentialStore> {
        if !self.storage_path.exists() {
            return Ok(CredentialStore {
                version: 1,
                credentials: HashMap::new(),
            });
        }

        let encrypted_data = fs::read(&self.storage_path).await?;
        let decrypted_data = self.decrypt_data(&encrypted_data)?;
        let store: CredentialStore = serde_json::from_slice(&decrypted_data)?;
        Ok(store)
    }

    async fn save_store(&self, store: &CredentialStore) -> Result<()> {
        let json_data = serde_json::to_vec(store)?;
        let encrypted_data = self.encrypt_data(&json_data)?;
        fs::write(&self.storage_path, encrypted_data).await?;
        Ok(())
    }

    fn encrypt_data(&self, data: &[u8]) -> Result<Vec<u8>> {
        let cipher = Aes256Gcm::new_from_slice(self.encryption_key.as_bytes())
            .map_err(|e| anyhow::anyhow!("Failed to create cipher: {}", e))?;

        let mut nonce_bytes = [0u8; 12];
        OsRng.fill_bytes(&mut nonce_bytes);
        let nonce = Nonce::from_slice(&nonce_bytes);

        let ciphertext = cipher
            .encrypt(nonce, data)
            .map_err(|e| anyhow::anyhow!("Encryption failed: {}", e))?;

        // Prepend nonce to ciphertext
        let mut result = nonce_bytes.to_vec();
        result.extend_from_slice(&ciphertext);
        Ok(result)
    }

    fn decrypt_data(&self, encrypted_data: &[u8]) -> Result<Vec<u8>> {
        if encrypted_data.len() < 12 {
            return Err(anyhow::anyhow!("Invalid encrypted data length"));
        }

        let (nonce_bytes, ciphertext) = encrypted_data.split_at(12);
        let nonce = Nonce::from_slice(nonce_bytes);

        let cipher = Aes256Gcm::new_from_slice(self.encryption_key.as_bytes())
            .map_err(|e| anyhow::anyhow!("Failed to create cipher: {}", e))?;

        let plaintext = cipher
            .decrypt(nonce, ciphertext)
            .map_err(|e| anyhow::anyhow!("Decryption failed: {}", e))?;

        Ok(plaintext)
    }

    fn encrypt_credential_value(&self, value: &str) -> CredentialResult<(String, String)> {
        let cipher = Aes256Gcm::new_from_slice(self.encryption_key.as_bytes())
            .map_err(|e| CredentialError::EncryptionFailed {
                message: format!("Failed to create cipher: {}", e),
            })?;

        let mut nonce_bytes = [0u8; 12];
        OsRng.fill_bytes(&mut nonce_bytes);
        let nonce = Nonce::from_slice(&nonce_bytes);

        let ciphertext = cipher
            .encrypt(nonce, value.as_bytes())
            .map_err(|e| CredentialError::EncryptionFailed {
                message: format!("Encryption failed: {}", e),
            })?;

        let encrypted_value = general_purpose::STANDARD.encode(&ciphertext);
        let nonce_str = general_purpose::STANDARD.encode(&nonce_bytes);

        Ok((encrypted_value, nonce_str))
    }

    fn decrypt_credential_value(&self, encrypted_value: &str, nonce_str: &str) -> CredentialResult<String> {
        let cipher = Aes256Gcm::new_from_slice(self.encryption_key.as_bytes())
            .map_err(|e| CredentialError::EncryptionFailed {
                message: format!("Failed to create cipher: {}", e),
            })?;

        let ciphertext = general_purpose::STANDARD
            .decode(encrypted_value)
            .map_err(|e| CredentialError::InvalidFormat)?;

        let nonce_bytes = general_purpose::STANDARD
            .decode(nonce_str)
            .map_err(|e| CredentialError::InvalidFormat)?;

        let nonce = Nonce::from_slice(&nonce_bytes);

        let plaintext = cipher
            .decrypt(&nonce, ciphertext.as_slice())
            .map_err(|e| CredentialError::EncryptionFailed {
                message: format!("Decryption failed: {}", e),
            })?;

        String::from_utf8(plaintext).map_err(|_| CredentialError::InvalidFormat)
    }
}

#[async_trait]
impl CredentialManager for EncryptedFileStorage {
    async fn store_credential(
        &self,
        id: &str,
        credential_type: CredentialType,
        name: &str,
        value: &str,
        description: Option<&str>,
    ) -> Result<()> {
        let mut store = self.load_store().await?;

        let (encrypted_value, nonce) = self.encrypt_credential_value(value)
            .map_err(|e| anyhow::anyhow!("Failed to encrypt credential: {}", e))?;

        let metadata = CredentialMetadata {
            id: id.to_string(),
            credential_type,
            name: name.to_string(),
            description: description.map(|s| s.to_string()),
            created_at: chrono::Utc::now(),
            last_used: None,
            is_valid: true,
        };

        let encrypted_data = EncryptedCredentialData {
            metadata,
            encrypted_value,
            nonce,
        };

        store.credentials.insert(id.to_string(), encrypted_data);
        self.save_store(&store).await?;

        Ok(())
    }

    async fn retrieve_credential(&self, id: &str) -> Result<Option<SecureCredential>> {
        let store = self.load_store().await?;

        if let Some(encrypted_data) = store.credentials.get(id) {
            let value = self.decrypt_credential_value(&encrypted_data.encrypted_value, &encrypted_data.nonce)
                .map_err(|e| anyhow::anyhow!("Failed to decrypt credential: {}", e))?;

            Ok(Some(SecureCredential {
                metadata: encrypted_data.metadata.clone(),
                value: SecretString::new(value),
            }))
        } else {
            Ok(None)
        }
    }

    async fn delete_credential(&self, id: &str) -> Result<bool> {
        let mut store = self.load_store().await?;
        let removed = store.credentials.remove(id).is_some();
        
        if removed {
            self.save_store(&store).await?;
        }
        
        Ok(removed)
    }

    async fn list_credentials(&self) -> Result<Vec<CredentialMetadata>> {
        let store = self.load_store().await?;
        Ok(store.credentials.values().map(|data| data.metadata.clone()).collect())
    }

    async fn update_credential(&self, id: &str, value: &str) -> Result<()> {
        let mut store = self.load_store().await?;

        if let Some(encrypted_data) = store.credentials.get_mut(id) {
            let (encrypted_value, nonce) = self.encrypt_credential_value(value)
                .map_err(|e| anyhow::anyhow!("Failed to encrypt credential: {}", e))?;

            encrypted_data.encrypted_value = encrypted_value;
            encrypted_data.nonce = nonce;
            encrypted_data.metadata.last_used = Some(chrono::Utc::now());

            self.save_store(&store).await?;
            Ok(())
        } else {
            Err(anyhow::anyhow!("Credential not found: {}", id))
        }
    }

    async fn validate_credential(&self, _id: &str) -> Result<bool> {
        // Would implement actual validation logic based on credential type
        Ok(true)
    }

    async fn mark_credential_used(&self, id: &str) -> Result<()> {
        let mut store = self.load_store().await?;

        if let Some(encrypted_data) = store.credentials.get_mut(id) {
            encrypted_data.metadata.last_used = Some(chrono::Utc::now());
            self.save_store(&store).await?;
        }

        Ok(())
    }
}
