#[cfg(target_os = "macos")]
mod macos {
    use super::super::{CredentialManager, CredentialMetadata, CredentialType, SecureCredential, SecretString, CredentialError, CredentialResult};
    use async_trait::async_trait;
    use anyhow::Result;

    pub struct MacOSKeychain {
        service_prefix: String,
    }

    impl MacOSKeychain {
        pub fn new() -> Self {
            Self {
                service_prefix: "workflow-manager".to_string(),
            }
        }

        fn get_service_name(&self, id: &str) -> String {
            format!("{}.{}", self.service_prefix, id)
        }

        fn store_keychain_item(&self, _service: &str, _account: &str, _password: &str) -> CredentialResult<()> {
            // Simplified implementation - would use security-framework in real implementation
            Err(CredentialError::KeychainUnavailable)
        }

        fn retrieve_keychain_item(&self, _service: &str, _account: &str) -> CredentialResult<Option<String>> {
            // Simplified implementation - would use security-framework in real implementation
            Err(CredentialError::KeychainUnavailable)
        }

        fn delete_keychain_item(&self, _service: &str, _account: &str) -> CredentialResult<bool> {
            // Simplified implementation - would use security-framework in real implementation
            Err(CredentialError::KeychainUnavailable)
        }
    }

    #[async_trait]
    impl CredentialManager for MacOSKeychain {
        async fn store_credential(
            &self,
            id: &str,
            credential_type: CredentialType,
            name: &str,
            value: &str,
            description: Option<&str>,
        ) -> Result<()> {
            let service = self.get_service_name(id);
            let account = format!("{:?}", credential_type);
            
            self.store_keychain_item(&service, &account, value)
                .map_err(|e| anyhow::anyhow!("Keychain storage failed: {}", e))?;
            
            Ok(())
        }

        async fn retrieve_credential(&self, id: &str) -> Result<Option<SecureCredential>> {
            // This is a simplified implementation
            // In practice, you'd also need to store and retrieve metadata
            let service = self.get_service_name(id);
            let account = "credential"; // Simplified for now
            
            match self.retrieve_keychain_item(&service, &account) {
                Ok(Some(value)) => {
                    let metadata = CredentialMetadata {
                        id: id.to_string(),
                        credential_type: CredentialType::N8nApiKey, // Would be stored separately
                        name: "Retrieved Credential".to_string(),
                        description: None,
                        created_at: chrono::Utc::now(),
                        last_used: None,
                        is_valid: true,
                    };
                    
                    Ok(Some(SecureCredential {
                        metadata,
                        value: SecretString::new(value),
                    }))
                }
                Ok(None) => Ok(None),
                Err(e) => Err(anyhow::anyhow!("Keychain retrieval failed: {}", e)),
            }
        }

        async fn delete_credential(&self, id: &str) -> Result<bool> {
            let service = self.get_service_name(id);
            let account = "credential";
            
            self.delete_keychain_item(&service, &account)
                .map_err(|e| anyhow::anyhow!("Keychain deletion failed: {}", e))
        }

        async fn list_credentials(&self) -> Result<Vec<CredentialMetadata>> {
            // Implementation would search keychain for all items with our service prefix
            // This is a placeholder
            Ok(vec![])
        }

        async fn update_credential(&self, id: &str, value: &str) -> Result<()> {
            // Delete and re-add
            self.delete_credential(id).await?;
            self.store_credential(id, CredentialType::N8nApiKey, "Updated", value, None).await
        }

        async fn validate_credential(&self, _id: &str) -> Result<bool> {
            // Would implement actual validation logic
            Ok(true)
        }

        async fn mark_credential_used(&self, _id: &str) -> Result<()> {
            // Would update last_used timestamp
            Ok(())
        }
    }
}

#[cfg(target_os = "windows")]
mod windows {
    use super::super::{CredentialManager, CredentialMetadata, CredentialType, SecureCredential, SecretString, CredentialError, CredentialResult};
    use async_trait::async_trait;
    use anyhow::Result;
    use std::ffi::{CString, OsStr};
    use std::os::windows::ffi::OsStrExt;
    use std::ptr;
    use winapi::um::wincred::{
        CredDeleteW, CredReadW, CredWriteW, CREDENTIALW, CRED_PERSIST_LOCAL_MACHINE,
        CRED_TYPE_GENERIC, PCREDENTIALW,
    };
    use winapi::um::winbase::lstrlenW;

    pub struct WindowsCredentialManager {
        target_prefix: String,
    }

    impl WindowsCredentialManager {
        pub fn new() -> Self {
            Self {
                target_prefix: "workflow-manager".to_string(),
            }
        }

        fn get_target_name(&self, id: &str) -> Vec<u16> {
            let target = format!("{}.{}", self.target_prefix, id);
            OsStr::new(&target).encode_wide().chain(std::iter::once(0)).collect()
        }

        fn store_credential_windows(&self, target: &[u16], username: &str, password: &str) -> CredentialResult<()> {
            let username_wide: Vec<u16> = OsStr::new(username).encode_wide().chain(std::iter::once(0)).collect();
            let password_bytes = password.as_bytes();

            let mut credential = CREDENTIALW {
                Flags: 0,
                Type: CRED_TYPE_GENERIC,
                TargetName: target.as_ptr() as *mut u16,
                Comment: ptr::null_mut(),
                LastWritten: unsafe { std::mem::zeroed() },
                CredentialBlobSize: password_bytes.len() as u32,
                CredentialBlob: password_bytes.as_ptr() as *mut u8,
                Persist: CRED_PERSIST_LOCAL_MACHINE,
                AttributeCount: 0,
                Attributes: ptr::null_mut(),
                TargetAlias: ptr::null_mut(),
                UserName: username_wide.as_ptr() as *mut u16,
            };

            unsafe {
                if CredWriteW(&mut credential, 0) == 0 {
                    return Err(CredentialError::StorageError {
                        message: "Failed to write credential to Windows Credential Manager".to_string(),
                    });
                }
            }

            Ok(())
        }

        fn retrieve_credential_windows(&self, target: &[u16]) -> CredentialResult<Option<String>> {
            let mut credential: PCREDENTIALW = ptr::null_mut();

            unsafe {
                if CredReadW(target.as_ptr(), CRED_TYPE_GENERIC, 0, &mut credential) == 0 {
                    return Ok(None);
                }

                let password_slice = std::slice::from_raw_parts(
                    (*credential).CredentialBlob,
                    (*credential).CredentialBlobSize as usize,
                );

                let password = String::from_utf8(password_slice.to_vec())
                    .map_err(|_| CredentialError::InvalidFormat)?;

                // Free the credential
                winapi::um::wincred::CredFree(credential as *mut _);

                Ok(Some(password))
            }
        }

        fn delete_credential_windows(&self, target: &[u16]) -> CredentialResult<bool> {
            unsafe {
                if CredDeleteW(target.as_ptr(), CRED_TYPE_GENERIC, 0) == 0 {
                    Ok(false)
                } else {
                    Ok(true)
                }
            }
        }
    }

    #[async_trait]
    impl CredentialManager for WindowsCredentialManager {
        async fn store_credential(
            &self,
            id: &str,
            credential_type: CredentialType,
            name: &str,
            value: &str,
            description: Option<&str>,
        ) -> Result<()> {
            let target = self.get_target_name(id);
            let username = format!("{:?}", credential_type);
            
            self.store_credential_windows(&target, &username, value)
                .map_err(|e| anyhow::anyhow!("Windows credential storage failed: {}", e))?;
            
            Ok(())
        }

        async fn retrieve_credential(&self, id: &str) -> Result<Option<SecureCredential>> {
            let target = self.get_target_name(id);
            
            match self.retrieve_credential_windows(&target) {
                Ok(Some(value)) => {
                    let metadata = CredentialMetadata {
                        id: id.to_string(),
                        credential_type: CredentialType::N8nApiKey,
                        name: "Retrieved Credential".to_string(),
                        description: None,
                        created_at: chrono::Utc::now(),
                        last_used: None,
                        is_valid: true,
                    };
                    
                    Ok(Some(SecureCredential {
                        metadata,
                        value: SecretString::new(value),
                    }))
                }
                Ok(None) => Ok(None),
                Err(e) => Err(anyhow::anyhow!("Windows credential retrieval failed: {}", e)),
            }
        }

        async fn delete_credential(&self, id: &str) -> Result<bool> {
            let target = self.get_target_name(id);
            
            self.delete_credential_windows(&target)
                .map_err(|e| anyhow::anyhow!("Windows credential deletion failed: {}", e))
        }

        async fn list_credentials(&self) -> Result<Vec<CredentialMetadata>> {
            Ok(vec![])
        }

        async fn update_credential(&self, id: &str, value: &str) -> Result<()> {
            self.delete_credential(id).await?;
            self.store_credential(id, CredentialType::N8nApiKey, "Updated", value, None).await
        }

        async fn validate_credential(&self, _id: &str) -> Result<bool> {
            Ok(true)
        }

        async fn mark_credential_used(&self, _id: &str) -> Result<()> {
            Ok(())
        }
    }
}

// Re-export platform-specific implementations
#[cfg(target_os = "macos")]
pub use macos::MacOSKeychain as PlatformKeychain;

#[cfg(target_os = "windows")]
pub use windows::WindowsCredentialManager as PlatformKeychain;

#[cfg(not(any(target_os = "macos", target_os = "windows")))]
pub struct PlatformKeychain;

#[cfg(not(any(target_os = "macos", target_os = "windows")))]
impl PlatformKeychain {
    pub fn new() -> Self {
        Self
    }
}
