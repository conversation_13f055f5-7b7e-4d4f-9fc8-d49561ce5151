use anyhow::Result;
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use zeroize::{Zeroize, ZeroizeOnDrop};

pub mod keychain;
pub mod encrypted_storage;
pub mod service;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum CredentialType {
    N8n<PERSON><PERSON><PERSON>ey,
    OpenAiApi<PERSON>ey,
    ClaudeApi<PERSON>ey,
    DeepSeekApiKey,
    AimlApiKey,
    LocalModelConfig,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CredentialMetadata {
    pub id: String,
    pub credential_type: CredentialType,
    pub name: String,
    pub description: Option<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub last_used: Option<chrono::DateTime<chrono::Utc>>,
    pub is_valid: bool,
}

#[derive(Debug, <PERSON><PERSON>)]
pub struct SecureCredential {
    pub metadata: CredentialMetadata,
    pub value: SecretString,
}

impl SecureCredential {
    pub fn new(
        id: String,
        credential_type: CredentialType,
        name: String,
        description: Option<String>,
        value: String,
    ) -> Self {
        let now = chrono::Utc::now();
        Self {
            metadata: CredentialMetadata {
                id,
                credential_type,
                name,
                description,
                created_at: now,
                last_used: None,
                is_valid: true,
            },
            value: SecretString::new(value),
        }
    }
}

#[derive(Debug, Clone)]
pub struct SecretString {
    inner: String,
}

impl SecretString {
    pub fn new(value: String) -> Self {
        Self { inner: value }
    }

    pub fn expose_secret(&self) -> &str {
        &self.inner
    }
}

impl Drop for SecretString {
    fn drop(&mut self) {
        use zeroize::Zeroize;
        self.inner.zeroize();
    }
}

#[async_trait]
pub trait CredentialManager: Send + Sync {
    async fn store_credential(
        &self,
        id: &str,
        credential_type: CredentialType,
        name: &str,
        value: &str,
        description: Option<&str>,
    ) -> Result<()>;

    async fn retrieve_credential(&self, id: &str) -> Result<Option<SecureCredential>>;

    async fn delete_credential(&self, id: &str) -> Result<bool>;

    async fn list_credentials(&self) -> Result<Vec<CredentialMetadata>>;

    async fn update_credential(&self, id: &str, value: &str) -> Result<()>;

    async fn validate_credential(&self, id: &str) -> Result<bool>;

    async fn mark_credential_used(&self, id: &str) -> Result<()>;
}

#[derive(Debug, thiserror::Error)]
pub enum CredentialError {
    #[error("Credential not found: {id}")]
    NotFound { id: String },

    #[error("Keychain access denied")]
    AccessDenied,

    #[error("Keychain unavailable, using fallback storage")]
    KeychainUnavailable,

    #[error("Invalid credential format")]
    InvalidFormat,

    #[error("Encryption failed: {message}")]
    EncryptionFailed { message: String },

    #[error("Validation failed for credential: {id}")]
    ValidationFailed { id: String },

    #[error("Storage error: {message}")]
    StorageError { message: String },
}

pub type CredentialResult<T> = std::result::Result<T, CredentialError>;
