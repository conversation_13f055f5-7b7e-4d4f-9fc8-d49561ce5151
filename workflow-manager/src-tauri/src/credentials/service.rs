use super::{
    encrypted_storage::EncryptedFileStorage,
    keychain::PlatformKeychain,
    CredentialManager, CredentialMetadata, CredentialType, SecureCredential, CredentialError, CredentialResult
};
use anyhow::Result;
use async_trait::async_trait;
use std::path::PathBuf;
use std::sync::Arc;
use tokio::sync::RwLock;

pub enum StorageBackend {
    Keychain(PlatformKeychain),
    EncryptedFile(EncryptedFileStorage),
}

pub struct CredentialService {
    primary_backend: Arc<RwLock<StorageBackend>>,
    fallback_backend: Option<Arc<RwLock<EncryptedFileStorage>>>,
    use_fallback: Arc<RwLock<bool>>,
}

impl CredentialService {
    pub async fn new(storage_dir: PathBuf) -> Result<Self> {
        // Try to initialize keychain first
        let (primary_backend, fallback_backend, use_fallback) = match Self::try_keychain_init().await {
            Ok(keychain) => {
                // Keychain available, set up encrypted storage as fallback
                let encrypted_storage = EncryptedFileStorage::new(storage_dir).await?;
                (
                    Arc::new(RwLock::new(StorageBackend::Keychain(keychain))),
                    Some(Arc::new(RwLock::new(encrypted_storage))),
                    Arc::new(RwLock::new(false)),
                )
            }
            Err(_) => {
                // Keychain not available, use encrypted storage as primary
                let encrypted_storage = EncryptedFileStorage::new(storage_dir).await?;
                (
                    Arc::new(RwLock::new(StorageBackend::EncryptedFile(encrypted_storage))),
                    None,
                    Arc::new(RwLock::new(false)),
                )
            }
        };

        Ok(Self {
            primary_backend,
            fallback_backend,
            use_fallback,
        })
    }

    async fn try_keychain_init() -> Result<PlatformKeychain> {
        // Test keychain availability by attempting a simple operation
        let keychain = PlatformKeychain::new();
        
        // Try to store and retrieve a test credential
        let test_id = "workflow-manager-test";
        let test_value = "test-value";
        
        match keychain.store_credential(
            test_id,
            CredentialType::N8nApiKey,
            "Test",
            test_value,
            Some("Test credential for keychain availability"),
        ).await {
            Ok(_) => {
                // Clean up test credential
                let _ = keychain.delete_credential(test_id).await;
                Ok(keychain)
            }
            Err(_) => Err(anyhow::anyhow!("Keychain not available")),
        }
    }



    pub async fn is_keychain_available(&self) -> bool {
        matches!(&*self.primary_backend.read().await, StorageBackend::Keychain(_)) 
            && !*self.use_fallback.read().await
    }

    pub async fn switch_to_fallback(&self) -> Result<()> {
        if self.fallback_backend.is_some() {
            *self.use_fallback.write().await = true;
            Ok(())
        } else {
            Err(anyhow::anyhow!("No fallback backend available"))
        }
    }

    pub async fn switch_to_primary(&self) -> Result<()> {
        // Test if primary backend is available
        match &*self.primary_backend.read().await {
            StorageBackend::Keychain(_) => {
                // Test keychain availability
                if Self::try_keychain_init().await.is_ok() {
                    *self.use_fallback.write().await = false;
                    Ok(())
                } else {
                    Err(anyhow::anyhow!("Primary backend (keychain) not available"))
                }
            }
            StorageBackend::EncryptedFile(_) => {
                *self.use_fallback.write().await = false;
                Ok(())
            }
        }
    }

    // Get a credential by ID
    pub async fn get_credential(&self, credential_id: &str) -> Result<Option<SecureCredential>> {
        let use_fallback = *self.use_fallback.read().await;

        if !use_fallback {
            match &*self.primary_backend.read().await {
                StorageBackend::Keychain(keychain) => {
                    match keychain.retrieve_credential(credential_id).await {
                        Ok(result) => return Ok(result),
                        Err(_) => {
                            if self.fallback_backend.is_none() {
                                return Ok(None);
                            }
                        }
                    }
                }
                StorageBackend::EncryptedFile(storage) => {
                    return storage.retrieve_credential(credential_id).await;
                }
            }
        }

        // Use fallback backend
        if let Some(fallback) = &self.fallback_backend {
            fallback.read().await.retrieve_credential(credential_id).await
        } else {
            Ok(None)
        }
    }

    // Store a credential
    pub async fn store_credential(&self, credential_id: &str, value: &str, credential_type: CredentialType) -> Result<()> {
        let use_fallback = *self.use_fallback.read().await;

        if !use_fallback {
            match &*self.primary_backend.read().await {
                StorageBackend::Keychain(keychain) => {
                    match keychain.store_credential(
                        credential_id,
                        credential_type.clone(),
                        credential_id, // name
                        value,
                        None, // description
                    ).await {
                        Ok(result) => return Ok(result),
                        Err(_) => {
                            if self.fallback_backend.is_none() {
                                return Err(anyhow::anyhow!("Failed to store credential"));
                            }
                        }
                    }
                }
                StorageBackend::EncryptedFile(storage) => {
                    return storage.store_credential(
                        credential_id,
                        credential_type.clone(),
                        credential_id, // name
                        value,
                        None, // description
                    ).await;
                }
            }
        }

        // Use fallback backend
        if let Some(fallback) = &self.fallback_backend {
            fallback.write().await.store_credential(
                credential_id,
                credential_type,
                credential_id, // name
                value,
                None, // description
            ).await
        } else {
            Err(anyhow::anyhow!("No backend available"))
        }
    }

    // Helper method to validate credentials based on type
    pub async fn validate_credential_value(&self, credential_type: &CredentialType, value: &str) -> Result<bool> {
        match credential_type {
            CredentialType::N8nApiKey => {
                // Basic format validation for N8N API keys
                Ok(!value.is_empty() && value.len() > 10)
            }
            CredentialType::OpenAiApiKey => {
                // OpenAI API keys typically start with "sk-"
                Ok(value.starts_with("sk-") && value.len() > 20)
            }
            CredentialType::ClaudeApiKey => {
                // Claude API keys typically start with "sk-ant-"
                Ok(value.starts_with("sk-ant-") && value.len() > 20)
            }
            CredentialType::DeepSeekApiKey => {
                // DeepSeek API key validation
                Ok(!value.is_empty() && value.len() > 10)
            }
            CredentialType::AimlApiKey => {
                // AIML API key validation
                Ok(!value.is_empty() && value.len() > 10)
            }
            CredentialType::LocalModelConfig => {
                // Local model config validation (JSON format expected)
                Ok(serde_json::from_str::<serde_json::Value>(value).is_ok())
            }
        }
    }

    // Helper method to test API connectivity
    pub async fn test_credential_connectivity(&self, credential_type: &CredentialType, value: &str) -> Result<bool> {
        match credential_type {
            CredentialType::N8nApiKey => {
                // Would implement actual N8N API test
                Ok(true)
            }
            CredentialType::OpenAiApiKey => {
                // Would implement OpenAI API test
                Ok(true)
            }
            CredentialType::ClaudeApiKey => {
                // Would implement Claude API test
                Ok(true)
            }
            CredentialType::DeepSeekApiKey => {
                // Would implement DeepSeek API test
                Ok(true)
            }
            CredentialType::AimlApiKey => {
                // Would implement AIML API test
                Ok(true)
            }
            CredentialType::LocalModelConfig => {
                // Would test local model availability
                Ok(true)
            }
        }
    }

    // Convenience method to generate credential IDs
    pub fn generate_credential_id(credential_type: &CredentialType, instance_name: &str) -> String {
        match credential_type {
            CredentialType::N8nApiKey => format!("n8n.{}", instance_name),
            CredentialType::OpenAiApiKey => "ai.openai".to_string(),
            CredentialType::ClaudeApiKey => "ai.claude".to_string(),
            CredentialType::DeepSeekApiKey => "ai.deepseek".to_string(),
            CredentialType::AimlApiKey => "ai.aiml".to_string(),
            CredentialType::LocalModelConfig => format!("ai.local.{}", instance_name),
        }
    }
}

// For now, we'll implement a simplified version that works with the current architecture
#[async_trait]
impl CredentialManager for CredentialService {
    async fn store_credential(
        &self,
        id: &str,
        credential_type: CredentialType,
        name: &str,
        value: &str,
        description: Option<&str>,
    ) -> Result<()> {
        // Validate credential format
        if !self.validate_credential_value(&credential_type, value).await? {
            return Err(anyhow::anyhow!("Invalid credential format"));
        }

        // Try primary backend first
        let use_fallback = *self.use_fallback.read().await;

        if !use_fallback {
            match &*self.primary_backend.read().await {
                StorageBackend::Keychain(keychain) => {
                    match keychain.store_credential(id, credential_type.clone(), name, value, description).await {
                        Ok(_) => return Ok(()),
                        Err(_) => {
                            // Switch to fallback if available
                            if self.fallback_backend.is_some() {
                                *self.use_fallback.write().await = true;
                            } else {
                                return Err(anyhow::anyhow!("Failed to store credential and no fallback available"));
                            }
                        }
                    }
                }
                StorageBackend::EncryptedFile(storage) => {
                    return storage.store_credential(id, credential_type, name, value, description).await;
                }
            }
        }

        // Use fallback backend
        if let Some(fallback) = &self.fallback_backend {
            fallback.read().await.store_credential(id, credential_type, name, value, description).await
        } else {
            Err(anyhow::anyhow!("No available storage backend"))
        }
    }

    async fn retrieve_credential(&self, id: &str) -> Result<Option<SecureCredential>> {
        let use_fallback = *self.use_fallback.read().await;
        
        if !use_fallback {
            match &*self.primary_backend.read().await {
                StorageBackend::Keychain(keychain) => {
                    match keychain.retrieve_credential(id).await {
                        Ok(credential) => return Ok(credential),
                        Err(_) => {
                            // Try fallback if available
                            if self.fallback_backend.is_none() {
                                return Ok(None);
                            }
                        }
                    }
                }
                StorageBackend::EncryptedFile(storage) => {
                    return storage.retrieve_credential(id).await;
                }
            }
        }

        // Use fallback backend
        if let Some(fallback) = &self.fallback_backend {
            fallback.read().await.retrieve_credential(id).await
        } else {
            Ok(None)
        }
    }

    async fn delete_credential(&self, id: &str) -> Result<bool> {
        println!("🔍 CredentialService::delete_credential called with id: {}", id);
        let use_fallback = *self.use_fallback.read().await;
        println!("🔧 Using fallback: {}", use_fallback);

        if !use_fallback {
            match &*self.primary_backend.read().await {
                StorageBackend::Keychain(keychain) => {
                    println!("🔑 Attempting to delete from keychain");
                    match keychain.delete_credential(id).await {
                        Ok(result) => {
                            println!("✅ Keychain delete result: {}", result);
                            return Ok(result);
                        }
                        Err(e) => {
                            println!("❌ Keychain delete failed: {}", e);
                            if self.fallback_backend.is_none() {
                                println!("⚠️ No fallback backend available");
                                return Ok(false);
                            }
                        }
                    }
                }
                StorageBackend::EncryptedFile(storage) => {
                    println!("📁 Attempting to delete from encrypted file");
                    let result = storage.delete_credential(id).await;
                    println!("📁 Encrypted file delete result: {:?}", result);
                    return result;
                }
            }
        }

        // Use fallback backend
        if let Some(fallback) = &self.fallback_backend {
            println!("🔄 Using fallback backend");
            let result = fallback.read().await.delete_credential(id).await;
            println!("🔄 Fallback delete result: {:?}", result);
            result
        } else {
            println!("⚠️ No fallback backend available");
            Ok(false)
        }
    }

    async fn list_credentials(&self) -> Result<Vec<CredentialMetadata>> {
        let use_fallback = *self.use_fallback.read().await;
        
        if !use_fallback {
            match &*self.primary_backend.read().await {
                StorageBackend::Keychain(keychain) => {
                    match keychain.list_credentials().await {
                        Ok(credentials) => return Ok(credentials),
                        Err(_) => {
                            if self.fallback_backend.is_none() {
                                return Ok(vec![]);
                            }
                        }
                    }
                }
                StorageBackend::EncryptedFile(storage) => {
                    return storage.list_credentials().await;
                }
            }
        }

        // Use fallback backend
        if let Some(fallback) = &self.fallback_backend {
            fallback.read().await.list_credentials().await
        } else {
            Ok(vec![])
        }
    }

    async fn update_credential(&self, id: &str, value: &str) -> Result<()> {
        let use_fallback = *self.use_fallback.read().await;
        
        if !use_fallback {
            match &*self.primary_backend.read().await {
                StorageBackend::Keychain(keychain) => {
                    match keychain.update_credential(id, value).await {
                        Ok(_) => return Ok(()),
                        Err(_) => {
                            if self.fallback_backend.is_none() {
                                return Err(anyhow::anyhow!("Failed to update credential"));
                            }
                        }
                    }
                }
                StorageBackend::EncryptedFile(storage) => {
                    return storage.update_credential(id, value).await;
                }
            }
        }

        // Use fallback backend
        if let Some(fallback) = &self.fallback_backend {
            fallback.read().await.update_credential(id, value).await
        } else {
            Err(anyhow::anyhow!("No available storage backend"))
        }
    }

    async fn validate_credential(&self, id: &str) -> Result<bool> {
        if let Some(credential) = self.retrieve_credential(id).await? {
            self.test_credential_connectivity(&credential.metadata.credential_type, credential.value.expose_secret()).await
        } else {
            Ok(false)
        }
    }

    async fn mark_credential_used(&self, id: &str) -> Result<()> {
        let use_fallback = *self.use_fallback.read().await;
        
        if !use_fallback {
            match &*self.primary_backend.read().await {
                StorageBackend::Keychain(keychain) => {
                    match keychain.mark_credential_used(id).await {
                        Ok(_) => return Ok(()),
                        Err(_) => {
                            if self.fallback_backend.is_none() {
                                return Ok(());
                            }
                        }
                    }
                }
                StorageBackend::EncryptedFile(storage) => {
                    return storage.mark_credential_used(id).await;
                }
            }
        }

        // Use fallback backend
        if let Some(fallback) = &self.fallback_backend {
            fallback.read().await.mark_credential_used(id).await
        } else {
            Ok(())
        }
    }
}
