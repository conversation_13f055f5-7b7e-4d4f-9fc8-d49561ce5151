use anyhow::Result;
use sqlx::{Pool, Sqlite};

pub async fn run_migrations(pool: &Pool<Sqlite>) -> Result<()> {
    // Create migrations table if it doesn't exist
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS migrations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            version TEXT NOT NULL UNIQUE,
            applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        "#,
    )
    .execute(pool)
    .await?;

    // Define migrations
    let migrations = vec![
        ("001_initial_schema", create_initial_schema()),
        ("002_add_indexes", create_indexes()),
        ("003_add_app_settings", create_app_settings_table()),
    ];

    // Apply migrations
    for (version, sql) in migrations {
        let exists = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM migrations WHERE version = ?",
        )
        .bind(version)
        .fetch_one(pool)
        .await?;

        if exists == 0 {
            println!("Applying migration: {}", version);
            
            // Execute migration
            sqlx::query(&sql).execute(pool).await?;
            
            // Record migration
            sqlx::query("INSERT INTO migrations (version) VALUES (?)")
                .bind(version)
                .execute(pool)
                .await?;
            
            println!("Migration {} applied successfully", version);
        }
    }

    Ok(())
}

fn create_initial_schema() -> String {
    r#"
    -- N8N Instances table
    CREATE TABLE n8n_instances (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        url TEXT NOT NULL,
        api_key TEXT, -- Will be NULL, actual key stored in keychain
        status TEXT NOT NULL DEFAULT 'inactive',
        version TEXT,
        description TEXT,
        is_default BOOLEAN NOT NULL DEFAULT FALSE,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        last_ping DATETIME
    );

    -- Workflows table
    CREATE TABLE workflows (
        id TEXT PRIMARY KEY,
        n8n_instance_id TEXT NOT NULL,
        n8n_workflow_id TEXT NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        status TEXT NOT NULL DEFAULT 'inactive',
        is_active BOOLEAN NOT NULL DEFAULT FALSE,
        tags TEXT, -- JSON array
        nodes TEXT, -- JSON
        connections TEXT, -- JSON
        settings TEXT, -- JSON
        static_data TEXT, -- JSON
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        last_execution DATETIME,
        execution_count INTEGER NOT NULL DEFAULT 0,
        FOREIGN KEY (n8n_instance_id) REFERENCES n8n_instances(id) ON DELETE CASCADE
    );

    -- Executions table
    CREATE TABLE executions (
        id TEXT PRIMARY KEY,
        workflow_id TEXT NOT NULL,
        n8n_execution_id TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'running',
        mode TEXT NOT NULL,
        started_at DATETIME NOT NULL,
        finished_at DATETIME,
        duration INTEGER, -- milliseconds
        data TEXT, -- JSON
        error TEXT,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (workflow_id) REFERENCES workflows(id) ON DELETE CASCADE
    );

    -- Templates table
    CREATE TABLE templates (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        category TEXT NOT NULL,
        tags TEXT, -- JSON array
        nodes TEXT NOT NULL, -- JSON
        connections TEXT NOT NULL, -- JSON
        settings TEXT, -- JSON
        static_data TEXT, -- JSON
        variables TEXT, -- JSON for template variables
        is_public BOOLEAN NOT NULL DEFAULT FALSE,
        usage_count INTEGER NOT NULL DEFAULT 0,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        created_by TEXT -- AI model or user identifier
    );

    -- AI Configurations table
    CREATE TABLE ai_configs (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        provider TEXT NOT NULL,
        model TEXT NOT NULL,
        api_key TEXT, -- Will be NULL, actual key stored in keychain
        base_url TEXT,
        max_tokens INTEGER,
        temperature REAL,
        system_prompt TEXT,
        is_active BOOLEAN NOT NULL DEFAULT TRUE,
        is_default BOOLEAN NOT NULL DEFAULT FALSE,
        settings TEXT, -- JSON
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        last_used DATETIME,
        usage_count INTEGER NOT NULL DEFAULT 0
    );
    "#.to_string()
}

fn create_indexes() -> String {
    r#"
    -- Indexes for better performance
    CREATE INDEX IF NOT EXISTS idx_workflows_instance_id ON workflows(n8n_instance_id);
    CREATE INDEX IF NOT EXISTS idx_workflows_status ON workflows(status);
    CREATE INDEX IF NOT EXISTS idx_workflows_is_active ON workflows(is_active);
    CREATE INDEX IF NOT EXISTS idx_workflows_created_at ON workflows(created_at);
    
    CREATE INDEX IF NOT EXISTS idx_executions_workflow_id ON executions(workflow_id);
    CREATE INDEX IF NOT EXISTS idx_executions_status ON executions(status);
    CREATE INDEX IF NOT EXISTS idx_executions_started_at ON executions(started_at);
    CREATE INDEX IF NOT EXISTS idx_executions_n8n_execution_id ON executions(n8n_execution_id);
    
    CREATE INDEX IF NOT EXISTS idx_templates_category ON templates(category);
    CREATE INDEX IF NOT EXISTS idx_templates_is_public ON templates(is_public);
    CREATE INDEX IF NOT EXISTS idx_templates_usage_count ON templates(usage_count);
    CREATE INDEX IF NOT EXISTS idx_templates_created_at ON templates(created_at);
    
    CREATE INDEX IF NOT EXISTS idx_ai_configs_provider ON ai_configs(provider);
    CREATE INDEX IF NOT EXISTS idx_ai_configs_is_active ON ai_configs(is_active);
    CREATE INDEX IF NOT EXISTS idx_ai_configs_is_default ON ai_configs(is_default);
    
    CREATE INDEX IF NOT EXISTS idx_n8n_instances_status ON n8n_instances(status);
    CREATE INDEX IF NOT EXISTS idx_n8n_instances_is_default ON n8n_instances(is_default);
    "#.to_string()
}

fn create_app_settings_table() -> String {
    r#"
    CREATE TABLE IF NOT EXISTS app_settings (
        id TEXT PRIMARY KEY,
        auto_sync BOOLEAN NOT NULL DEFAULT TRUE,
        sync_interval INTEGER NOT NULL DEFAULT 30,
        notifications BOOLEAN NOT NULL DEFAULT TRUE,
        max_execution_history INTEGER NOT NULL DEFAULT 1000,
        enable_analytics BOOLEAN NOT NULL DEFAULT FALSE,
        auto_backup BOOLEAN NOT NULL DEFAULT TRUE,
        backup_interval INTEGER NOT NULL DEFAULT 24,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
    );
    "#.to_string()
}
