use anyhow::Result;
use sqlx::{sqlite::SqlitePoolOptions, Pool, Sqlite};
use std::path::Path;
use std::str::FromStr;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>};

pub mod migrations;
pub mod repositories;

pub use repositories::*;

pub type DbPool = Pool<Sqlite>;

pub async fn init_database(app_handle: &AppHandle) -> Result<DbPool> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| anyhow::anyhow!("Failed to get app data dir: {}", e))?;

    println!("App data directory: {:?}", app_dir);

    // Ensure the app data directory exists
    if !app_dir.exists() {
        println!("Creating app data directory: {:?}", app_dir);
        std::fs::create_dir_all(&app_dir)
            .map_err(|e| anyhow::anyhow!("Failed to create app data directory: {}", e))?;
    }

    let db_path = app_dir.join("workflow_manager.db");
    let db_url = format!("sqlite://{}?mode=rwc", db_path.display());

    println!("Database path: {:?}", db_path);
    println!("Database URL: {}", db_url);

    // Create connection pool with SQLite-specific options
    let pool = SqlitePoolOptions::new()
        .max_connections(10)
        .connect_with(
            sqlx::sqlite::SqliteConnectOptions::from_str(&db_url)?
                .create_if_missing(true)
                .journal_mode(sqlx::sqlite::SqliteJournalMode::Wal)
        )
        .await
        .map_err(|e| anyhow::anyhow!("Failed to connect to database: {}", e))?;

    println!("Database connection established successfully");

    // Run migrations
    migrations::run_migrations(&pool).await?;

    println!("Database migrations completed successfully");

    Ok(pool)
}

pub async fn get_database_path(app_handle: &AppHandle) -> Result<String> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| anyhow::anyhow!("Failed to get app data dir: {}", e))?;

    let db_path = app_dir.join("workflow_manager.db");
    Ok(db_path.to_string_lossy().to_string())
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;

    #[tokio::test]
    async fn test_database_creation() {
        let temp_dir = tempdir().unwrap();
        let db_path = temp_dir.path().join("test.db");
        let db_url = format!("sqlite:{}", db_path.display());

        let pool = SqlitePoolOptions::new()
            .max_connections(1)
            .connect(&db_url)
            .await
            .unwrap();

        migrations::run_migrations(&pool).await.unwrap();

        // Test that tables were created
        let result = sqlx::query("SELECT name FROM sqlite_master WHERE type='table'")
            .fetch_all(&pool)
            .await
            .unwrap();

        let table_names: Vec<String> = result
            .iter()
            .map(|row| row.get::<String, _>("name"))
            .collect();

        assert!(table_names.contains(&"n8n_instances".to_string()));
        assert!(table_names.contains(&"workflows".to_string()));
        assert!(table_names.contains(&"executions".to_string()));
        assert!(table_names.contains(&"templates".to_string()));
        assert!(table_names.contains(&"ai_configs".to_string()));
    }
}
