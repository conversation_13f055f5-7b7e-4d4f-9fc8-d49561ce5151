use anyhow::Result;
use sqlx::{Pool, Sqlite};
use crate::models::{AiConfig, CreateAiConfig, UpdateAiConfig, <PERSON><PERSON><PERSON>ider, AiUsageStats};
use super::Repository;

pub struct AiConfigRepository {
    pool: Pool<Sqlite>,
}

impl AiConfigRepository {
    pub fn new(pool: Pool<Sqlite>) -> Self {
        Self { pool }
    }

    pub async fn get_by_provider(&self, provider: AiProvider) -> Result<Vec<AiConfig>> {
        let provider_str = String::from(provider);
        
        let configs = sqlx::query_as::<_, AiConfig>(
            "SELECT * FROM ai_configs WHERE provider = ? ORDER BY is_default DESC, name"
        )
        .bind(provider_str)
        .fetch_all(&self.pool)
        .await?;

        Ok(configs)
    }

    pub async fn get_active_configs(&self) -> Result<Vec<AiConfig>> {
        let configs = sqlx::query_as::<_, AiConfig>(
            "SELECT * FROM ai_configs WHERE is_active = TRUE ORDER BY is_default DESC, name"
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(configs)
    }

    pub async fn get_default(&self) -> Result<Option<AiConfig>> {
        let config = sqlx::query_as::<_, AiConfig>(
            "SELECT * FROM ai_configs WHERE is_default = TRUE LIMIT 1"
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(config)
    }

    pub async fn set_default(&self, id: &str) -> Result<()> {
        // First, unset all defaults
        sqlx::query("UPDATE ai_configs SET is_default = FALSE")
            .execute(&self.pool)
            .await?;

        // Then set the new default
        sqlx::query("UPDATE ai_configs SET is_default = TRUE WHERE id = ?")
            .bind(id)
            .execute(&self.pool)
            .await?;

        Ok(())
    }

    pub async fn increment_usage(&self, id: &str) -> Result<()> {
        sqlx::query(
            "UPDATE ai_configs SET usage_count = usage_count + 1, last_used = ?, updated_at = ? WHERE id = ?"
        )
        .bind(chrono::Utc::now())
        .bind(chrono::Utc::now())
        .bind(id)
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_usage_stats(&self, id: &str) -> Result<AiUsageStats> {
        // For now, return basic stats - this would be implemented with actual usage tracking
        Ok(AiUsageStats {
            total_requests: 0,
            successful_requests: 0,
            failed_requests: 0,
            avg_response_time: None,
            total_tokens_used: None,
            success_rate: 0.0,
        })
    }

    pub async fn get_local_models(&self) -> Result<Vec<AiConfig>> {
        let configs = sqlx::query_as::<_, AiConfig>(
            "SELECT * FROM ai_configs WHERE provider = 'local' ORDER BY name"
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(configs)
    }

    pub async fn toggle_active(&self, id: &str) -> Result<bool> {
        let config = self.get_by_id(id).await?;
        
        if let Some(mut config) = config {
            config.is_active = !config.is_active;
            config.updated_at = chrono::Utc::now();
            
            sqlx::query("UPDATE ai_configs SET is_active = ?, updated_at = ? WHERE id = ?")
                .bind(config.is_active)
                .bind(config.updated_at)
                .bind(id)
                .execute(&self.pool)
                .await?;
            
            Ok(config.is_active)
        } else {
            Err(anyhow::anyhow!("AI config not found"))
        }
    }
}

impl Repository<AiConfig> for AiConfigRepository {
    async fn create(&self, config: &AiConfig) -> Result<AiConfig> {
        sqlx::query(
            r#"
            INSERT INTO ai_configs (
                id, name, provider, model, api_key, base_url, max_tokens,
                temperature, system_prompt, is_active, is_default, settings,
                created_at, updated_at, last_used, usage_count
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#
        )
        .bind(&config.id)
        .bind(&config.name)
        .bind(&config.provider)
        .bind(&config.model)
        .bind(&config.api_key)
        .bind(&config.base_url)
        .bind(config.max_tokens)
        .bind(config.temperature)
        .bind(&config.system_prompt)
        .bind(config.is_active)
        .bind(config.is_default)
        .bind(&config.settings)
        .bind(config.created_at)
        .bind(config.updated_at)
        .bind(config.last_used)
        .bind(config.usage_count)
        .execute(&self.pool)
        .await?;

        Ok(config.clone())
    }

    async fn get_by_id(&self, id: &str) -> Result<Option<AiConfig>> {
        let config = sqlx::query_as::<_, AiConfig>(
            "SELECT * FROM ai_configs WHERE id = ? LIMIT 1"
        )
        .bind(id)
        .fetch_optional(&self.pool)
        .await?;

        Ok(config)
    }

    async fn update(&self, config: &AiConfig) -> Result<AiConfig> {
        sqlx::query(
            r#"
            UPDATE ai_configs SET
                name = ?, model = ?, api_key = ?, base_url = ?, max_tokens = ?,
                temperature = ?, system_prompt = ?, is_active = ?, is_default = ?,
                settings = ?, updated_at = ?
            WHERE id = ?
            "#
        )
        .bind(&config.name)
        .bind(&config.model)
        .bind(&config.api_key)
        .bind(&config.base_url)
        .bind(config.max_tokens)
        .bind(config.temperature)
        .bind(&config.system_prompt)
        .bind(config.is_active)
        .bind(config.is_default)
        .bind(&config.settings)
        .bind(config.updated_at)
        .bind(&config.id)
        .execute(&self.pool)
        .await?;

        Ok(config.clone())
    }

    async fn delete(&self, id: &str) -> Result<bool> {
        let result = sqlx::query("DELETE FROM ai_configs WHERE id = ?")
            .bind(id)
            .execute(&self.pool)
            .await?;

        Ok(result.rows_affected() > 0)
    }

    async fn list(&self, limit: Option<i64>, offset: Option<i64>) -> Result<Vec<AiConfig>> {
        let limit = limit.unwrap_or(100);
        let offset = offset.unwrap_or(0);

        let configs = sqlx::query_as::<_, AiConfig>(
            "SELECT * FROM ai_configs ORDER BY is_default DESC, created_at DESC LIMIT ? OFFSET ?"
        )
        .bind(limit)
        .bind(offset)
        .fetch_all(&self.pool)
        .await?;

        Ok(configs)
    }
}
