use anyhow::Result;
use sqlx::{Pool, Sqlite};
use crate::models::{AppSettings, CreateAppSettings, UpdateAppSettings};
use super::Repository;

pub struct AppSettingsRepository {
    pool: Pool<Sqlite>,
}

impl AppSettingsRepository {
    pub fn new(pool: Pool<Sqlite>) -> Self {
        Self { pool }
    }

    pub async fn get_settings(&self) -> Result<AppSettings> {
        // Try to get existing settings
        if let Some(settings) = self.get_by_id("default").await? {
            Ok(settings)
        } else {
            // Create default settings if none exist
            let default_settings = AppSettings::default();
            self.create(&default_settings).await?;
            Ok(default_settings)
        }
    }

    pub async fn update_settings(&self, update_data: UpdateAppSettings) -> Result<AppSettings> {
        let mut settings = self.get_settings().await?;

        // Apply updates
        if let Some(auto_sync) = update_data.auto_sync {
            settings.auto_sync = auto_sync;
        }
        if let Some(sync_interval) = update_data.sync_interval {
            settings.sync_interval = sync_interval;
        }
        if let Some(notifications) = update_data.notifications {
            settings.notifications = notifications;
        }
        if let Some(max_execution_history) = update_data.max_execution_history {
            settings.max_execution_history = max_execution_history;
        }
        if let Some(enable_analytics) = update_data.enable_analytics {
            settings.enable_analytics = enable_analytics;
        }
        if let Some(auto_backup) = update_data.auto_backup {
            settings.auto_backup = auto_backup;
        }
        if let Some(backup_interval) = update_data.backup_interval {
            settings.backup_interval = backup_interval;
        }

        settings.updated_at = chrono::Utc::now();

        self.update(&settings).await
    }
}

impl Repository<AppSettings> for AppSettingsRepository {
    async fn create(&self, settings: &AppSettings) -> Result<AppSettings> {
        sqlx::query(
            r#"
            INSERT INTO app_settings (
                id, auto_sync, sync_interval, notifications, max_execution_history,
                enable_analytics, auto_backup, backup_interval, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#
        )
        .bind(&settings.id)
        .bind(settings.auto_sync)
        .bind(settings.sync_interval)
        .bind(settings.notifications)
        .bind(settings.max_execution_history)
        .bind(settings.enable_analytics)
        .bind(settings.auto_backup)
        .bind(settings.backup_interval)
        .bind(settings.created_at)
        .bind(settings.updated_at)
        .execute(&self.pool)
        .await?;

        Ok(settings.clone())
    }

    async fn get_by_id(&self, id: &str) -> Result<Option<AppSettings>> {
        let settings = sqlx::query_as::<_, AppSettings>(
            "SELECT * FROM app_settings WHERE id = ? LIMIT 1"
        )
        .bind(id)
        .fetch_optional(&self.pool)
        .await?;

        Ok(settings)
    }

    async fn update(&self, settings: &AppSettings) -> Result<AppSettings> {
        sqlx::query(
            r#"
            UPDATE app_settings SET
                auto_sync = ?, sync_interval = ?, notifications = ?, max_execution_history = ?,
                enable_analytics = ?, auto_backup = ?, backup_interval = ?, updated_at = ?
            WHERE id = ?
            "#
        )
        .bind(settings.auto_sync)
        .bind(settings.sync_interval)
        .bind(settings.notifications)
        .bind(settings.max_execution_history)
        .bind(settings.enable_analytics)
        .bind(settings.auto_backup)
        .bind(settings.backup_interval)
        .bind(settings.updated_at)
        .bind(&settings.id)
        .execute(&self.pool)
        .await?;

        Ok(settings.clone())
    }

    async fn delete(&self, id: &str) -> Result<bool> {
        let result = sqlx::query("DELETE FROM app_settings WHERE id = ?")
            .bind(id)
            .execute(&self.pool)
            .await?;

        Ok(result.rows_affected() > 0)
    }

    async fn list(&self, _limit: Option<i64>, _offset: Option<i64>) -> Result<Vec<AppSettings>> {
        let settings = sqlx::query_as::<_, AppSettings>(
            "SELECT * FROM app_settings ORDER BY created_at DESC"
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(settings)
    }
}
