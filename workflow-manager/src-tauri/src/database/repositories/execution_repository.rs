use anyhow::Result;
use sqlx::{Pool, Sqlite};
use crate::models::{Execution, CreateExecution, UpdateExecution, ExecutionStatus, ExecutionSummary};
use super::Repository;

pub struct ExecutionRepository {
    pool: Pool<Sqlite>,
}

impl ExecutionRepository {
    pub fn new(pool: Pool<Sqlite>) -> Self {
        Self { pool }
    }

    pub async fn get_by_workflow(&self, workflow_id: &str, limit: Option<i64>, offset: Option<i64>) -> Result<Vec<Execution>> {
        let limit = limit.unwrap_or(50);
        let offset = offset.unwrap_or(0);

        let executions = sqlx::query_as::<_, Execution>(
            "SELECT * FROM executions WHERE workflow_id = ? ORDER BY started_at DESC LIMIT ? OFFSET ?"
        )
        .bind(workflow_id)
        .bind(limit)
        .bind(offset)
        .fetch_all(&self.pool)
        .await?;

        Ok(executions)
    }

    pub async fn get_by_status(&self, status: ExecutionStatus, limit: Option<i64>) -> Result<Vec<Execution>> {
        let limit = limit.unwrap_or(100);
        let status_str = String::from(status);

        let executions = sqlx::query_as::<_, Execution>(
            "SELECT * FROM executions WHERE status = ? ORDER BY started_at DESC LIMIT ?"
        )
        .bind(status_str)
        .bind(limit)
        .fetch_all(&self.pool)
        .await?;

        Ok(executions)
    }

    pub async fn get_running_executions(&self) -> Result<Vec<Execution>> {
        let executions = sqlx::query_as::<_, Execution>(
            "SELECT * FROM executions WHERE status IN ('running', 'waiting') ORDER BY started_at"
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(executions)
    }

    pub async fn get_by_n8n_execution_id(&self, n8n_execution_id: &str) -> Result<Option<Execution>> {
        let execution = sqlx::query_as::<_, Execution>(
            "SELECT * FROM executions WHERE n8n_execution_id = ? LIMIT 1"
        )
        .bind(n8n_execution_id)
        .fetch_optional(&self.pool)
        .await?;

        Ok(execution)
    }

    pub async fn get_summary(&self, workflow_id: Option<&str>) -> Result<ExecutionSummary> {
        let (total, success, failed, running, cancelled, avg_duration) = if let Some(workflow_id) = workflow_id {
            let total = sqlx::query_scalar::<_, i64>(
                "SELECT COUNT(*) FROM executions WHERE workflow_id = ?"
            )
            .bind(workflow_id)
            .fetch_one(&self.pool)
            .await?;

            let success = sqlx::query_scalar::<_, i64>(
                "SELECT COUNT(*) FROM executions WHERE workflow_id = ? AND status = 'success'"
            )
            .bind(workflow_id)
            .fetch_one(&self.pool)
            .await?;

            let failed = sqlx::query_scalar::<_, i64>(
                "SELECT COUNT(*) FROM executions WHERE workflow_id = ? AND status = 'failed'"
            )
            .bind(workflow_id)
            .fetch_one(&self.pool)
            .await?;

            let running = sqlx::query_scalar::<_, i64>(
                "SELECT COUNT(*) FROM executions WHERE workflow_id = ? AND status IN ('running', 'waiting')"
            )
            .bind(workflow_id)
            .fetch_one(&self.pool)
            .await?;

            let cancelled = sqlx::query_scalar::<_, i64>(
                "SELECT COUNT(*) FROM executions WHERE workflow_id = ? AND status = 'cancelled'"
            )
            .bind(workflow_id)
            .fetch_one(&self.pool)
            .await?;

            let avg_duration = sqlx::query_scalar::<_, Option<f64>>(
                "SELECT AVG(duration) FROM executions WHERE workflow_id = ? AND duration IS NOT NULL"
            )
            .bind(workflow_id)
            .fetch_one(&self.pool)
            .await?;

            (total, success, failed, running, cancelled, avg_duration.map(|d| d / 1000.0))
        } else {
            let total = sqlx::query_scalar::<_, i64>("SELECT COUNT(*) FROM executions")
                .fetch_one(&self.pool)
                .await?;

            let success = sqlx::query_scalar::<_, i64>(
                "SELECT COUNT(*) FROM executions WHERE status = 'success'"
            )
            .fetch_one(&self.pool)
            .await?;

            let failed = sqlx::query_scalar::<_, i64>(
                "SELECT COUNT(*) FROM executions WHERE status = 'failed'"
            )
            .fetch_one(&self.pool)
            .await?;

            let running = sqlx::query_scalar::<_, i64>(
                "SELECT COUNT(*) FROM executions WHERE status IN ('running', 'waiting')"
            )
            .fetch_one(&self.pool)
            .await?;

            let cancelled = sqlx::query_scalar::<_, i64>(
                "SELECT COUNT(*) FROM executions WHERE status = 'cancelled'"
            )
            .fetch_one(&self.pool)
            .await?;

            let avg_duration = sqlx::query_scalar::<_, Option<f64>>(
                "SELECT AVG(duration) FROM executions WHERE duration IS NOT NULL"
            )
            .fetch_one(&self.pool)
            .await?;

            (total, success, failed, running, cancelled, avg_duration.map(|d| d / 1000.0))
        };

        Ok(ExecutionSummary::new(total, success, failed, running, cancelled, avg_duration))
    }
}

impl Repository<Execution> for ExecutionRepository {
    async fn create(&self, execution: &Execution) -> Result<Execution> {
        sqlx::query(
            r#"
            INSERT INTO executions (
                id, workflow_id, n8n_execution_id, status, mode, started_at,
                finished_at, duration, data, error, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#
        )
        .bind(&execution.id)
        .bind(&execution.workflow_id)
        .bind(&execution.n8n_execution_id)
        .bind(&execution.status)
        .bind(&execution.mode)
        .bind(execution.started_at)
        .bind(execution.finished_at)
        .bind(execution.duration)
        .bind(&execution.data)
        .bind(&execution.error)
        .bind(execution.created_at)
        .bind(execution.updated_at)
        .execute(&self.pool)
        .await?;

        Ok(execution.clone())
    }

    async fn get_by_id(&self, id: &str) -> Result<Option<Execution>> {
        let execution = sqlx::query_as::<_, Execution>(
            "SELECT * FROM executions WHERE id = ? LIMIT 1"
        )
        .bind(id)
        .fetch_optional(&self.pool)
        .await?;

        Ok(execution)
    }

    async fn update(&self, execution: &Execution) -> Result<Execution> {
        sqlx::query(
            r#"
            UPDATE executions SET
                status = ?, finished_at = ?, duration = ?, data = ?, error = ?, updated_at = ?
            WHERE id = ?
            "#
        )
        .bind(&execution.status)
        .bind(execution.finished_at)
        .bind(execution.duration)
        .bind(&execution.data)
        .bind(&execution.error)
        .bind(execution.updated_at)
        .bind(&execution.id)
        .execute(&self.pool)
        .await?;

        Ok(execution.clone())
    }

    async fn delete(&self, id: &str) -> Result<bool> {
        let result = sqlx::query("DELETE FROM executions WHERE id = ?")
            .bind(id)
            .execute(&self.pool)
            .await?;

        Ok(result.rows_affected() > 0)
    }

    async fn list(&self, limit: Option<i64>, offset: Option<i64>) -> Result<Vec<Execution>> {
        let limit = limit.unwrap_or(100);
        let offset = offset.unwrap_or(0);

        let executions = sqlx::query_as::<_, Execution>(
            "SELECT * FROM executions ORDER BY started_at DESC LIMIT ? OFFSET ?"
        )
        .bind(limit)
        .bind(offset)
        .fetch_all(&self.pool)
        .await?;

        Ok(executions)
    }
}
