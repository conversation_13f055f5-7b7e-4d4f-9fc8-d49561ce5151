pub mod n8n_instance_repository;
pub mod workflow_repository;
pub mod execution_repository;
pub mod template_repository;
pub mod ai_config_repository;
pub mod app_settings_repository;

pub use n8n_instance_repository::N8nInstanceRepository;
pub use workflow_repository::WorkflowRepository;
pub use execution_repository::ExecutionRepository;
pub use template_repository::TemplateRepository;
pub use ai_config_repository::AiConfigRepository;
pub use app_settings_repository::AppSettingsRepository;

use anyhow::Result;
use sqlx::{Pool, Sqlite};

pub trait Repository<T> {
    async fn create(&self, item: &T) -> Result<T>;
    async fn get_by_id(&self, id: &str) -> Result<Option<T>>;
    async fn update(&self, item: &T) -> Result<T>;
    async fn delete(&self, id: &str) -> Result<bool>;
    async fn list(&self, limit: Option<i64>, offset: Option<i64>) -> Result<Vec<T>>;
}

pub struct Repositories {
    pub n8n_instances: N8nInstanceRepository,
    pub workflows: WorkflowRepository,
    pub executions: ExecutionRepository,
    pub templates: TemplateRepository,
    pub ai_configs: AiConfigRepository,
    pub app_settings: AppSettingsRepository,
}

impl Repositories {
    pub fn new(pool: Pool<Sqlite>) -> Self {
        Self {
            n8n_instances: N8nInstanceRepository::new(pool.clone()),
            workflows: WorkflowRepository::new(pool.clone()),
            executions: ExecutionRepository::new(pool.clone()),
            templates: TemplateRepository::new(pool.clone()),
            ai_configs: AiConfigRepository::new(pool.clone()),
            app_settings: AppSettingsRepository::new(pool),
        }
    }
}
