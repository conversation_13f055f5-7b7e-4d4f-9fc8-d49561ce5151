use anyhow::Result;
use sqlx::{Pool, Sqlite};
use crate::models::{N8nInstance, N8nInstanceWithStats, CreateN8nInstance, UpdateN8nInstance, Status};
use super::Repository;

pub struct N8nInstanceRepository {
    pool: Pool<Sqlite>,
}

impl N8nInstanceRepository {
    pub fn new(pool: Pool<Sqlite>) -> Self {
        Self { pool }
    }

    pub async fn get_default(&self) -> Result<Option<N8nInstance>> {
        let instance = sqlx::query_as::<_, N8nInstance>(
            "SELECT * FROM n8n_instances WHERE is_default = TRUE LIMIT 1"
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(instance)
    }

    pub async fn set_default(&self, id: &str) -> Result<()> {
        // First, unset all defaults
        sqlx::query("UPDATE n8n_instances SET is_default = FALSE")
            .execute(&self.pool)
            .await?;

        // Then set the new default
        sqlx::query("UPDATE n8n_instances SET is_default = TRUE WHERE id = ?")
            .bind(id)
            .execute(&self.pool)
            .await?;

        Ok(())
    }

    pub async fn get_by_url(&self, url: &str) -> Result<Option<N8nInstance>> {
        let instance = sqlx::query_as::<_, N8nInstance>(
            "SELECT * FROM n8n_instances WHERE url = ? LIMIT 1"
        )
        .bind(url)
        .fetch_optional(&self.pool)
        .await?;

        Ok(instance)
    }

    pub async fn get_active_instances(&self) -> Result<Vec<N8nInstance>> {
        let instances = sqlx::query_as::<_, N8nInstance>(
            "SELECT * FROM n8n_instances WHERE status = 'active' ORDER BY name"
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(instances)
    }

    pub async fn get_with_stats(&self, id: &str) -> Result<Option<N8nInstanceWithStats>> {
        let instance = self.get_by_id(id).await?;
        
        if let Some(instance) = instance {
            let workflow_count = sqlx::query_scalar::<_, i64>(
                "SELECT COUNT(*) FROM workflows WHERE n8n_instance_id = ?"
            )
            .bind(id)
            .fetch_one(&self.pool)
            .await?;

            let active_workflow_count = sqlx::query_scalar::<_, i64>(
                "SELECT COUNT(*) FROM workflows WHERE n8n_instance_id = ? AND is_active = TRUE"
            )
            .bind(id)
            .fetch_one(&self.pool)
            .await?;

            let execution_count = sqlx::query_scalar::<_, i64>(
                r#"
                SELECT COUNT(*) FROM executions e
                JOIN workflows w ON e.workflow_id = w.id
                WHERE w.n8n_instance_id = ?
                "#
            )
            .bind(id)
            .fetch_one(&self.pool)
            .await?;

            let last_execution = sqlx::query_scalar::<_, Option<chrono::DateTime<chrono::Utc>>>(
                r#"
                SELECT MAX(e.started_at) FROM executions e
                JOIN workflows w ON e.workflow_id = w.id
                WHERE w.n8n_instance_id = ?
                "#
            )
            .bind(id)
            .fetch_one(&self.pool)
            .await?;

            Ok(Some(N8nInstanceWithStats {
                instance,
                workflow_count,
                active_workflow_count,
                execution_count,
                last_execution,
            }))
        } else {
            Ok(None)
        }
    }

    pub async fn update_status(&self, id: &str, status: Status, version: Option<String>) -> Result<()> {
        let status_str = String::from(status);
        let now = chrono::Utc::now();

        sqlx::query(
            r#"
            UPDATE n8n_instances 
            SET status = ?, version = ?, last_ping = ?, updated_at = ?
            WHERE id = ?
            "#
        )
        .bind(&status_str)
        .bind(version)
        .bind(now)
        .bind(now)
        .bind(id)
        .execute(&self.pool)
        .await?;

        Ok(())
    }
}

impl Repository<N8nInstance> for N8nInstanceRepository {
    async fn create(&self, instance: &N8nInstance) -> Result<N8nInstance> {
        sqlx::query(
            r#"
            INSERT INTO n8n_instances (
                id, name, url, api_key, status, version, description, 
                is_default, created_at, updated_at, last_ping
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#
        )
        .bind(&instance.id)
        .bind(&instance.name)
        .bind(&instance.url)
        .bind(&instance.api_key)
        .bind(&instance.status)
        .bind(&instance.version)
        .bind(&instance.description)
        .bind(instance.is_default)
        .bind(instance.created_at)
        .bind(instance.updated_at)
        .bind(instance.last_ping)
        .execute(&self.pool)
        .await?;

        Ok(instance.clone())
    }

    async fn get_by_id(&self, id: &str) -> Result<Option<N8nInstance>> {
        let instance = sqlx::query_as::<_, N8nInstance>(
            "SELECT * FROM n8n_instances WHERE id = ? LIMIT 1"
        )
        .bind(id)
        .fetch_optional(&self.pool)
        .await?;

        Ok(instance)
    }

    async fn update(&self, instance: &N8nInstance) -> Result<N8nInstance> {
        sqlx::query(
            r#"
            UPDATE n8n_instances SET
                name = ?, url = ?, api_key = ?, status = ?, version = ?,
                description = ?, is_default = ?, updated_at = ?, last_ping = ?
            WHERE id = ?
            "#
        )
        .bind(&instance.name)
        .bind(&instance.url)
        .bind(&instance.api_key)
        .bind(&instance.status)
        .bind(&instance.version)
        .bind(&instance.description)
        .bind(instance.is_default)
        .bind(instance.updated_at)
        .bind(instance.last_ping)
        .bind(&instance.id)
        .execute(&self.pool)
        .await?;

        Ok(instance.clone())
    }

    async fn delete(&self, id: &str) -> Result<bool> {
        let result = sqlx::query("DELETE FROM n8n_instances WHERE id = ?")
            .bind(id)
            .execute(&self.pool)
            .await?;

        Ok(result.rows_affected() > 0)
    }

    async fn list(&self, limit: Option<i64>, offset: Option<i64>) -> Result<Vec<N8nInstance>> {
        let limit = limit.unwrap_or(100);
        let offset = offset.unwrap_or(0);

        let instances = sqlx::query_as::<_, N8nInstance>(
            "SELECT * FROM n8n_instances ORDER BY created_at DESC LIMIT ? OFFSET ?"
        )
        .bind(limit)
        .bind(offset)
        .fetch_all(&self.pool)
        .await?;

        Ok(instances)
    }
}
