use anyhow::Result;
use sqlx::{Pool, Sqlite};
use crate::models::{Template, CreateTemplate, UpdateTemplate, TemplateCategory, TemplateWithUsage};
use super::Repository;

pub struct TemplateRepository {
    pool: Pool<Sqlite>,
}

impl TemplateRepository {
    pub fn new(pool: Pool<Sqlite>) -> Self {
        Self { pool }
    }

    pub async fn get_by_category(&self, category: &str, limit: Option<i64>, offset: Option<i64>) -> Result<Vec<Template>> {
        let limit = limit.unwrap_or(50);
        let offset = offset.unwrap_or(0);

        let templates = sqlx::query_as::<_, Template>(
            "SELECT * FROM templates WHERE category = ? ORDER BY usage_count DESC, name LIMIT ? OFFSET ?"
        )
        .bind(category)
        .bind(limit)
        .bind(offset)
        .fetch_all(&self.pool)
        .await?;

        Ok(templates)
    }

    pub async fn get_public_templates(&self, limit: Option<i64>, offset: Option<i64>) -> Result<Vec<Template>> {
        let limit = limit.unwrap_or(50);
        let offset = offset.unwrap_or(0);

        let templates = sqlx::query_as::<_, Template>(
            "SELECT * FROM templates WHERE is_public = TRUE ORDER BY usage_count DESC, name LIMIT ? OFFSET ?"
        )
        .bind(limit)
        .bind(offset)
        .fetch_all(&self.pool)
        .await?;

        Ok(templates)
    }

    pub async fn search(&self, query: &str, category: Option<&str>) -> Result<Vec<Template>> {
        let search_pattern = format!("%{}%", query);
        
        let templates = if let Some(category) = category {
            sqlx::query_as::<_, Template>(
                r#"
                SELECT * FROM templates 
                WHERE category = ? AND (
                    name LIKE ? OR 
                    description LIKE ? OR 
                    tags LIKE ?
                )
                ORDER BY usage_count DESC, name
                "#
            )
            .bind(category)
            .bind(&search_pattern)
            .bind(&search_pattern)
            .bind(&search_pattern)
            .fetch_all(&self.pool)
            .await?
        } else {
            sqlx::query_as::<_, Template>(
                r#"
                SELECT * FROM templates 
                WHERE name LIKE ? OR description LIKE ? OR tags LIKE ?
                ORDER BY usage_count DESC, name
                "#
            )
            .bind(&search_pattern)
            .bind(&search_pattern)
            .bind(&search_pattern)
            .fetch_all(&self.pool)
            .await?
        };

        Ok(templates)
    }

    pub async fn get_categories(&self) -> Result<Vec<TemplateCategory>> {
        let categories = sqlx::query_as::<_, (String, i64)>(
            "SELECT category, COUNT(*) as count FROM templates GROUP BY category ORDER BY count DESC"
        )
        .fetch_all(&self.pool)
        .await?;

        let result = categories
            .into_iter()
            .map(|(name, count)| TemplateCategory {
                name,
                count,
                description: None,
            })
            .collect();

        Ok(result)
    }

    pub async fn get_popular_templates(&self, limit: Option<i64>) -> Result<Vec<Template>> {
        let limit = limit.unwrap_or(10);

        let templates = sqlx::query_as::<_, Template>(
            "SELECT * FROM templates ORDER BY usage_count DESC LIMIT ?"
        )
        .bind(limit)
        .fetch_all(&self.pool)
        .await?;

        Ok(templates)
    }

    pub async fn increment_usage(&self, id: &str) -> Result<()> {
        sqlx::query(
            "UPDATE templates SET usage_count = usage_count + 1, updated_at = ? WHERE id = ?"
        )
        .bind(chrono::Utc::now())
        .bind(id)
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_with_usage(&self, id: &str) -> Result<Option<TemplateWithUsage>> {
        let template = self.get_by_id(id).await?;
        
        if let Some(template) = template {
            // For now, return empty usage data - this would be implemented with actual usage tracking
            Ok(Some(TemplateWithUsage {
                template,
                recent_usage: vec![],
                popularity_score: 0.0,
            }))
        } else {
            Ok(None)
        }
    }
}

impl Repository<Template> for TemplateRepository {
    async fn create(&self, template: &Template) -> Result<Template> {
        sqlx::query(
            r#"
            INSERT INTO templates (
                id, name, description, category, tags, nodes, connections,
                settings, static_data, variables, is_public, usage_count,
                created_at, updated_at, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#
        )
        .bind(&template.id)
        .bind(&template.name)
        .bind(&template.description)
        .bind(&template.category)
        .bind(&template.tags)
        .bind(&template.nodes)
        .bind(&template.connections)
        .bind(&template.settings)
        .bind(&template.static_data)
        .bind(&template.variables)
        .bind(template.is_public)
        .bind(template.usage_count)
        .bind(template.created_at)
        .bind(template.updated_at)
        .bind(&template.created_by)
        .execute(&self.pool)
        .await?;

        Ok(template.clone())
    }

    async fn get_by_id(&self, id: &str) -> Result<Option<Template>> {
        let template = sqlx::query_as::<_, Template>(
            "SELECT * FROM templates WHERE id = ? LIMIT 1"
        )
        .bind(id)
        .fetch_optional(&self.pool)
        .await?;

        Ok(template)
    }

    async fn update(&self, template: &Template) -> Result<Template> {
        sqlx::query(
            r#"
            UPDATE templates SET
                name = ?, description = ?, category = ?, tags = ?, nodes = ?,
                connections = ?, settings = ?, static_data = ?, variables = ?,
                is_public = ?, updated_at = ?
            WHERE id = ?
            "#
        )
        .bind(&template.name)
        .bind(&template.description)
        .bind(&template.category)
        .bind(&template.tags)
        .bind(&template.nodes)
        .bind(&template.connections)
        .bind(&template.settings)
        .bind(&template.static_data)
        .bind(&template.variables)
        .bind(template.is_public)
        .bind(template.updated_at)
        .bind(&template.id)
        .execute(&self.pool)
        .await?;

        Ok(template.clone())
    }

    async fn delete(&self, id: &str) -> Result<bool> {
        let result = sqlx::query("DELETE FROM templates WHERE id = ?")
            .bind(id)
            .execute(&self.pool)
            .await?;

        Ok(result.rows_affected() > 0)
    }

    async fn list(&self, limit: Option<i64>, offset: Option<i64>) -> Result<Vec<Template>> {
        let limit = limit.unwrap_or(100);
        let offset = offset.unwrap_or(0);

        let templates = sqlx::query_as::<_, Template>(
            "SELECT * FROM templates ORDER BY created_at DESC LIMIT ? OFFSET ?"
        )
        .bind(limit)
        .bind(offset)
        .fetch_all(&self.pool)
        .await?;

        Ok(templates)
    }
}
