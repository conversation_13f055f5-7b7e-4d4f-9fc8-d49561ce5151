use anyhow::Result;
use sqlx::{Pool, Sqlite};
use crate::models::{Workflow, WorkflowWithStats, CreateWorkflow, UpdateWorkflow, Status};
use super::Repository;

pub struct WorkflowRepository {
    pool: Pool<Sqlite>,
}

impl WorkflowRepository {
    pub fn new(pool: Pool<Sqlite>) -> Self {
        Self { pool }
    }

    pub async fn get_by_instance(&self, instance_id: &str) -> Result<Vec<Workflow>> {
        let workflows = sqlx::query_as::<_, Workflow>(
            "SELECT * FROM workflows WHERE n8n_instance_id = ? ORDER BY name"
        )
        .bind(instance_id)
        .fetch_all(&self.pool)
        .await?;

        Ok(workflows)
    }

    pub async fn get_by_n8n_id(&self, instance_id: &str, n8n_workflow_id: &str) -> Result<Option<Workflow>> {
        let workflow = sqlx::query_as::<_, Workflow>(
            "SELECT * FROM workflows WHERE n8n_instance_id = ? AND n8n_workflow_id = ? LIMIT 1"
        )
        .bind(instance_id)
        .bind(n8n_workflow_id)
        .fetch_optional(&self.pool)
        .await?;

        Ok(workflow)
    }

    pub async fn get_by_n8n_workflow_id(&self, n8n_workflow_id: &str) -> Result<Option<Workflow>> {
        let workflow = sqlx::query_as::<_, Workflow>(
            "SELECT * FROM workflows WHERE n8n_workflow_id = ? LIMIT 1"
        )
        .bind(n8n_workflow_id)
        .fetch_optional(&self.pool)
        .await?;

        Ok(workflow)
    }

    pub async fn get_active_workflows(&self, instance_id: Option<&str>) -> Result<Vec<Workflow>> {
        let workflows = if let Some(instance_id) = instance_id {
            sqlx::query_as::<_, Workflow>(
                "SELECT * FROM workflows WHERE n8n_instance_id = ? AND is_active = TRUE ORDER BY name"
            )
            .bind(instance_id)
            .fetch_all(&self.pool)
            .await?
        } else {
            sqlx::query_as::<_, Workflow>(
                "SELECT * FROM workflows WHERE is_active = TRUE ORDER BY name"
            )
            .fetch_all(&self.pool)
            .await?
        };

        Ok(workflows)
    }

    pub async fn search(&self, query: &str, instance_id: Option<&str>) -> Result<Vec<Workflow>> {
        let search_pattern = format!("%{}%", query);
        
        let workflows = if let Some(instance_id) = instance_id {
            sqlx::query_as::<_, Workflow>(
                r#"
                SELECT * FROM workflows 
                WHERE n8n_instance_id = ? AND (
                    name LIKE ? OR 
                    description LIKE ? OR 
                    tags LIKE ?
                )
                ORDER BY name
                "#
            )
            .bind(instance_id)
            .bind(&search_pattern)
            .bind(&search_pattern)
            .bind(&search_pattern)
            .fetch_all(&self.pool)
            .await?
        } else {
            sqlx::query_as::<_, Workflow>(
                r#"
                SELECT * FROM workflows 
                WHERE name LIKE ? OR description LIKE ? OR tags LIKE ?
                ORDER BY name
                "#
            )
            .bind(&search_pattern)
            .bind(&search_pattern)
            .bind(&search_pattern)
            .fetch_all(&self.pool)
            .await?
        };

        Ok(workflows)
    }

    pub async fn get_with_stats(&self, id: &str) -> Result<Option<WorkflowWithStats>> {
        let workflow = self.get_by_id(id).await?;
        
        if let Some(workflow) = workflow {
            // Get recent executions
            let recent_executions = sqlx::query_scalar::<_, String>(
                "SELECT id FROM executions WHERE workflow_id = ? ORDER BY started_at DESC LIMIT 10"
            )
            .bind(id)
            .fetch_all(&self.pool)
            .await?;

            // Calculate success rate
            let total_executions = sqlx::query_scalar::<_, i64>(
                "SELECT COUNT(*) FROM executions WHERE workflow_id = ?"
            )
            .bind(id)
            .fetch_one(&self.pool)
            .await?;

            let successful_executions = sqlx::query_scalar::<_, i64>(
                "SELECT COUNT(*) FROM executions WHERE workflow_id = ? AND status = 'success'"
            )
            .bind(id)
            .fetch_one(&self.pool)
            .await?;

            let success_rate = if total_executions > 0 {
                (successful_executions as f64 / total_executions as f64) * 100.0
            } else {
                0.0
            };

            // Calculate average execution time
            let avg_execution_time = sqlx::query_scalar::<_, Option<f64>>(
                "SELECT AVG(duration) FROM executions WHERE workflow_id = ? AND duration IS NOT NULL"
            )
            .bind(id)
            .fetch_one(&self.pool)
            .await?;

            Ok(Some(WorkflowWithStats {
                workflow,
                recent_executions,
                success_rate,
                avg_execution_time: avg_execution_time.map(|t| t / 1000.0), // Convert to seconds
            }))
        } else {
            Ok(None)
        }
    }

    pub async fn update_execution_stats(&self, id: &str) -> Result<()> {
        let execution_count = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM executions WHERE workflow_id = ?"
        )
        .bind(id)
        .fetch_one(&self.pool)
        .await?;

        let last_execution = sqlx::query_scalar::<_, Option<chrono::DateTime<chrono::Utc>>>(
            "SELECT MAX(started_at) FROM executions WHERE workflow_id = ?"
        )
        .bind(id)
        .fetch_one(&self.pool)
        .await?;

        sqlx::query(
            "UPDATE workflows SET execution_count = ?, last_execution = ?, updated_at = ? WHERE id = ?"
        )
        .bind(execution_count)
        .bind(last_execution)
        .bind(chrono::Utc::now())
        .bind(id)
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn set_active(&self, id: &str, is_active: bool) -> Result<()> {
        sqlx::query(
            "UPDATE workflows SET is_active = ?, updated_at = ? WHERE id = ?"
        )
        .bind(is_active)
        .bind(chrono::Utc::now())
        .bind(id)
        .execute(&self.pool)
        .await?;

        Ok(())
    }
}

impl Repository<Workflow> for WorkflowRepository {
    async fn create(&self, workflow: &Workflow) -> Result<Workflow> {
        sqlx::query(
            r#"
            INSERT INTO workflows (
                id, n8n_instance_id, n8n_workflow_id, name, description, status,
                is_active, tags, nodes, connections, settings, static_data,
                created_at, updated_at, last_execution, execution_count
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#
        )
        .bind(&workflow.id)
        .bind(&workflow.n8n_instance_id)
        .bind(&workflow.n8n_workflow_id)
        .bind(&workflow.name)
        .bind(&workflow.description)
        .bind(&workflow.status)
        .bind(workflow.is_active)
        .bind(&workflow.tags)
        .bind(&workflow.nodes)
        .bind(&workflow.connections)
        .bind(&workflow.settings)
        .bind(&workflow.static_data)
        .bind(workflow.created_at)
        .bind(workflow.updated_at)
        .bind(workflow.last_execution)
        .bind(workflow.execution_count)
        .execute(&self.pool)
        .await?;

        Ok(workflow.clone())
    }

    async fn get_by_id(&self, id: &str) -> Result<Option<Workflow>> {
        let workflow = sqlx::query_as::<_, Workflow>(
            "SELECT * FROM workflows WHERE id = ? LIMIT 1"
        )
        .bind(id)
        .fetch_optional(&self.pool)
        .await?;

        Ok(workflow)
    }

    async fn update(&self, workflow: &Workflow) -> Result<Workflow> {
        sqlx::query(
            r#"
            UPDATE workflows SET
                name = ?, description = ?, status = ?, is_active = ?,
                tags = ?, nodes = ?, connections = ?, settings = ?, static_data = ?,
                updated_at = ?, last_execution = ?, execution_count = ?
            WHERE id = ?
            "#
        )
        .bind(&workflow.name)
        .bind(&workflow.description)
        .bind(&workflow.status)
        .bind(workflow.is_active)
        .bind(&workflow.tags)
        .bind(&workflow.nodes)
        .bind(&workflow.connections)
        .bind(&workflow.settings)
        .bind(&workflow.static_data)
        .bind(workflow.updated_at)
        .bind(workflow.last_execution)
        .bind(workflow.execution_count)
        .bind(&workflow.id)
        .execute(&self.pool)
        .await?;

        Ok(workflow.clone())
    }

    async fn delete(&self, id: &str) -> Result<bool> {
        let result = sqlx::query("DELETE FROM workflows WHERE id = ?")
            .bind(id)
            .execute(&self.pool)
            .await?;

        Ok(result.rows_affected() > 0)
    }

    async fn list(&self, limit: Option<i64>, offset: Option<i64>) -> Result<Vec<Workflow>> {
        let limit = limit.unwrap_or(100);
        let offset = offset.unwrap_or(0);

        let workflows = sqlx::query_as::<_, Workflow>(
            "SELECT * FROM workflows ORDER BY created_at DESC LIMIT ? OFFSET ?"
        )
        .bind(limit)
        .bind(offset)
        .fetch_all(&self.pool)
        .await?;

        Ok(workflows)
    }
}
