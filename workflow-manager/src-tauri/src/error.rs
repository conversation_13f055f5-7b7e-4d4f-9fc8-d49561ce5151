use thiserror::Error;

#[derive(Erro<PERSON>, Debug)]
pub enum WorkflowManagerError {
    #[error("Database error: {0}")]
    Database(#[from] sqlx::Error),

    #[error("Credential error: {0}")]
    Credential(String),

    #[error("N8N API error: {0}")]
    N8nApi(String),

    #[error("AI service error: {0}")]
    AiService(String),

    #[error("System error: {0}")]
    System(String),

    #[error("Validation error: {0}")]
    Validation(String),

    #[error("Not found: {0}")]
    NotFound(String),

    #[error("Unauthorized: {0}")]
    Unauthorized(String),

    #[error("Configuration error: {0}")]
    Configuration(String),

    #[error("Network error: {0}")]
    Network(#[from] reqwest::Error),

    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),

    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),

    #[error("UUID error: {0}")]
    Uuid(#[from] uuid::Error),

    #[error("Chrono parse error: {0}")]
    ChronoParse(#[from] chrono::ParseError),

    #[error("Internal error: {0}")]
    Internal(String),
}

impl WorkflowManagerError {
    pub fn credential(msg: impl Into<String>) -> Self {
        Self::Credential(msg.into())
    }

    pub fn n8n_api(msg: impl Into<String>) -> Self {
        Self::N8nApi(msg.into())
    }

    pub fn ai_service(msg: impl Into<String>) -> Self {
        Self::AiService(msg.into())
    }

    pub fn system(msg: impl Into<String>) -> Self {
        Self::System(msg.into())
    }

    pub fn validation(msg: impl Into<String>) -> Self {
        Self::Validation(msg.into())
    }

    pub fn not_found(msg: impl Into<String>) -> Self {
        Self::NotFound(msg.into())
    }

    pub fn unauthorized(msg: impl Into<String>) -> Self {
        Self::Unauthorized(msg.into())
    }

    pub fn configuration(msg: impl Into<String>) -> Self {
        Self::Configuration(msg.into())
    }

    pub fn internal(msg: impl Into<String>) -> Self {
        Self::Internal(msg.into())
    }

    /// Get the error category for logging and metrics
    pub fn category(&self) -> &'static str {
        match self {
            Self::Database(_) => "database",
            Self::Credential(_) => "credential",
            Self::N8nApi(_) => "n8n_api",
            Self::AiService(_) => "ai_service",
            Self::System(_) => "system",
            Self::Validation(_) => "validation",
            Self::NotFound(_) => "not_found",
            Self::Unauthorized(_) => "unauthorized",
            Self::Configuration(_) => "configuration",
            Self::Network(_) => "network",
            Self::Serialization(_) => "serialization",
            Self::Io(_) => "io",
            Self::Uuid(_) => "uuid",
            Self::ChronoParse(_) => "chrono_parse",
            Self::Internal(_) => "internal",
        }
    }

    /// Check if the error is retryable
    pub fn is_retryable(&self) -> bool {
        match self {
            Self::Network(_) => true,
            Self::N8nApi(msg) => {
                // Retry on timeout or server errors, but not on auth errors
                msg.contains("timeout") || msg.contains("500") || msg.contains("502") || msg.contains("503")
            },
            Self::Database(sqlx::Error::PoolTimedOut) => true,
            Self::Database(sqlx::Error::Io(_)) => true,
            Self::System(_) => false, // System errors are usually not retryable
            Self::Validation(_) => false, // Validation errors need fixing, not retrying
            Self::NotFound(_) => false,
            Self::Unauthorized(_) => false,
            Self::Configuration(_) => false,
            _ => false,
        }
    }

    /// Get the HTTP status code equivalent for this error
    pub fn status_code(&self) -> u16 {
        match self {
            Self::NotFound(_) => 404,
            Self::Unauthorized(_) => 401,
            Self::Validation(_) => 400,
            Self::Configuration(_) => 400,
            Self::Database(_) => 500,
            Self::N8nApi(_) => 502,
            Self::AiService(_) => 502,
            Self::Network(_) => 502,
            Self::System(_) => 500,
            Self::Credential(_) => 500,
            Self::Serialization(_) => 500,
            Self::Io(_) => 500,
            Self::Uuid(_) => 400,
            Self::ChronoParse(_) => 400,
            Self::Internal(_) => 500,
        }
    }
}

pub type Result<T> = std::result::Result<T, WorkflowManagerError>;

// Error context for better debugging
#[derive(Debug, Clone)]
pub struct ErrorContext {
    pub operation: String,
    pub component: String,
    pub user_id: Option<String>,
    pub instance_id: Option<String>,
    pub workflow_id: Option<String>,
    pub execution_id: Option<String>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

impl ErrorContext {
    pub fn new(operation: impl Into<String>, component: impl Into<String>) -> Self {
        Self {
            operation: operation.into(),
            component: component.into(),
            user_id: None,
            instance_id: None,
            workflow_id: None,
            execution_id: None,
            timestamp: chrono::Utc::now(),
        }
    }

    pub fn with_instance_id(mut self, instance_id: impl Into<String>) -> Self {
        self.instance_id = Some(instance_id.into());
        self
    }

    pub fn with_workflow_id(mut self, workflow_id: impl Into<String>) -> Self {
        self.workflow_id = Some(workflow_id.into());
        self
    }

    pub fn with_execution_id(mut self, execution_id: impl Into<String>) -> Self {
        self.execution_id = Some(execution_id.into());
        self
    }

    pub fn with_user_id(mut self, user_id: impl Into<String>) -> Self {
        self.user_id = Some(user_id.into());
        self
    }
}

// Enhanced error with context
#[derive(Debug)]
pub struct ContextualError {
    pub error: WorkflowManagerError,
    pub context: ErrorContext,
}

impl ContextualError {
    pub fn new(error: WorkflowManagerError, context: ErrorContext) -> Self {
        Self { error, context }
    }

    pub fn log(&self) {
        log::error!(
            "[{}:{}] {} - {} | Instance: {:?} | Workflow: {:?} | Execution: {:?}",
            self.context.component,
            self.context.operation,
            self.error.category(),
            self.error,
            self.context.instance_id,
            self.context.workflow_id,
            self.context.execution_id
        );
    }
}

impl std::fmt::Display for ContextualError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{} (in {}:{})", self.error, self.context.component, self.context.operation)
    }
}

impl std::error::Error for ContextualError {
    fn source(&self) -> Option<&(dyn std::error::Error + 'static)> {
        Some(&self.error)
    }
}

// Validation helpers
pub mod validation {
    use super::*;

    pub fn validate_url(url: &str) -> Result<()> {
        if url.is_empty() {
            return Err(WorkflowManagerError::validation("URL cannot be empty"));
        }

        if !url.starts_with("http://") && !url.starts_with("https://") {
            return Err(WorkflowManagerError::validation("URL must start with http:// or https://"));
        }

        // Basic URL validation
        url::Url::parse(url)
            .map_err(|_| WorkflowManagerError::validation("Invalid URL format"))?;

        Ok(())
    }

    pub fn validate_name(name: &str, min_length: usize, max_length: usize) -> Result<()> {
        if name.is_empty() {
            return Err(WorkflowManagerError::validation("Name cannot be empty"));
        }

        if name.len() < min_length {
            return Err(WorkflowManagerError::validation(format!(
                "Name must be at least {} characters long", min_length
            )));
        }

        if name.len() > max_length {
            return Err(WorkflowManagerError::validation(format!(
                "Name must be at most {} characters long", max_length
            )));
        }

        // Check for invalid characters
        if name.chars().any(|c| c.is_control()) {
            return Err(WorkflowManagerError::validation("Name contains invalid characters"));
        }

        Ok(())
    }

    pub fn validate_uuid(uuid_str: &str) -> Result<uuid::Uuid> {
        uuid::Uuid::parse_str(uuid_str)
            .map_err(|_| WorkflowManagerError::validation("Invalid UUID format"))
    }

    pub fn validate_json(json_str: &str) -> Result<serde_json::Value> {
        serde_json::from_str(json_str)
            .map_err(|e| WorkflowManagerError::validation(format!("Invalid JSON: {}", e)))
    }

    pub fn validate_workflow_nodes(nodes: &serde_json::Value) -> Result<()> {
        if !nodes.is_array() {
            return Err(WorkflowManagerError::validation("Workflow nodes must be an array"));
        }

        let nodes_array = nodes.as_array().unwrap();
        if nodes_array.is_empty() {
            return Err(WorkflowManagerError::validation("Workflow must have at least one node"));
        }

        // Validate each node has required fields
        for (index, node) in nodes_array.iter().enumerate() {
            if !node.is_object() {
                return Err(WorkflowManagerError::validation(format!(
                    "Node at index {} must be an object", index
                )));
            }

            let node_obj = node.as_object().unwrap();
            
            if !node_obj.contains_key("id") {
                return Err(WorkflowManagerError::validation(format!(
                    "Node at index {} missing required 'id' field", index
                )));
            }

            if !node_obj.contains_key("type") {
                return Err(WorkflowManagerError::validation(format!(
                    "Node at index {} missing required 'type' field", index
                )));
            }
        }

        Ok(())
    }

    pub fn validate_api_key(api_key: &str) -> Result<()> {
        if api_key.is_empty() {
            return Err(WorkflowManagerError::validation("API key cannot be empty"));
        }

        if api_key.len() < 10 {
            return Err(WorkflowManagerError::validation("API key is too short"));
        }

        // Check for suspicious patterns
        if api_key.chars().all(|c| c.is_ascii_digit()) {
            return Err(WorkflowManagerError::validation("API key appears to be invalid (all digits)"));
        }

        Ok(())
    }
}

// Retry mechanism
pub mod retry {
    use super::*;
    use std::time::Duration;
    use tokio::time::sleep;

    pub struct RetryConfig {
        pub max_attempts: u32,
        pub base_delay: Duration,
        pub max_delay: Duration,
        pub backoff_multiplier: f64,
    }

    impl Default for RetryConfig {
        fn default() -> Self {
            Self {
                max_attempts: 3,
                base_delay: Duration::from_millis(100),
                max_delay: Duration::from_secs(30),
                backoff_multiplier: 2.0,
            }
        }
    }

    pub async fn retry_with_backoff<T, F, Fut>(
        operation: F,
        config: RetryConfig,
    ) -> Result<T>
    where
        F: Fn() -> Fut,
        Fut: std::future::Future<Output = Result<T>>,
    {
        let mut last_error = None;
        let mut delay = config.base_delay;

        for attempt in 1..=config.max_attempts {
            match operation().await {
                Ok(result) => return Ok(result),
                Err(error) => {
                    last_error = Some(error);
                    
                    if attempt < config.max_attempts {
                        if let Some(ref err) = last_error {
                            if !err.is_retryable() {
                                break;
                            }
                        }

                        sleep(delay).await;
                        delay = std::cmp::min(
                            Duration::from_millis((delay.as_millis() as f64 * config.backoff_multiplier) as u64),
                            config.max_delay,
                        );
                    }
                }
            }
        }

        Err(last_error.unwrap_or_else(|| WorkflowManagerError::internal("Retry failed without error")))
    }
}
