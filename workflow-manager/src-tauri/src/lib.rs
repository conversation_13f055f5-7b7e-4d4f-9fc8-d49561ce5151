mod models;
mod database;
mod commands;
mod credentials;
mod n8n_api;
mod ai;
mod system;
mod error;
mod sync;

#[cfg(test)]
mod testing;

use database::{init_database, Repositories};
use credentials::service::CredentialService;
use std::sync::Arc;
use tauri::{Manager, tray::{TrayIconBuilder, TrayIconEvent}, menu::{MenuBuilder, MenuItemBuilder}, WindowEvent, WebviewWindow};

#[cfg(target_os = "macos")]
use objc::{msg_send, sel, sel_impl, class, runtime::Object};

#[cfg(target_os = "macos")]
fn hide_dock_icon() {
    unsafe {
        let app: *mut Object = msg_send![class!(NSApplication), sharedApplication];
        // NSApplicationActivationPolicyAccessory = 1
        let _: () = msg_send![app, setActivationPolicy: 1];
    }
}

#[cfg(target_os = "macos")]
fn show_dock_icon() {
    unsafe {
        let app: *mut Object = msg_send![class!(NSApplication), sharedApplication];

        // NSApplicationActivationPolicyRegular = 0
        let _: () = msg_send![app, setActivationPolicy: 0];

        // Force refresh the dock tile to prevent generic icon issue
        let dock_tile: *mut Object = msg_send![app, dockTile];
        if !dock_tile.is_null() {
            let _: () = msg_send![dock_tile, display];
        }

        // Small delay to ensure the activation policy change takes effect
        std::thread::sleep(std::time::Duration::from_millis(100));
    }
}
use tauri::menu::PredefinedMenuItem;
use std::fs::{File, OpenOptions};
use std::io::Write;

// Cross-platform window and taskbar management
fn hide_window_and_taskbar(window: &WebviewWindow) {
    // Hide the window first
    let _ = window.hide();

    // Try to hide from taskbar (Windows/Linux only - macOS doesn't support this)
    #[cfg(not(target_os = "macos"))]
    {
        let _ = window.set_skip_taskbar(true);
        println!("Window hidden and removed from taskbar (Windows/Linux)");
    }

    #[cfg(target_os = "macos")]
    {
        // On macOS, hide the dock icon when window is hidden
        hide_dock_icon();
        println!("Window hidden and dock icon hidden (macOS)");
    }
}

fn show_window_and_taskbar(window: &WebviewWindow) {
    // Restore taskbar visibility first (Windows/Linux only)
    #[cfg(not(target_os = "macos"))]
    {
        let _ = window.set_skip_taskbar(false);
    }

    #[cfg(target_os = "macos")]
    {
        // On macOS, show the dock icon when window is shown
        show_dock_icon();
    }

    // Show and focus the window
    let _ = window.show();
    let _ = window.set_focus();

    #[cfg(not(target_os = "macos"))]
    {
        println!("Window shown and added back to taskbar (Windows/Linux)");
    }

    #[cfg(target_os = "macos")]
    {
        println!("Window shown and dock icon restored (macOS)");
    }
}

#[derive(Clone)]
pub struct AppState {
    pub repositories: Arc<Repositories>,
    pub credential_service: Arc<CredentialService>,
    pub sync_service: Arc<sync::AutoSyncService>,
}

// Function to check if another instance is running
fn check_single_instance(app_handle: &tauri::AppHandle) -> Result<bool, Box<dyn std::error::Error>> {
    let app_data_dir = app_handle.path().app_data_dir()?;
    let lock_file_path = app_data_dir.join("workflow_manager.lock");

    // Ensure the directory exists
    if let Some(parent) = lock_file_path.parent() {
        std::fs::create_dir_all(parent)?;
    }

    // Try to create/open the lock file
    match OpenOptions::new()
        .create(true)
        .write(true)
        .truncate(true)
        .open(&lock_file_path)
    {
        Ok(mut file) => {
            // Write current process ID to the lock file
            let pid = std::process::id();
            writeln!(file, "{}", pid)?;
            file.flush()?;

            println!("Single instance lock acquired with PID: {}", pid);
            Ok(false) // No other instance running
        }
        Err(_) => {
            // Lock file exists and is locked by another process
            println!("Another instance is already running");
            Ok(true) // Another instance is running
        }
    }
}

// Function to send signal to existing instance to show window
fn signal_existing_instance(app_handle: &tauri::AppHandle) -> Result<(), Box<dyn std::error::Error>> {
    let app_data_dir = app_handle.path().app_data_dir()?;
    let signal_file_path = app_data_dir.join("show_window.signal");

    // Create a signal file to tell the existing instance to show the window
    let mut file = File::create(&signal_file_path)?;
    writeln!(file, "show")?;
    file.flush()?;

    println!("Signal sent to existing instance to show window");
    Ok(())
}

// Function to check for signals from new instances
fn setup_signal_monitoring(app_handle: &tauri::AppHandle) {
    let handle = app_handle.clone();
    let app_data_dir = match handle.path().app_data_dir() {
        Ok(dir) => dir,
        Err(_) => return,
    };
    let signal_file_path = app_data_dir.join("show_window.signal");

    tauri::async_runtime::spawn(async move {
        loop {
            tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;

            if signal_file_path.exists() {
                // Signal received, show the window and restore taskbar visibility
                if let Some(window) = handle.get_webview_window("main") {
                    show_window_and_taskbar(&window);
                    update_tray_menu(&handle, false);
                    println!("Window shown due to signal from new instance and added back to taskbar");
                }

                // Remove the signal file
                let _ = std::fs::remove_file(&signal_file_path);
            }
        }
    });
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::async_runtime::block_on(async {
        tauri::Builder::default()
            .plugin(tauri_plugin_opener::init())
            .plugin(tauri_plugin_sql::Builder::default().build())
            .plugin(tauri_plugin_store::Builder::default().build())
            .plugin(tauri_plugin_shell::init())
            .setup(|app| {
                let handle = app.handle().clone();

                // Check for single instance
                match check_single_instance(&handle) {
                    Ok(true) => {
                        // Another instance is already running, signal it to show window and exit
                        println!("Another instance detected, signaling to show window and exiting");
                        if let Err(e) = signal_existing_instance(&handle) {
                            eprintln!("Failed to signal existing instance: {}", e);
                        }
                        std::process::exit(0);
                    }
                    Ok(false) => {
                        // This is the first instance, continue setup
                        println!("First instance detected, continuing with setup");
                    }
                    Err(e) => {
                        eprintln!("Error checking single instance: {}", e);
                        // Continue anyway
                    }
                }

                // Setup signal monitoring for new instances
                setup_signal_monitoring(&handle);

                // Setup system tray
                setup_system_tray(&handle)?;

                // Setup window event handlers
                setup_window_events(&handle)?;

                tauri::async_runtime::spawn(async move {
                    match init_database(&handle).await {
                        Ok(pool) => {
                            let repositories = Arc::new(Repositories::new(pool));

                            // Initialize credential service
                            let app_data_dir = handle
                                .path()
                                .app_data_dir()
                                .expect("Failed to get app data directory");

                            let credentials_dir = app_data_dir.join("credentials");

                            match CredentialService::new(credentials_dir).await {
                                Ok(credential_service) => {
                                    let sync_service = Arc::new(sync::AutoSyncService::new(handle.clone()));
                                    let app_state = AppState {
                                        repositories,
                                        credential_service: Arc::new(credential_service),
                                        sync_service: sync_service.clone(),
                                    };
                                    handle.manage(app_state);
                                    println!("Database and credential service initialized successfully");

                                    // Start the auto sync service
                                    let sync_service_clone = sync_service.clone();
                                    tauri::async_runtime::spawn(async move {
                                        if let Err(e) = sync_service_clone.start().await {
                                            eprintln!("Failed to start auto sync service: {}", e);
                                        }
                                    });
                                }
                                Err(e) => {
                                    eprintln!("Failed to initialize credential service: {}", e);
                                    std::process::exit(1);
                                }
                            }
                        }
                        Err(e) => {
                            eprintln!("Failed to initialize database: {}", e);
                            std::process::exit(1);
                        }
                    }
                });

                Ok(())
            })
            .invoke_handler(tauri::generate_handler![
                // Credential management commands
                commands::credential_commands::create_credential,
                commands::credential_commands::get_credential_info,
                commands::credential_commands::list_credentials,
                commands::credential_commands::update_credential,
                commands::credential_commands::delete_credential,
                commands::credential_commands::validate_credential,
                commands::credential_commands::test_credential_format,
                commands::credential_commands::test_credential_connectivity,
                commands::credential_commands::get_storage_backend_info,
                commands::credential_commands::switch_storage_backend,
                commands::credential_commands::mark_credential_used,
                commands::credential_commands::generate_credential_id,
                commands::credential_commands::get_credential_types,
                // N8N instance management commands
                commands::n8n_instance_commands::create_n8n_instance,
                commands::n8n_instance_commands::get_n8n_instance,
                commands::n8n_instance_commands::get_n8n_instance_with_stats,
                commands::n8n_instance_commands::list_n8n_instances,
                commands::n8n_instance_commands::update_n8n_instance,
                commands::n8n_instance_commands::delete_n8n_instance,
                commands::n8n_instance_commands::set_default_n8n_instance,
                commands::n8n_instance_commands::get_default_n8n_instance,
                commands::n8n_instance_commands::get_active_n8n_instances,
                commands::n8n_instance_commands::ping_n8n_instance,
                commands::n8n_instance_commands::get_n8n_instance_api_key,
                commands::n8n_instance_commands::sync_workflows_from_n8n,
                commands::n8n_instance_commands::sync_executions_from_n8n,
                // Dashboard commands
                commands::get_dashboard_data,
                // Workflow commands
                commands::workflow_commands::create_workflow,
                commands::workflow_commands::get_workflow,
                commands::workflow_commands::list_workflows,
                commands::workflow_commands::update_workflow,
                commands::workflow_commands::delete_workflow,
                commands::workflow_commands::toggle_workflow,
                // Template commands
                commands::template_commands::create_template,
                commands::template_commands::get_template,
                commands::template_commands::list_templates,
                commands::template_commands::use_template,
                commands::template_commands::deploy_template_to_instance,
                // Execution commands
                commands::execution_commands::get_execution,
                commands::execution_commands::list_executions,
                commands::execution_commands::execute_workflow,
                // AI commands
                commands::ai_commands::debug_ai_test,
                commands::ai_commands::generate_workflow_with_ai,
                commands::ai_commands::chat_with_ai,
                commands::ai_commands::get_available_ai_providers,
                commands::ai_commands::test_ai_connection,
                commands::ai_commands::improve_workflow_with_ai,
                // System commands
                commands::system_commands::show_notification,
                commands::system_commands::get_system_info,
                commands::system_commands::minimize_to_tray,
                commands::system_commands::restore_from_tray,
                commands::system_commands::quit_app,
                commands::system_commands::enable_autostart,
                commands::system_commands::disable_autostart,
                commands::system_commands::is_autostart_enabled,
                commands::system_commands::notify_workflow_execution_started,
                commands::system_commands::notify_workflow_execution_completed,
                commands::system_commands::notify_n8n_instance_connected,
                commands::system_commands::notify_n8n_instance_disconnected,
                commands::system_commands::notify_sync_completed,
                commands::system_commands::notify_ai_workflow_generated,
                commands::system_commands::handle_tray_menu_action,
                commands::system_commands::get_performance_metrics,
                commands::system_commands::set_window_always_on_top,
                commands::system_commands::center_window,
                commands::system_commands::set_window_size,
                // Seed commands
                commands::seed_commands::seed_sample_templates,
                // Settings commands
                commands::settings_commands::get_app_settings,
                commands::settings_commands::update_app_settings,
                commands::settings_commands::reset_app_settings,
                commands::settings_commands::get_syncing_instances,
            ])
            .run(tauri::generate_context!())
            .expect("error while running tauri application");
    });
}

fn setup_system_tray(app: &tauri::AppHandle) -> tauri::Result<()> {
    println!("Setting up system tray...");

    // Create dynamic tray menu that updates based on window state
    let menu = create_tray_menu(app, true)?; // Start with window visible

    println!("Tray menu created successfully");

    // Create single tray icon with colored icon
    let _tray = TrayIconBuilder::with_id("main-tray")
        .menu(&menu)
        .tooltip("Workflow Manager Pro")
        .icon(app.default_window_icon().unwrap().clone())
        .on_tray_icon_event(|tray, event| {
            println!("Tray icon event received: {:?}", event);
            match event {
                TrayIconEvent::Click { button, button_state, .. } => {
                    println!("Tray click - Button: {:?}, State: {:?}", button, button_state);
                    if button == tauri::tray::MouseButton::Left && button_state == tauri::tray::MouseButtonState::Up {
                        // Left click - toggle window visibility
                        println!("Left click detected - toggling window");
                        toggle_window_visibility(tray.app_handle());
                    }
                }
                TrayIconEvent::DoubleClick { .. } => {
                    println!("Double click detected - showing window");
                    if let Some(window) = tray.app_handle().get_webview_window("main") {
                        let _ = window.show();
                        let _ = window.set_focus();
                        // Update menu to reflect new state
                        update_tray_menu(tray.app_handle(), false);
                    }
                }
                _ => {
                    println!("Other tray event: {:?}", event);
                }
            }
        })
        .on_menu_event(|app, event| {
            println!("Menu event triggered: {}", event.id().as_ref());
            match event.id().as_ref() {
                "toggle" => {
                    println!("Toggle menu item clicked");
                    toggle_window_visibility(app);
                }
                "about" => {
                    println!("About menu item clicked");
                    // Show About dialog - for now just log to console
                    // The native About dialog is handled by the system via Info.plist on macOS
                    let about_message = format!(
                        "About Workflow Manager Pro v{}\n\
                        Professional N8N workflow management and automation tool with AI-powered workflow generation.\n\
                        🤖 AI-Powered Workflow Generation | 🎨 Visual Workflow Designer\n\
                        🔗 Multi-Instance N8N Integration | 📊 Real-Time Execution Monitoring\n\
                        🔐 Enterprise-Grade Security | ⚡ High-Performance Architecture\n\
                        Built with Rust + React + TypeScript | Powered by Tauri v2 Framework\n\
                        © 2025 N8N Lab Team. All rights reserved.\n\
                        Website: https://n8nlab.app | Support: <EMAIL>",
                        app.package_info().version
                    );
                    println!("{}", about_message);
                }
                "quit" => {
                    println!("Quit menu item clicked");
                    app.exit(0);
                }
                _ => {
                    println!("Unknown menu item: {}", event.id().as_ref());
                }
            }
        })
        .build(app)?;

    println!("System tray setup completed successfully");
    Ok(())
}

fn create_tray_menu(app: &tauri::AppHandle, window_visible: bool) -> tauri::Result<tauri::menu::Menu<tauri::Wry>> {
    let toggle_text = if window_visible {
        "Hide to Tray"
    } else {
        "Show Workflow Manager Pro"
    };

    let toggle_item = MenuItemBuilder::with_id("toggle", toggle_text).build(app)?;
    let separator = PredefinedMenuItem::separator(app)?;
    let about_item = MenuItemBuilder::with_id("about", "About").build(app)?;
    let quit_item = MenuItemBuilder::with_id("quit", "Quit").build(app)?;

    MenuBuilder::new(app)
        .items(&[
            &toggle_item,
            &separator,
            &about_item,
            &separator,
            &quit_item,
        ])
        .build()
}

fn toggle_window_visibility(app: &tauri::AppHandle) {
    if let Some(window) = app.get_webview_window("main") {
        match window.is_visible() {
            Ok(true) => {
                println!("Window is visible, hiding it and removing from taskbar");
                hide_window_and_taskbar(&window);
                update_tray_menu(app, true); // Window is now hidden
            }
            Ok(false) => {
                println!("Window is hidden, showing it and adding back to taskbar");
                show_window_and_taskbar(&window);
                update_tray_menu(app, false); // Window is now visible
            }
            Err(e) => {
                println!("Error checking window visibility: {:?}", e);
                show_window_and_taskbar(&window);
                update_tray_menu(app, false); // Assume window is now visible
            }
        }
    } else {
        println!("Could not find main window");
    }
}

fn update_tray_menu(app: &tauri::AppHandle, window_hidden: bool) {
    if let Some(tray) = app.tray_by_id("main-tray") {
        if let Ok(new_menu) = create_tray_menu(app, !window_hidden) {
            let _ = tray.set_menu(Some(new_menu));
            println!("Tray menu updated - window hidden: {}", window_hidden);
        }
    }
}

fn setup_window_events(app: &tauri::AppHandle) -> tauri::Result<()> {
    println!("Setting up window event handlers...");

    // Get the main window
    if let Some(window) = app.get_webview_window("main") {
        let app_handle = app.clone();

        // Set up window event handler
        window.on_window_event(move |event| {
            match event {
                WindowEvent::CloseRequested { api, .. } => {
                    println!("Close button clicked - hiding window instead of exiting");

                    // Prevent the default close behavior
                    api.prevent_close();

                    // Hide the window and remove from taskbar
                    if let Some(window) = app_handle.get_webview_window("main") {
                        hide_window_and_taskbar(&window);
                        // Update tray menu to reflect hidden state
                        update_tray_menu(&app_handle, true);
                        println!("Window hidden to tray and removed from taskbar");
                    }
                }
                WindowEvent::Focused(focused) => {
                    if *focused {
                        println!("Window focused");
                        // Update tray menu when window becomes visible/focused
                        update_tray_menu(&app_handle, false);
                    }
                }
                _ => {}
            }
        });

        println!("Window event handlers setup completed");
    } else {
        println!("Warning: Could not find main window for event setup");
    }

    Ok(())
}
