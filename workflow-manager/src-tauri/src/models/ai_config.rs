use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use serde_json::Value;
use sqlx::FromRow;
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AiProvider {
    OpenA<PERSON>,
    Claude,
    DeepSeek,
    AIML,
    Local,
}

impl From<String> for AiProvider {
    fn from(s: String) -> Self {
        match s.as_str() {
            "openai" => AiProvider::OpenAI,
            "claude" => AiProvider::<PERSON>,
            "deepseek" => AiProvider::DeepSeek,
            "aiml" => AiProvider::AIML,
            "local" => AiProvider::Local,
            _ => AiProvider::OpenAI,
        }
    }
}

impl From<AiProvider> for String {
    fn from(provider: AiProvider) -> Self {
        match provider {
            AiProvider::OpenAI => "openai".to_string(),
            AiProvider::Claude => "claude".to_string(),
            AiProvider::DeepSeek => "deepseek".to_string(),
            AiProvider::AIML => "aiml".to_string(),
            AiProvider::Local => "local".to_string(),
        }
    }
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, FromRow)]
pub struct AiConfig {
    pub id: String,
    pub name: String,
    pub provider: String,
    pub model: String,
    pub api_key: Option<String>, // Will be stored in secure keychain
    pub base_url: Option<String>, // For local models or custom endpoints
    pub max_tokens: Option<i32>,
    pub temperature: Option<f32>,
    pub system_prompt: Option<String>,
    pub is_active: bool,
    pub is_default: bool,
    pub settings: Option<String>, // JSON for provider-specific settings
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub last_used: Option<DateTime<Utc>>,
    pub usage_count: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateAiConfig {
    pub name: String,
    pub provider: AiProvider,
    pub model: String,
    pub api_key: Option<String>,
    pub base_url: Option<String>,
    pub max_tokens: Option<i32>,
    pub temperature: Option<f32>,
    pub system_prompt: Option<String>,
    pub is_active: Option<bool>,
    pub is_default: Option<bool>,
    pub settings: Option<Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateAiConfig {
    pub name: Option<String>,
    pub model: Option<String>,
    pub api_key: Option<String>,
    pub base_url: Option<String>,
    pub max_tokens: Option<i32>,
    pub temperature: Option<f32>,
    pub system_prompt: Option<String>,
    pub is_active: Option<bool>,
    pub is_default: Option<bool>,
    pub settings: Option<Value>,
}

impl AiConfig {
    pub fn new(create_data: CreateAiConfig) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4().to_string(),
            name: create_data.name,
            provider: String::from(create_data.provider),
            model: create_data.model,
            api_key: None, // Will be handled by keychain
            base_url: create_data.base_url,
            max_tokens: create_data.max_tokens,
            temperature: create_data.temperature,
            system_prompt: create_data.system_prompt,
            is_active: create_data.is_active.unwrap_or(true),
            is_default: create_data.is_default.unwrap_or(false),
            settings: create_data.settings.map(|s| serde_json::to_string(&s).unwrap_or_default()),
            created_at: now,
            updated_at: now,
            last_used: None,
            usage_count: 0,
        }
    }

    pub fn get_provider(&self) -> AiProvider {
        self.provider.clone().into()
    }

    pub fn get_settings(&self) -> Option<Value> {
        self.settings
            .as_ref()
            .and_then(|s| serde_json::from_str(s).ok())
    }

    pub fn increment_usage(&mut self) {
        self.usage_count += 1;
        self.last_used = Some(Utc::now());
        self.updated_at = Utc::now();
    }

    pub fn update(&mut self, update_data: UpdateAiConfig) {
        if let Some(name) = update_data.name {
            self.name = name;
        }
        if let Some(model) = update_data.model {
            self.model = model;
        }
        if let Some(base_url) = update_data.base_url {
            self.base_url = Some(base_url);
        }
        if let Some(max_tokens) = update_data.max_tokens {
            self.max_tokens = Some(max_tokens);
        }
        if let Some(temperature) = update_data.temperature {
            self.temperature = Some(temperature);
        }
        if let Some(system_prompt) = update_data.system_prompt {
            self.system_prompt = Some(system_prompt);
        }
        if let Some(is_active) = update_data.is_active {
            self.is_active = is_active;
        }
        if let Some(is_default) = update_data.is_default {
            self.is_default = is_default;
        }
        if let Some(settings) = update_data.settings {
            self.settings = Some(serde_json::to_string(&settings).unwrap_or_default());
        }
        self.updated_at = Utc::now();
    }

    pub fn is_local_model(&self) -> bool {
        matches!(self.get_provider(), AiProvider::Local)
    }

    pub fn requires_api_key(&self) -> bool {
        !self.is_local_model()
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AiUsageStats {
    pub total_requests: i64,
    pub successful_requests: i64,
    pub failed_requests: i64,
    pub avg_response_time: Option<f64>,
    pub total_tokens_used: Option<i64>,
    pub success_rate: f64,
}
