use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, FromRow)]
pub struct AppSettings {
    pub id: String,
    pub auto_sync: bool,
    pub sync_interval: i32, // seconds
    pub notifications: bool,
    pub max_execution_history: i32,
    pub enable_analytics: bool,
    pub auto_backup: bool,
    pub backup_interval: i32, // hours
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateAppSettings {
    pub auto_sync: Option<bool>,
    pub sync_interval: Option<i32>,
    pub notifications: Option<bool>,
    pub max_execution_history: Option<i32>,
    pub enable_analytics: Option<bool>,
    pub auto_backup: Option<bool>,
    pub backup_interval: Option<i32>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UpdateAppSettings {
    pub auto_sync: Option<bool>,
    pub sync_interval: Option<i32>,
    pub notifications: Option<bool>,
    pub max_execution_history: Option<i32>,
    pub enable_analytics: Option<bool>,
    pub auto_backup: Option<bool>,
    pub backup_interval: Option<i32>,
}

impl AppSettings {
    pub fn new(create_data: CreateAppSettings) -> Self {
        let now = Utc::now();
        Self {
            id: "default".to_string(), // Single settings record
            auto_sync: create_data.auto_sync.unwrap_or(true),
            sync_interval: create_data.sync_interval.unwrap_or(30),
            notifications: create_data.notifications.unwrap_or(true),
            max_execution_history: create_data.max_execution_history.unwrap_or(1000),
            enable_analytics: create_data.enable_analytics.unwrap_or(false),
            auto_backup: create_data.auto_backup.unwrap_or(true),
            backup_interval: create_data.backup_interval.unwrap_or(24),
            created_at: now,
            updated_at: now,
        }
    }

    pub fn default() -> Self {
        Self::new(CreateAppSettings {
            auto_sync: Some(true),
            sync_interval: Some(30),
            notifications: Some(true),
            max_execution_history: Some(1000),
            enable_analytics: Some(false),
            auto_backup: Some(true),
            backup_interval: Some(24),
        })
    }
}
