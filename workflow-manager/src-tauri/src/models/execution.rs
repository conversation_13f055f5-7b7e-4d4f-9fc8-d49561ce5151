use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use serde_json::Value;
use sqlx::FromRow;
use uuid::Uuid;
use super::ExecutionStatus;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, FromRow)]
pub struct Execution {
    pub id: String,
    pub workflow_id: String,
    pub n8n_execution_id: String,
    pub status: String,
    pub mode: String, // manual, trigger, retry, etc.
    pub started_at: DateTime<Utc>,
    pub finished_at: Option<DateTime<Utc>>,
    pub duration: Option<i64>, // milliseconds
    pub data: Option<String>, // JSON execution data as string
    pub error: Option<String>, // Error message if failed
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateExecution {
    pub workflow_id: String,
    pub n8n_execution_id: String,
    pub mode: String,
    pub started_at: DateTime<Utc>,
    pub data: Option<Value>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UpdateExecution {
    pub status: Option<ExecutionStatus>,
    pub finished_at: Option<DateTime<Utc>>,
    pub duration: Option<i64>,
    pub data: Option<Value>,
    pub error: Option<String>,
}

impl Execution {
    pub fn new(create_data: CreateExecution) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4().to_string(),
            workflow_id: create_data.workflow_id,
            n8n_execution_id: create_data.n8n_execution_id,
            status: String::from(ExecutionStatus::Running),
            mode: create_data.mode,
            started_at: create_data.started_at,
            finished_at: None,
            duration: None,
            data: create_data.data.map(|d| serde_json::to_string(&d).unwrap_or_default()),
            error: None,
            created_at: now,
            updated_at: now,
        }
    }

    pub fn get_status(&self) -> ExecutionStatus {
        self.status.clone().into()
    }

    pub fn set_status(&mut self, status: ExecutionStatus) {
        self.status = String::from(status);
        self.updated_at = Utc::now();
    }

    pub fn get_data(&self) -> Option<Value> {
        self.data
            .as_ref()
            .and_then(|d| serde_json::from_str(d).ok())
    }

    pub fn complete_success(&mut self, data: Option<Value>) {
        let now = Utc::now();
        self.finished_at = Some(now);
        self.duration = Some((now - self.started_at).num_milliseconds());
        self.data = data.map(|d| serde_json::to_string(&d).unwrap_or_default());
        self.set_status(ExecutionStatus::Success);
    }

    pub fn complete_failed(&mut self, error: String, data: Option<Value>) {
        let now = Utc::now();
        self.finished_at = Some(now);
        self.duration = Some((now - self.started_at).num_milliseconds());
        self.error = Some(error);
        self.data = data.map(|d| serde_json::to_string(&d).unwrap_or_default());
        self.set_status(ExecutionStatus::Failed);
    }

    pub fn cancel(&mut self) {
        let now = Utc::now();
        self.finished_at = Some(now);
        self.duration = Some((now - self.started_at).num_milliseconds());
        self.set_status(ExecutionStatus::Cancelled);
    }

    pub fn get_duration_seconds(&self) -> Option<f64> {
        self.duration.map(|d| d as f64 / 1000.0)
    }

    pub fn is_running(&self) -> bool {
        matches!(self.get_status(), ExecutionStatus::Running | ExecutionStatus::Waiting)
    }

    pub fn is_completed(&self) -> bool {
        matches!(
            self.get_status(),
            ExecutionStatus::Success | ExecutionStatus::Failed | ExecutionStatus::Cancelled
        )
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionSummary {
    pub total: i64,
    pub success: i64,
    pub failed: i64,
    pub running: i64,
    pub cancelled: i64,
    pub success_rate: f64,
    pub avg_duration: Option<f64>,
}

impl ExecutionSummary {
    pub fn new(
        total: i64,
        success: i64,
        failed: i64,
        running: i64,
        cancelled: i64,
        avg_duration: Option<f64>,
    ) -> Self {
        let success_rate = if total > 0 {
            (success as f64 / total as f64) * 100.0
        } else {
            0.0
        };

        Self {
            total,
            success,
            failed,
            running,
            cancelled,
            success_rate,
            avg_duration,
        }
    }
}
