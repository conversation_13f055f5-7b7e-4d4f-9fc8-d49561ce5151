use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

pub mod n8n_instance;
pub mod workflow;
pub mod execution;
pub mod template;
pub mod ai_config;
pub mod app_settings;

pub use n8n_instance::*;
pub use workflow::*;
pub use execution::*;
pub use template::*;
pub use ai_config::*;
pub use app_settings::*;

// Common types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Status {
    Active,
    Inactive,
    Error,
}

impl From<String> for Status {
    fn from(s: String) -> Self {
        match s.as_str() {
            "active" => Status::Active,
            "inactive" => Status::Inactive,
            "error" => Status::Error,
            _ => Status::Inactive,
        }
    }
}

impl From<Status> for String {
    fn from(status: Status) -> Self {
        match status {
            Status::Active => "active".to_string(),
            Status::Inactive => "inactive".to_string(),
            Status::Error => "error".to_string(),
        }
    }
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum ExecutionStatus {
    Running,
    Success,
    Failed,
    Waiting,
    Cancelled,
}

impl From<String> for ExecutionStatus {
    fn from(s: String) -> Self {
        match s.as_str() {
            "running" => ExecutionStatus::Running,
            "success" => ExecutionStatus::Success,
            "failed" => ExecutionStatus::Failed,
            "waiting" => ExecutionStatus::Waiting,
            "cancelled" => ExecutionStatus::Cancelled,
            _ => ExecutionStatus::Failed,
        }
    }
}

impl From<ExecutionStatus> for String {
    fn from(status: ExecutionStatus) -> Self {
        match status {
            ExecutionStatus::Running => "running".to_string(),
            ExecutionStatus::Success => "success".to_string(),
            ExecutionStatus::Failed => "failed".to_string(),
            ExecutionStatus::Waiting => "waiting".to_string(),
            ExecutionStatus::Cancelled => "cancelled".to_string(),
        }
    }
}
