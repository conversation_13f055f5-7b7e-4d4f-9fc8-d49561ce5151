use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;
use super::Status;

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct N8nInstance {
    pub id: String,
    pub name: String,
    pub url: String,
    pub api_key: Option<String>, // Will be stored in secure keychain
    pub status: String,
    pub version: Option<String>,
    pub description: Option<String>,
    pub is_default: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub last_ping: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateN8nInstance {
    pub name: String,
    pub url: String,
    pub api_key: String,
    pub description: Option<String>,
    pub is_default: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateN8nInstance {
    pub name: Option<String>,
    pub url: Option<String>,
    pub api_key: Option<String>,
    pub description: Option<String>,
    pub is_default: Option<bool>,
    pub status: Option<Status>,
}

impl N8nInstance {
    pub fn new(create_data: CreateN8nInstance) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4().to_string(),
            name: create_data.name,
            url: create_data.url,
            api_key: None, // Will be handled by keychain
            status: String::from(Status::Inactive),
            version: None,
            description: create_data.description,
            is_default: create_data.is_default.unwrap_or(false),
            created_at: now,
            updated_at: now,
            last_ping: None,
        }
    }

    pub fn get_status(&self) -> Status {
        self.status.clone().into()
    }

    pub fn set_status(&mut self, status: Status) {
        self.status = String::from(status);
        self.updated_at = Utc::now();
    }

    pub fn ping_success(&mut self, version: Option<String>) {
        self.last_ping = Some(Utc::now());
        self.version = version;
        self.set_status(Status::Active);
    }

    pub fn ping_failed(&mut self) {
        self.last_ping = Some(Utc::now());
        self.set_status(Status::Error);
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct N8nInstanceWithStats {
    #[serde(flatten)]
    pub instance: N8nInstance,
    pub workflow_count: i64,
    pub active_workflow_count: i64,
    pub execution_count: i64,
    pub last_execution: Option<DateTime<Utc>>,
}
