use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use serde_json::Value;
use sqlx::FromRow;
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Template {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub category: String,
    pub tags: Option<String>, // JSON array as string
    pub nodes: String, // JSON as string
    pub connections: String, // JSON as string
    pub settings: Option<String>, // JSON as string
    pub static_data: Option<String>, // JSON as string
    pub variables: Option<String>, // JSON object for template variables
    pub is_public: bool,
    pub usage_count: i64,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub created_by: Option<String>, // AI model or user identifier
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateTemplate {
    pub name: String,
    pub description: Option<String>,
    pub category: String,
    pub tags: Option<Vec<String>>,
    pub nodes: Value,
    pub connections: Value,
    pub settings: Option<Value>,
    pub static_data: Option<Value>,
    pub variables: Option<Value>,
    pub is_public: Option<bool>,
    pub created_by: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateTemplate {
    pub name: Option<String>,
    pub description: Option<String>,
    pub category: Option<String>,
    pub tags: Option<Vec<String>>,
    pub nodes: Option<Value>,
    pub connections: Option<Value>,
    pub settings: Option<Value>,
    pub static_data: Option<Value>,
    pub variables: Option<Value>,
    pub is_public: Option<bool>,
}

impl Template {
    pub fn new(create_data: CreateTemplate) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4().to_string(),
            name: create_data.name,
            description: create_data.description,
            category: create_data.category,
            tags: create_data.tags.map(|t| serde_json::to_string(&t).unwrap_or_default()),
            nodes: serde_json::to_string(&create_data.nodes).unwrap_or_default(),
            connections: serde_json::to_string(&create_data.connections).unwrap_or_default(),
            settings: create_data.settings.map(|s| serde_json::to_string(&s).unwrap_or_default()),
            static_data: create_data.static_data.map(|sd| serde_json::to_string(&sd).unwrap_or_default()),
            variables: create_data.variables.map(|v| serde_json::to_string(&v).unwrap_or_default()),
            is_public: create_data.is_public.unwrap_or(false),
            usage_count: 0,
            created_at: now,
            updated_at: now,
            created_by: create_data.created_by,
        }
    }

    pub fn get_tags(&self) -> Vec<String> {
        self.tags
            .as_ref()
            .and_then(|t| serde_json::from_str(t).ok())
            .unwrap_or_default()
    }

    pub fn get_nodes(&self) -> Option<Value> {
        serde_json::from_str(&self.nodes).ok()
    }

    pub fn get_connections(&self) -> Option<Value> {
        serde_json::from_str(&self.connections).ok()
    }

    pub fn get_settings(&self) -> Option<Value> {
        self.settings
            .as_ref()
            .and_then(|s| serde_json::from_str(s).ok())
    }

    pub fn get_static_data(&self) -> Option<Value> {
        self.static_data
            .as_ref()
            .and_then(|sd| serde_json::from_str(sd).ok())
    }

    pub fn get_variables(&self) -> Option<Value> {
        self.variables
            .as_ref()
            .and_then(|v| serde_json::from_str(v).ok())
    }

    pub fn increment_usage(&mut self) {
        self.usage_count += 1;
        self.updated_at = Utc::now();
    }

    pub fn update(&mut self, update_data: UpdateTemplate) {
        if let Some(name) = update_data.name {
            self.name = name;
        }
        if let Some(description) = update_data.description {
            self.description = Some(description);
        }
        if let Some(category) = update_data.category {
            self.category = category;
        }
        if let Some(tags) = update_data.tags {
            self.tags = Some(serde_json::to_string(&tags).unwrap_or_default());
        }
        if let Some(nodes) = update_data.nodes {
            self.nodes = serde_json::to_string(&nodes).unwrap_or_default();
        }
        if let Some(connections) = update_data.connections {
            self.connections = serde_json::to_string(&connections).unwrap_or_default();
        }
        if let Some(settings) = update_data.settings {
            self.settings = Some(serde_json::to_string(&settings).unwrap_or_default());
        }
        if let Some(static_data) = update_data.static_data {
            self.static_data = Some(serde_json::to_string(&static_data).unwrap_or_default());
        }
        if let Some(variables) = update_data.variables {
            self.variables = Some(serde_json::to_string(&variables).unwrap_or_default());
        }
        if let Some(is_public) = update_data.is_public {
            self.is_public = is_public;
        }
        self.updated_at = Utc::now();
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TemplateCategory {
    pub name: String,
    pub count: i64,
    pub description: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TemplateWithUsage {
    #[serde(flatten)]
    pub template: Template,
    pub recent_usage: Vec<DateTime<Utc>>,
    pub popularity_score: f64,
}
