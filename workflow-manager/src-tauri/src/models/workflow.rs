use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use serde_json::Value;
use sqlx::FromRow;
use uuid::Uuid;
use super::Status;

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Workflow {
    pub id: String,
    pub n8n_instance_id: String,
    pub n8n_workflow_id: String,
    pub name: String,
    pub description: Option<String>,
    pub status: String,
    pub is_active: bool,
    pub tags: Option<String>, // JSON array as string
    pub nodes: Option<String>, // JSON as string
    pub connections: Option<String>, // JSON as string
    pub settings: Option<String>, // JSON as string
    pub static_data: Option<String>, // JSON as string
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub last_execution: Option<DateTime<Utc>>,
    pub execution_count: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateWorkflow {
    pub n8n_instance_id: String,
    pub name: String,
    pub description: Option<String>,
    pub nodes: Option<Value>,
    pub connections: Option<Value>,
    pub settings: Option<Value>,
    pub static_data: Option<Value>,
    pub tags: Option<Vec<String>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateWorkflow {
    pub name: Option<String>,
    pub description: Option<String>,
    pub status: Option<Status>,
    pub is_active: Option<bool>,
    pub nodes: Option<Value>,
    pub connections: Option<Value>,
    pub settings: Option<Value>,
    pub static_data: Option<Value>,
    pub tags: Option<Vec<String>>,
}

impl Workflow {
    pub fn new(create_data: CreateWorkflow, n8n_workflow_id: String) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4().to_string(),
            n8n_instance_id: create_data.n8n_instance_id,
            n8n_workflow_id,
            name: create_data.name,
            description: create_data.description,
            status: String::from(Status::Inactive),
            is_active: false,
            tags: create_data.tags.map(|t| serde_json::to_string(&t).unwrap_or_default()),
            nodes: create_data.nodes.map(|n| serde_json::to_string(&n).unwrap_or_default()),
            connections: create_data.connections.map(|c| serde_json::to_string(&c).unwrap_or_default()),
            settings: create_data.settings.map(|s| serde_json::to_string(&s).unwrap_or_default()),
            static_data: create_data.static_data.map(|sd| serde_json::to_string(&sd).unwrap_or_default()),
            created_at: now,
            updated_at: now,
            last_execution: None,
            execution_count: 0,
        }
    }

    pub fn get_status(&self) -> Status {
        self.status.clone().into()
    }

    pub fn set_status(&mut self, status: Status) {
        self.status = String::from(status);
        self.updated_at = Utc::now();
    }

    pub fn get_tags(&self) -> Vec<String> {
        self.tags
            .as_ref()
            .and_then(|t| serde_json::from_str(t).ok())
            .unwrap_or_default()
    }

    pub fn get_nodes(&self) -> Option<Value> {
        self.nodes
            .as_ref()
            .and_then(|n| serde_json::from_str(n).ok())
    }

    pub fn get_connections(&self) -> Option<Value> {
        self.connections
            .as_ref()
            .and_then(|c| serde_json::from_str(c).ok())
    }

    pub fn get_settings(&self) -> Option<Value> {
        self.settings
            .as_ref()
            .and_then(|s| serde_json::from_str(s).ok())
    }

    pub fn get_static_data(&self) -> Option<Value> {
        self.static_data
            .as_ref()
            .and_then(|sd| serde_json::from_str(sd).ok())
    }

    pub fn increment_execution_count(&mut self) {
        self.execution_count += 1;
        self.last_execution = Some(Utc::now());
        self.updated_at = Utc::now();
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkflowWithStats {
    #[serde(flatten)]
    pub workflow: Workflow,
    pub recent_executions: Vec<String>, // Execution IDs
    pub success_rate: f64,
    pub avg_execution_time: Option<f64>,
}
