use reqwest::{Client, header::{<PERSON>er<PERSON>ap, HeaderValue}};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use crate::models::{Workflow, Execution};

#[derive(Debug, Clone)]
pub struct N8nApiClient {
    client: Client,
    base_url: String,
    api_key: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct N8nWorkflow {
    pub id: String,
    pub name: String,
    pub active: bool,
    pub nodes: serde_json::Value,
    pub connections: serde_json::Value,
    pub settings: Option<serde_json::Value>,
    #[serde(rename = "staticData")]
    pub static_data: Option<serde_json::Value>,
    #[serde(rename = "createdAt")]
    pub created_at: String,
    #[serde(rename = "updatedAt")]
    pub updated_at: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct N8nExecution {
    pub id: String,
    #[serde(rename = "workflowId")]
    pub workflow_id: String,
    pub status: String,
    #[serde(rename = "startedAt")]
    pub started_at: String,
    #[serde(rename = "finishedAt")]
    pub finished_at: Option<String>,
    pub mode: String,
    pub data: Option<serde_json::Value>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct N8nExecutionsList {
    pub data: Vec<N8nExecution>,
    #[serde(rename = "nextCursor")]
    pub next_cursor: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct N8nWorkflowsList {
    pub data: Vec<N8nWorkflow>,
    #[serde(rename = "nextCursor")]
    pub next_cursor: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct N8nHealthCheck {
    pub status: String,
    pub version: Option<String>,
}

impl N8nApiClient {
    pub fn new(base_url: String, api_key: String) -> Result<Self, String> {
        let mut headers = HeaderMap::new();
        headers.insert(
            "X-N8N-API-KEY",
            HeaderValue::from_str(&api_key)
                .map_err(|e| format!("Invalid API key format: {}", e))?,
        );
        headers.insert("Content-Type", HeaderValue::from_static("application/json"));

        let client = Client::builder()
            .default_headers(headers)
            .timeout(std::time::Duration::from_secs(30))
            .danger_accept_invalid_certs(true) // Allow self-signed certificates for development
            .build()
            .map_err(|e| format!("Failed to create HTTP client: {}", e))?;

        Ok(Self {
            client,
            base_url: base_url.trim_end_matches('/').to_string(),
            api_key,
        })
    }

    pub async fn health_check(&self) -> Result<N8nHealthCheck, String> {
        let url = format!("{}/healthz", self.base_url);
        println!("Health check URL: {}", url);

        let response = self.client
            .get(&url)
            .send()
            .await
            .map_err(|e| {
                println!("Health check request error: {}", e);
                format!("Health check request failed: {}", e)
            })?;

        println!("Health check response status: {}", response.status());

        if response.status().is_success() {
            let response_text = response
                .text()
                .await
                .map_err(|e| format!("Failed to read response text: {}", e))?;

            println!("Health check response body: {}", response_text);

            // Try to parse as JSON
            serde_json::from_str::<N8nHealthCheck>(&response_text)
                .map_err(|e| format!("Failed to parse health check response: {}", e))
        } else {
            Err(format!("Health check failed with status: {}", response.status()))
        }
    }

    pub async fn list_workflows(&self, limit: Option<u32>) -> Result<N8nWorkflowsList, String> {
        let mut url = format!("{}/api/v1/workflows", self.base_url);
        
        if let Some(limit) = limit {
            url.push_str(&format!("?limit={}", limit));
        }

        let response = self.client
            .get(&url)
            .send()
            .await
            .map_err(|e| format!("Failed to fetch workflows: {}", e))?;

        if response.status().is_success() {
            response
                .json::<N8nWorkflowsList>()
                .await
                .map_err(|e| format!("Failed to parse workflows response: {}", e))
        } else {
            Err(format!("Failed to fetch workflows: {}", response.status()))
        }
    }

    pub async fn get_workflow(&self, workflow_id: &str) -> Result<N8nWorkflow, String> {
        let url = format!("{}/api/v1/workflows/{}", self.base_url, workflow_id);

        let response = self.client
            .get(&url)
            .send()
            .await
            .map_err(|e| format!("Failed to fetch workflow: {}", e))?;

        if response.status().is_success() {
            response
                .json::<N8nWorkflow>()
                .await
                .map_err(|e| format!("Failed to parse workflow response: {}", e))
        } else {
            Err(format!("Failed to fetch workflow: {}", response.status()))
        }
    }

    pub async fn list_executions(&self, workflow_id: Option<&str>, limit: Option<u32>) -> Result<N8nExecutionsList, String> {
        let mut url = format!("{}/api/v1/executions", self.base_url);
        let mut params = Vec::new();

        if let Some(workflow_id) = workflow_id {
            params.push(format!("workflowId={}", workflow_id));
        }
        if let Some(limit) = limit {
            params.push(format!("limit={}", limit));
        }

        if !params.is_empty() {
            url.push_str(&format!("?{}", params.join("&")));
        }

        let response = self.client
            .get(&url)
            .send()
            .await
            .map_err(|e| format!("Failed to fetch executions: {}", e))?;

        if response.status().is_success() {
            response
                .json::<N8nExecutionsList>()
                .await
                .map_err(|e| format!("Failed to parse executions response: {}", e))
        } else {
            Err(format!("Failed to fetch executions: {}", response.status()))
        }
    }

    pub async fn get_execution(&self, execution_id: &str) -> Result<N8nExecution, String> {
        let url = format!("{}/api/v1/executions/{}", self.base_url, execution_id);

        let response = self.client
            .get(&url)
            .send()
            .await
            .map_err(|e| format!("Failed to fetch execution: {}", e))?;

        if response.status().is_success() {
            response
                .json::<N8nExecution>()
                .await
                .map_err(|e| format!("Failed to parse execution response: {}", e))
        } else {
            Err(format!("Failed to fetch execution: {}", response.status()))
        }
    }

    pub async fn create_workflow(&self, workflow_data: serde_json::Value) -> Result<N8nWorkflow, String> {
        let url = format!("{}/api/v1/workflows", self.base_url);

        let response = self.client
            .post(&url)
            .json(&workflow_data)
            .send()
            .await
            .map_err(|e| format!("Failed to create workflow: {}", e))?;

        if response.status().is_success() {
            response
                .json::<N8nWorkflow>()
                .await
                .map_err(|e| format!("Failed to parse create workflow response: {}", e))
        } else {
            Err(format!("Failed to create workflow: {}", response.status()))
        }
    }

    pub async fn update_workflow(&self, workflow_id: &str, workflow_data: serde_json::Value) -> Result<N8nWorkflow, String> {
        let url = format!("{}/api/v1/workflows/{}", self.base_url, workflow_id);

        let response = self.client
            .put(&url)
            .json(&workflow_data)
            .send()
            .await
            .map_err(|e| format!("Failed to update workflow: {}", e))?;

        if response.status().is_success() {
            response
                .json::<N8nWorkflow>()
                .await
                .map_err(|e| format!("Failed to parse update workflow response: {}", e))
        } else {
            Err(format!("Failed to update workflow: {}", response.status()))
        }
    }

    pub async fn delete_workflow(&self, workflow_id: &str) -> Result<bool, String> {
        let url = format!("{}/api/v1/workflows/{}", self.base_url, workflow_id);

        let response = self.client
            .delete(&url)
            .send()
            .await
            .map_err(|e| format!("Failed to delete workflow: {}", e))?;

        Ok(response.status().is_success())
    }

    pub async fn execute_workflow(&self, workflow_id: &str, input_data: Option<serde_json::Value>) -> Result<N8nExecution, String> {
        let url = format!("{}/api/v1/workflows/{}/execute", self.base_url, workflow_id);

        let mut body = serde_json::json!({});
        if let Some(data) = input_data {
            body["data"] = data;
        }

        let response = self.client
            .post(&url)
            .json(&body)
            .send()
            .await
            .map_err(|e| format!("Failed to execute workflow: {}", e))?;

        if response.status().is_success() {
            response
                .json::<N8nExecution>()
                .await
                .map_err(|e| format!("Failed to parse execution response: {}", e))
        } else {
            Err(format!("Failed to execute workflow: {}", response.status()))
        }
    }
}

// Helper function to convert N8N workflow to our internal model
pub fn n8n_workflow_to_model(n8n_workflow: &N8nWorkflow, instance_id: &str) -> Workflow {
    use chrono::{DateTime, Utc};

    Workflow {
        id: uuid::Uuid::new_v4().to_string(),
        n8n_instance_id: instance_id.to_string(),
        n8n_workflow_id: n8n_workflow.id.clone(),
        name: n8n_workflow.name.clone(),
        description: None, // N8N doesn't have a separate description field
        status: "active".to_string(),
        is_active: n8n_workflow.active,
        tags: None, // N8N doesn't have tags in the basic workflow structure
        nodes: Some(serde_json::to_string(&n8n_workflow.nodes).unwrap_or_default()),
        connections: Some(serde_json::to_string(&n8n_workflow.connections).unwrap_or_default()),
        settings: n8n_workflow.settings.as_ref().map(|s| serde_json::to_string(s).unwrap_or_default()),
        static_data: n8n_workflow.static_data.as_ref().map(|s| serde_json::to_string(s).unwrap_or_default()),
        created_at: DateTime::parse_from_rfc3339(&n8n_workflow.created_at)
            .map(|dt| dt.with_timezone(&Utc))
            .unwrap_or_else(|_| Utc::now()),
        updated_at: DateTime::parse_from_rfc3339(&n8n_workflow.updated_at)
            .map(|dt| dt.with_timezone(&Utc))
            .unwrap_or_else(|_| Utc::now()),
        last_execution: None,
        execution_count: 0,
    }
}

// Helper function to convert N8N execution to our internal model
pub fn n8n_execution_to_model(n8n_execution: &N8nExecution, _instance_id: &str) -> Execution {
    use chrono::{DateTime, Utc};

    Execution {
        id: uuid::Uuid::new_v4().to_string(),
        workflow_id: n8n_execution.workflow_id.clone(),
        n8n_execution_id: n8n_execution.id.clone(),
        status: n8n_execution.status.clone(),
        mode: n8n_execution.mode.clone(),
        started_at: DateTime::parse_from_rfc3339(&n8n_execution.started_at)
            .map(|dt| dt.with_timezone(&Utc))
            .unwrap_or_else(|_| Utc::now()),
        finished_at: n8n_execution.finished_at.as_ref().and_then(|dt| {
            DateTime::parse_from_rfc3339(dt)
                .map(|dt| dt.with_timezone(&Utc))
                .ok()
        }),
        duration: None, // Will be calculated
        data: n8n_execution.data.as_ref().map(|d| serde_json::to_string(d).unwrap_or_default()),
        error: None,
        created_at: Utc::now(),
        updated_at: Utc::now(),
    }
}
