use std::sync::Arc;
use std::time::Duration;
use std::collections::HashSet;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, Emitter};
use tokio::sync::RwLock;
use tokio::time::interval;
use crate::{AppState, models::*, database::repositories::Repository};
use crate::n8n_api::{N8nApiClient, n8n_workflow_to_model, n8n_execution_to_model};
use crate::credentials::CredentialManager;

pub struct AutoSyncService {
    app_handle: AppHandle,
    is_running: Arc<RwLock<bool>>,
    syncing_instances: Arc<RwLock<HashSet<String>>>,
}

impl AutoSyncService {
    pub fn new(app_handle: AppHandle) -> Self {
        Self {
            app_handle,
            is_running: Arc::new(RwLock::new(false)),
            syncing_instances: Arc::new(RwLock::new(HashSet::new())),
        }
    }

    pub async fn get_syncing_instances(&self) -> HashSet<String> {
        self.syncing_instances.read().await.clone()
    }

    pub async fn start(&self) -> Result<(), String> {
        let mut is_running = self.is_running.write().await;
        if *is_running {
            return Ok(()); // Already running
        }
        *is_running = true;
        drop(is_running);

        let app_handle = self.app_handle.clone();
        let is_running = self.is_running.clone();
        let syncing_instances = self.syncing_instances.clone();

        tokio::spawn(async move {
            println!("Auto sync service started");
            
            loop {
                // Check if we should stop
                if !*is_running.read().await {
                    println!("Auto sync service stopped");
                    break;
                }

                // Get current settings
                let state = app_handle.state::<AppState>();
                let repositories = &state.repositories;
                
                match repositories.app_settings.get_settings().await {
                    Ok(settings) => {
                        if settings.auto_sync {
                            println!("Running automatic sync (interval: {}s)", settings.sync_interval);

                            // Perform sync for all active instances
                            if let Err(e) = Self::sync_all_instances(&app_handle, &syncing_instances).await {
                                eprintln!("Auto sync failed: {}", e);
                            }
                            
                            // Wait for the configured interval
                            let mut interval_timer = interval(Duration::from_secs(settings.sync_interval as u64));
                            interval_timer.tick().await; // Skip the first immediate tick
                            interval_timer.tick().await; // Wait for the actual interval
                        } else {
                            // Auto sync is disabled, wait a bit and check again
                            tokio::time::sleep(Duration::from_secs(10)).await;
                        }
                    }
                    Err(e) => {
                        eprintln!("Failed to get settings for auto sync: {}", e);
                        tokio::time::sleep(Duration::from_secs(30)).await;
                    }
                }
            }
        });

        Ok(())
    }

    pub async fn stop(&self) {
        let mut is_running = self.is_running.write().await;
        *is_running = false;
        println!("Auto sync service stop requested");
    }

    pub async fn is_running(&self) -> bool {
        *self.is_running.read().await
    }

    async fn sync_all_instances(app_handle: &AppHandle, syncing_instances: &Arc<RwLock<HashSet<String>>>) -> Result<(), String> {
        let state = app_handle.state::<AppState>();
        let repositories = &state.repositories;
        let credential_service = &state.credential_service;

        // Get all active N8N instances
        let instances = repositories
            .n8n_instances
            .list(None, None)
            .await
            .map_err(|e| e.to_string())?;

        let active_instances: Vec<_> = instances
            .into_iter()
            .filter(|instance| instance.status.to_lowercase() == "active")
            .collect();

        if active_instances.is_empty() {
            println!("No active N8N instances found for auto sync");
            return Ok(());
        }

        println!("Auto syncing {} active instances", active_instances.len());

        let mut total_workflows = 0;
        let mut total_executions = 0;

        for instance in active_instances {
            println!("Auto syncing instance: {} ({})", instance.name, instance.url);

            // Mark instance as syncing
            {
                let mut syncing = syncing_instances.write().await;
                syncing.insert(instance.id.clone());
            }

            // Sync workflows
            match Self::sync_instance_workflows(&instance, repositories, credential_service).await {
                Ok(workflow_count) => {
                    total_workflows += workflow_count;
                    println!("Synced {} workflows from {}", workflow_count, instance.name);
                }
                Err(e) => {
                    eprintln!("Failed to sync workflows from {}: {}", instance.name, e);
                }
            }

            // Sync executions
            match Self::sync_instance_executions(&instance, repositories, credential_service).await {
                Ok(execution_count) => {
                    total_executions += execution_count;
                    println!("Synced {} executions from {}", execution_count, instance.name);
                }
                Err(e) => {
                    eprintln!("Failed to sync executions from {}: {}", instance.name, e);
                }
            }

            // Mark instance as no longer syncing
            {
                let mut syncing = syncing_instances.write().await;
                syncing.remove(&instance.id);
            }
        }

        println!("Auto sync completed: {} workflows, {} executions", total_workflows, total_executions);

        // Send notification if enabled
        if total_workflows > 0 || total_executions > 0 {
            if let Err(e) = crate::commands::system_commands::notify_sync_completed(
                app_handle.clone(),
                total_executions,
            ).await {
                eprintln!("Failed to send sync notification: {}", e);
            }
        }

        // Emit sync completed event to update dashboard
        if let Err(e) = app_handle.emit_to("main", "sync_completed", serde_json::json!({
            "total_workflows": total_workflows,
            "total_executions": total_executions,
            "timestamp": chrono::Utc::now().to_rfc3339()
        })) {
            eprintln!("Failed to emit sync completed event: {}", e);
        }

        Ok(())
    }

    async fn sync_instance_workflows(
        instance: &N8nInstance,
        repositories: &crate::database::repositories::Repositories,
        credential_service: &crate::credentials::service::CredentialService,
    ) -> Result<usize, String> {
        // Get API key
        let credential_id = format!("n8n_instance_{}", instance.id);
        let api_key = credential_service
            .retrieve_credential(&credential_id)
            .await
            .map_err(|e| format!("Failed to get API key: {}", e))?
            .ok_or("API key not found")?
            .value
            .expose_secret()
            .to_string();

        // Create API client
        let client = N8nApiClient::new(instance.url.clone(), api_key)
            .map_err(|e| format!("Failed to create N8N API client: {}", e))?;

        // Fetch workflows from N8N
        let n8n_workflows_list = client
            .list_workflows(None)
            .await
            .map_err(|e| format!("Failed to fetch workflows: {}", e))?;

        // Convert and store workflows
        let mut stored_count = 0;
        for n8n_workflow in n8n_workflows_list.data {
            let workflow = n8n_workflow_to_model(&n8n_workflow, &instance.id);
            
            // Check if workflow already exists
            if let Ok(Some(_)) = repositories.workflows.get_by_id(&workflow.id).await {
                // Update existing workflow
                if repositories.workflows.update(&workflow).await.is_ok() {
                    stored_count += 1;
                }
            } else {
                // Create new workflow
                if repositories.workflows.create(&workflow).await.is_ok() {
                    stored_count += 1;
                }
            }
        }

        Ok(stored_count)
    }

    async fn sync_instance_executions(
        instance: &N8nInstance,
        repositories: &crate::database::repositories::Repositories,
        credential_service: &crate::credentials::service::CredentialService,
    ) -> Result<usize, String> {
        // Get API key
        let credential_id = format!("n8n_instance_{}", instance.id);
        let api_key = credential_service
            .retrieve_credential(&credential_id)
            .await
            .map_err(|e| format!("Failed to get API key: {}", e))?
            .ok_or("API key not found")?
            .value
            .expose_secret()
            .to_string();

        // Create API client
        let client = N8nApiClient::new(instance.url.clone(), api_key)
            .map_err(|e| format!("Failed to create N8N API client: {}", e))?;

        // Fetch recent executions from N8N (limit to 100 for auto sync)
        let n8n_executions_list = client
            .list_executions(None, Some(100))
            .await
            .map_err(|e| format!("Failed to fetch executions: {}", e))?;

        // Convert and store executions
        let mut stored_count = 0;
        for n8n_execution in n8n_executions_list.data {
            let execution = n8n_execution_to_model(&n8n_execution, &instance.id);
            
            // Check if execution already exists
            if let Ok(Some(_)) = repositories.executions.get_by_id(&execution.id).await {
                // Update existing execution
                if repositories.executions.update(&execution).await.is_ok() {
                    stored_count += 1;
                }
            } else {
                // Create new execution
                if repositories.executions.create(&execution).await.is_ok() {
                    stored_count += 1;
                }
            }
        }

        Ok(stored_count)
    }
}
