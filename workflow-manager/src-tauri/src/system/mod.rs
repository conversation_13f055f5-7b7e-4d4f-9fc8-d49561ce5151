use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>ry};
use tauri_plugin_notification::{NotificationExt, PermissionState};
use std::collections::HashMap;

pub struct SystemManager {
    app_handle: AppHandle<Wry>,
}

#[derive(Debug, <PERSON>lone, serde::Serialize)]
pub struct SystemNotification {
    pub title: String,
    pub body: String,
    pub icon: Option<String>,
    pub tag: Option<String>,
}

impl SystemManager {
    pub fn new(app_handle: AppHandle<Wry>) -> Self {
        Self { app_handle }
    }

    pub async fn show_notification(&self, notification: SystemNotification) -> Result<(), String> {
        // Check notification permission
        let permission = self.app_handle
            .notification()
            .permission_state()
            .map_err(|e| format!("Failed to check notification permission: {}", e))?;

        if permission != PermissionState::Granted {
            // Request permission
            let granted = self.app_handle
                .notification()
                .request_permission()
                .map_err(|e| format!("Failed to request notification permission: {}", e))?;

            if granted != PermissionState::Granted {
                return Err("Notification permission not granted".to_string());
            }
        }

        // Create and show notification
        let mut builder = self.app_handle
            .notification()
            .builder()
            .title(notification.title)
            .body(notification.body);

        if let Some(icon) = notification.icon {
            builder = builder.icon(icon);
        }

        // Note: tag is not available in current tauri-plugin-notification version
        // if let Some(tag) = notification.tag {
        //     builder = builder.tag(tag);
        // }

        builder
            .show()
            .map_err(|e| format!("Failed to show notification: {}", e))?;

        Ok(())
    }

    pub fn setup_system_tray(&self) -> Result<(), String> {
        // System tray is configured in main.rs during app setup
        // This method can be used for dynamic tray updates
        Ok(())
    }

    pub async fn get_system_info(&self) -> Result<HashMap<String, String>, String> {
        let mut info = HashMap::new();
        
        info.insert("platform".to_string(), std::env::consts::OS.to_string());
        info.insert("arch".to_string(), std::env::consts::ARCH.to_string());
        info.insert("family".to_string(), std::env::consts::FAMILY.to_string());
        
        // Get app version
        if let Ok(version) = self.app_handle.package_info().version.to_string().parse::<String>() {
            info.insert("app_version".to_string(), version);
        }

        // Get app name
        info.insert("app_name".to_string(), self.app_handle.package_info().name.clone());

        Ok(info)
    }

    pub fn minimize_to_tray(&self) -> Result<(), String> {
        if let Some(window) = self.app_handle.get_webview_window("main") {
            window.hide().map_err(|e| format!("Failed to hide window: {}", e))?;
        }
        Ok(())
    }

    pub fn restore_from_tray(&self) -> Result<(), String> {
        if let Some(window) = self.app_handle.get_webview_window("main") {
            window.show().map_err(|e| format!("Failed to show window: {}", e))?;
            window.set_focus().map_err(|e| format!("Failed to focus window: {}", e))?;
        }
        Ok(())
    }

    pub fn quit_app(&self) -> Result<(), String> {
        self.app_handle.exit(0);
        Ok(())
    }
}

// Notification types for different events
pub mod notifications {
    use super::SystemNotification;

    pub fn workflow_execution_started(workflow_name: &str) -> SystemNotification {
        SystemNotification {
            title: "Workflow Started".to_string(),
            body: format!("Workflow '{}' has started executing", workflow_name),
            icon: Some("workflow-start".to_string()),
            tag: Some("workflow-execution".to_string()),
        }
    }

    pub fn workflow_execution_completed(workflow_name: &str, success: bool) -> SystemNotification {
        SystemNotification {
            title: if success { "Workflow Completed" } else { "Workflow Failed" }.to_string(),
            body: format!(
                "Workflow '{}' has {} execution", 
                workflow_name, 
                if success { "completed" } else { "failed" }
            ),
            icon: Some(if success { "workflow-success" } else { "workflow-error" }.to_string()),
            tag: Some("workflow-execution".to_string()),
        }
    }

    pub fn n8n_instance_connected(instance_name: &str) -> SystemNotification {
        SystemNotification {
            title: "N8N Instance Connected".to_string(),
            body: format!("Successfully connected to N8N instance '{}'", instance_name),
            icon: Some("instance-connected".to_string()),
            tag: Some("instance-status".to_string()),
        }
    }

    pub fn n8n_instance_disconnected(instance_name: &str) -> SystemNotification {
        SystemNotification {
            title: "N8N Instance Disconnected".to_string(),
            body: format!("Lost connection to N8N instance '{}'", instance_name),
            icon: Some("instance-disconnected".to_string()),
            tag: Some("instance-status".to_string()),
        }
    }

    pub fn sync_completed(execution_count: usize) -> SystemNotification {
        SystemNotification {
            title: "Data Sync Completed".to_string(),
            body: format!("Synchronized {} executions from N8N instances", execution_count),
            icon: Some("sync-completed".to_string()),
            tag: Some("data-sync".to_string()),
        }
    }

    pub fn ai_workflow_generated(workflow_name: &str) -> SystemNotification {
        SystemNotification {
            title: "AI Workflow Generated".to_string(),
            body: format!("AI has generated workflow '{}'", workflow_name),
            icon: Some("ai-generated".to_string()),
            tag: Some("ai-workflow".to_string()),
        }
    }
}

// Auto-start functionality
pub mod autostart {
    use std::path::PathBuf;

    pub fn enable_autostart(app_name: &str, app_path: &PathBuf) -> Result<(), String> {
        #[cfg(target_os = "windows")]
        {
            use std::process::Command;
            
            let output = Command::new("reg")
                .args(&[
                    "add",
                    "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Run",
                    "/v",
                    app_name,
                    "/t",
                    "REG_SZ",
                    "/d",
                    &format!("\"{}\"", app_path.display()),
                    "/f"
                ])
                .output()
                .map_err(|e| format!("Failed to add registry entry: {}", e))?;

            if !output.status.success() {
                return Err("Failed to enable autostart on Windows".to_string());
            }
        }

        #[cfg(target_os = "macos")]
        {
            use std::fs;
            use std::path::Path;

            let home_dir = dirs::home_dir().ok_or("Could not find home directory")?;
            let launch_agents_dir = home_dir.join("Library/LaunchAgents");
            
            // Create LaunchAgents directory if it doesn't exist
            fs::create_dir_all(&launch_agents_dir)
                .map_err(|e| format!("Failed to create LaunchAgents directory: {}", e))?;

            let plist_path = launch_agents_dir.join(format!("com.{}.plist", app_name.to_lowercase()));
            
            let plist_content = format!(
                r#"<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.{}</string>
    <key>ProgramArguments</key>
    <array>
        <string>{}</string>
    </array>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <false/>
</dict>
</plist>"#,
                app_name.to_lowercase(),
                app_path.display()
            );

            fs::write(&plist_path, plist_content)
                .map_err(|e| format!("Failed to write plist file: {}", e))?;
        }

        #[cfg(target_os = "linux")]
        {
            use std::fs;
            
            let home_dir = dirs::home_dir().ok_or("Could not find home directory")?;
            let autostart_dir = home_dir.join(".config/autostart");
            
            // Create autostart directory if it doesn't exist
            fs::create_dir_all(&autostart_dir)
                .map_err(|e| format!("Failed to create autostart directory: {}", e))?;

            let desktop_file_path = autostart_dir.join(format!("{}.desktop", app_name.to_lowercase()));
            
            let desktop_content = format!(
                r#"[Desktop Entry]
Type=Application
Name={}
Exec={}
Hidden=false
NoDisplay=false
X-GNOME-Autostart-enabled=true"#,
                app_name,
                app_path.display()
            );

            fs::write(&desktop_file_path, desktop_content)
                .map_err(|e| format!("Failed to write desktop file: {}", e))?;
        }

        Ok(())
    }

    pub fn disable_autostart(app_name: &str) -> Result<(), String> {
        #[cfg(target_os = "windows")]
        {
            use std::process::Command;
            
            let output = Command::new("reg")
                .args(&[
                    "delete",
                    "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Run",
                    "/v",
                    app_name,
                    "/f"
                ])
                .output()
                .map_err(|e| format!("Failed to delete registry entry: {}", e))?;

            if !output.status.success() {
                return Err("Failed to disable autostart on Windows".to_string());
            }
        }

        #[cfg(target_os = "macos")]
        {
            use std::fs;
            
            let home_dir = dirs::home_dir().ok_or("Could not find home directory")?;
            let plist_path = home_dir.join(format!("Library/LaunchAgents/com.{}.plist", app_name.to_lowercase()));
            
            if plist_path.exists() {
                fs::remove_file(&plist_path)
                    .map_err(|e| format!("Failed to remove plist file: {}", e))?;
            }
        }

        #[cfg(target_os = "linux")]
        {
            use std::fs;
            
            let home_dir = dirs::home_dir().ok_or("Could not find home directory")?;
            let desktop_file_path = home_dir.join(format!(".config/autostart/{}.desktop", app_name.to_lowercase()));
            
            if desktop_file_path.exists() {
                fs::remove_file(&desktop_file_path)
                    .map_err(|e| format!("Failed to remove desktop file: {}", e))?;
            }
        }

        Ok(())
    }

    pub fn is_autostart_enabled(app_name: &str) -> Result<bool, String> {
        #[cfg(target_os = "windows")]
        {
            use std::process::Command;
            
            let output = Command::new("reg")
                .args(&[
                    "query",
                    "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Run",
                    "/v",
                    app_name
                ])
                .output()
                .map_err(|e| format!("Failed to query registry: {}", e))?;

            Ok(output.status.success())
        }

        #[cfg(target_os = "macos")]
        {
            let home_dir = dirs::home_dir().ok_or("Could not find home directory")?;
            let plist_path = home_dir.join(format!("Library/LaunchAgents/com.{}.plist", app_name.to_lowercase()));
            Ok(plist_path.exists())
        }

        #[cfg(target_os = "linux")]
        {
            let home_dir = dirs::home_dir().ok_or("Could not find home directory")?;
            let desktop_file_path = home_dir.join(format!(".config/autostart/{}.desktop", app_name.to_lowercase()));
            Ok(desktop_file_path.exists())
        }
    }
}
