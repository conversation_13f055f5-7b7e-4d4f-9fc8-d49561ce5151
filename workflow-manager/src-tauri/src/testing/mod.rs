use crate::{AppState, database::repositories::Repositories, credentials::CredentialService};
use std::sync::Arc;
use tempfile::TempDir;

pub struct TestContext {
    pub temp_dir: TempDir,
    pub app_state: AppState,
    pub db_path: String,
}

impl TestContext {
    pub async fn new() -> Result<Self, Box<dyn std::error::Error>> {
        let temp_dir = TempDir::new()?;
        let db_path = temp_dir.path().join("test.db").to_string_lossy().to_string();
        
        // Initialize database
        let pool = sqlx::SqlitePool::connect(&format!("sqlite:{}", db_path)).await?;
        
        // Run migrations
        sqlx::migrate!("./migrations").run(&pool).await?;
        
        // Create repositories
        let repositories = Repositories::new(pool);
        
        // Create credential service
        let credential_service = CredentialService::new().await?;
        
        let app_state = AppState {
            repositories: Arc::new(repositories),
            credential_service: Arc::new(credential_service),
        };

        Ok(Self {
            temp_dir,
            app_state,
            db_path,
        })
    }

    pub async fn cleanup(self) -> Result<(), Box<dyn std::error::Error>> {
        // Close database connections
        self.app_state.repositories.close().await;
        
        // Temp directory will be automatically cleaned up when dropped
        Ok(())
    }
}

// Test utilities for creating mock data
pub mod mock_data {
    use crate::models::*;
    use uuid::Uuid;
    use chrono::Utc;

    pub fn create_test_n8n_instance() -> CreateN8nInstance {
        CreateN8nInstance {
            name: "Test Instance".to_string(),
            url: "http://localhost:5678".to_string(),
            description: Some("Test N8N instance".to_string()),
            is_active: true,
        }
    }

    pub fn create_test_workflow(instance_id: &str) -> CreateWorkflow {
        CreateWorkflow {
            name: "Test Workflow".to_string(),
            description: Some("Test workflow description".to_string()),
            n8n_instance_id: instance_id.to_string(),
            is_active: true,
            nodes: serde_json::json!([
                {
                    "id": "start",
                    "type": "n8n-nodes-base.start",
                    "position": [100, 100],
                    "parameters": {}
                }
            ]),
            connections: serde_json::json!({}),
            settings: Some(serde_json::json!({
                "executionOrder": "v1"
            })),
            static_data: None,
            variables: None,
            tags: Some(vec!["test".to_string()]),
        }
    }

    pub fn create_test_execution(workflow_id: &str, instance_id: &str) -> CreateExecution {
        CreateExecution {
            workflow_id: workflow_id.to_string(),
            n8n_instance_id: instance_id.to_string(),
            status: "success".to_string(),
            started_at: Utc::now().to_rfc3339(),
            finished_at: Some(Utc::now().to_rfc3339()),
            data: Some(serde_json::json!({
                "resultData": {
                    "runData": {}
                }
            })),
            error: None,
            wait_till: None,
        }
    }

    pub fn create_test_template() -> CreateTemplate {
        CreateTemplate {
            name: "Test Template".to_string(),
            description: Some("Test template description".to_string()),
            category: "automation".to_string(),
            tags: vec!["test".to_string()],
            workflow_data: serde_json::json!({
                "nodes": [],
                "connections": {}
            }),
            variables: None,
            requirements: Some(vec!["HTTP Request".to_string()]),
            difficulty_level: Some("beginner".to_string()),
            estimated_time: Some(30),
        }
    }
}

// Integration test helpers
pub mod integration_tests {
    use super::*;
    use crate::models::*;

    pub async fn test_n8n_instance_crud() -> Result<(), Box<dyn std::error::Error>> {
        let ctx = TestContext::new().await?;
        
        // Test create
        let create_data = mock_data::create_test_n8n_instance();
        let instance = ctx.app_state.repositories.n8n_instances.create(&create_data).await?;
        assert_eq!(instance.name, create_data.name);
        assert_eq!(instance.url, create_data.url);
        
        // Test get
        let retrieved = ctx.app_state.repositories.n8n_instances.get_by_id(&instance.id).await?;
        assert!(retrieved.is_some());
        assert_eq!(retrieved.unwrap().id, instance.id);
        
        // Test update
        let update_data = UpdateN8nInstance {
            name: Some("Updated Instance".to_string()),
            url: Some("http://localhost:5679".to_string()),
            description: Some("Updated description".to_string()),
            is_active: Some(false),
        };
        let updated = ctx.app_state.repositories.n8n_instances.update(&instance.id, &update_data).await?;
        assert_eq!(updated.name, "Updated Instance");
        assert_eq!(updated.url, "http://localhost:5679");
        assert!(!updated.is_active);
        
        // Test list
        let instances = ctx.app_state.repositories.n8n_instances.list(None, None).await?;
        assert_eq!(instances.len(), 1);
        
        // Test delete
        let deleted = ctx.app_state.repositories.n8n_instances.delete(&instance.id).await?;
        assert!(deleted);
        
        let after_delete = ctx.app_state.repositories.n8n_instances.get_by_id(&instance.id).await?;
        assert!(after_delete.is_none());
        
        ctx.cleanup().await?;
        Ok(())
    }

    pub async fn test_workflow_crud() -> Result<(), Box<dyn std::error::Error>> {
        let ctx = TestContext::new().await?;
        
        // Create instance first
        let instance_data = mock_data::create_test_n8n_instance();
        let instance = ctx.app_state.repositories.n8n_instances.create(&instance_data).await?;
        
        // Test create workflow
        let create_data = mock_data::create_test_workflow(&instance.id);
        let workflow = ctx.app_state.repositories.workflows.create(&create_data).await?;
        assert_eq!(workflow.name, create_data.name);
        assert_eq!(workflow.n8n_instance_id, instance.id);
        
        // Test get
        let retrieved = ctx.app_state.repositories.workflows.get_by_id(&workflow.id).await?;
        assert!(retrieved.is_some());
        
        // Test update
        let update_data = UpdateWorkflow {
            name: Some("Updated Workflow".to_string()),
            description: Some("Updated description".to_string()),
            is_active: Some(false),
            nodes: None,
            connections: None,
            settings: None,
            static_data: None,
            variables: None,
            tags: None,
        };
        let updated = ctx.app_state.repositories.workflows.update(&workflow.id, &update_data).await?;
        assert_eq!(updated.name, "Updated Workflow");
        
        // Test list
        let workflows = ctx.app_state.repositories.workflows.list(None, None).await?;
        assert_eq!(workflows.len(), 1);
        
        // Test list by instance
        let instance_workflows = ctx.app_state.repositories.workflows.list_by_instance(&instance.id, None, None).await?;
        assert_eq!(instance_workflows.len(), 1);
        
        // Test delete
        let deleted = ctx.app_state.repositories.workflows.delete(&workflow.id).await?;
        assert!(deleted);
        
        ctx.cleanup().await?;
        Ok(())
    }

    pub async fn test_execution_crud() -> Result<(), Box<dyn std::error::Error>> {
        let ctx = TestContext::new().await?;
        
        // Create instance and workflow first
        let instance_data = mock_data::create_test_n8n_instance();
        let instance = ctx.app_state.repositories.n8n_instances.create(&instance_data).await?;
        
        let workflow_data = mock_data::create_test_workflow(&instance.id);
        let workflow = ctx.app_state.repositories.workflows.create(&workflow_data).await?;
        
        // Test create execution
        let create_data = mock_data::create_test_execution(&workflow.id, &instance.id);
        let execution = ctx.app_state.repositories.executions.create(&create_data).await?;
        assert_eq!(execution.workflow_id, workflow.id);
        assert_eq!(execution.status, "success");
        
        // Test get
        let retrieved = ctx.app_state.repositories.executions.get_by_id(&execution.id).await?;
        assert!(retrieved.is_some());
        
        // Test update
        let update_data = UpdateExecution {
            status: Some("failed".to_string()),
            finished_at: Some(chrono::Utc::now().to_rfc3339()),
            data: None,
            error: Some("Test error".to_string()),
            wait_till: None,
        };
        let updated = ctx.app_state.repositories.executions.update(&execution.id, &update_data).await?;
        assert_eq!(updated.status, "failed");
        
        // Test list
        let executions = ctx.app_state.repositories.executions.list(None, None).await?;
        assert_eq!(executions.len(), 1);
        
        // Test list by workflow
        let workflow_executions = ctx.app_state.repositories.executions.list_by_workflow(&workflow.id, None, None).await?;
        assert_eq!(workflow_executions.len(), 1);
        
        ctx.cleanup().await?;
        Ok(())
    }

    pub async fn test_template_crud() -> Result<(), Box<dyn std::error::Error>> {
        let ctx = TestContext::new().await?;
        
        // Test create
        let create_data = mock_data::create_test_template();
        let template = ctx.app_state.repositories.templates.create(&create_data).await?;
        assert_eq!(template.name, create_data.name);
        assert_eq!(template.category, create_data.category);
        
        // Test get
        let retrieved = ctx.app_state.repositories.templates.get_by_id(&template.id).await?;
        assert!(retrieved.is_some());
        
        // Test update
        let update_data = UpdateTemplate {
            name: Some("Updated Template".to_string()),
            description: Some("Updated description".to_string()),
            category: Some("integration".to_string()),
            tags: Some(vec!["updated".to_string()]),
            workflow_data: None,
            variables: None,
            requirements: None,
            difficulty_level: Some("intermediate".to_string()),
            estimated_time: Some(60),
        };
        let updated = ctx.app_state.repositories.templates.update(&template.id, &update_data).await?;
        assert_eq!(updated.name, "Updated Template");
        assert_eq!(updated.category, "integration");
        
        // Test list
        let templates = ctx.app_state.repositories.templates.list(None, None).await?;
        assert_eq!(templates.len(), 1);
        
        // Test list by category
        let category_templates = ctx.app_state.repositories.templates.list_by_category("integration", None, None).await?;
        assert_eq!(category_templates.len(), 1);
        
        // Test delete
        let deleted = ctx.app_state.repositories.templates.delete(&template.id).await?;
        assert!(deleted);
        
        ctx.cleanup().await?;
        Ok(())
    }
}

// Performance testing utilities
pub mod performance_tests {
    use super::*;
    use std::time::Instant;

    pub async fn benchmark_database_operations() -> Result<(), Box<dyn std::error::Error>> {
        let ctx = TestContext::new().await?;
        
        // Benchmark instance creation
        let start = Instant::now();
        for i in 0..100 {
            let create_data = CreateN8nInstance {
                name: format!("Instance {}", i),
                url: format!("http://localhost:{}", 5678 + i),
                description: Some(format!("Test instance {}", i)),
                is_active: true,
            };
            ctx.app_state.repositories.n8n_instances.create(&create_data).await?;
        }
        let duration = start.elapsed();
        println!("Created 100 instances in {:?}", duration);
        
        // Benchmark instance listing
        let start = Instant::now();
        let instances = ctx.app_state.repositories.n8n_instances.list(None, None).await?;
        let duration = start.elapsed();
        println!("Listed {} instances in {:?}", instances.len(), duration);
        
        ctx.cleanup().await?;
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_context_creation() {
        let ctx = TestContext::new().await.expect("Failed to create test context");
        assert!(!ctx.db_path.is_empty());
        ctx.cleanup().await.expect("Failed to cleanup test context");
    }

    #[tokio::test]
    async fn test_n8n_instance_operations() {
        integration_tests::test_n8n_instance_crud().await.expect("N8N instance CRUD test failed");
    }

    #[tokio::test]
    async fn test_workflow_operations() {
        integration_tests::test_workflow_crud().await.expect("Workflow CRUD test failed");
    }

    #[tokio::test]
    async fn test_execution_operations() {
        integration_tests::test_execution_crud().await.expect("Execution CRUD test failed");
    }

    #[tokio::test]
    async fn test_template_operations() {
        integration_tests::test_template_crud().await.expect("Template CRUD test failed");
    }
}
