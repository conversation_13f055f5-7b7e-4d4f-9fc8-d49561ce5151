import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { ThemeProvider } from '@/components/ThemeProvider';
import AppLayout from '@/components/layouts/AppLayout';
import Dashboard from '@/pages/Dashboard';
import Workflows from '@/pages/Workflows';
import WorkflowCreate from '@/pages/Workflows/Create';
import WorkflowDeploy from '@/pages/Workflows/Deploy';
import WorkflowEdit from '@/pages/Workflows/Edit';
import WorkflowShow from '@/pages/Workflows/Show';
import Instances from '@/pages/Instances';
import InstanceDeployments from '@/pages/InstanceDeployments';
import Settings from '@/pages/Settings';
import './styles/globals.css';

function App() {
  console.log("🎯 App component rendering...");

  return (
    <ThemeProvider defaultTheme="system" storageKey="workflow-manager-theme">
      <Router>
        <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
          <Routes>
            <Route path="/" element={<AppLayout />}>
              <Route index element={<Dashboard />} />
              <Route path="workflows" element={<Workflows />} />
              <Route path="workflows/create" element={<WorkflowCreate />} />
              <Route path="workflows/deploy" element={<WorkflowDeploy />} />
              <Route path="workflows/:id" element={<WorkflowShow />} />
              <Route path="workflows/:id/edit" element={<WorkflowEdit />} />
              <Route path="instances" element={<Instances />} />
              <Route path="instances/:instanceId/deployments" element={<InstanceDeployments />} />
              <Route path="settings" element={<Settings />} />
            </Route>
          </Routes>

          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
              success: {
                duration: 3000,
                iconTheme: {
                  primary: '#10b981',
                  secondary: '#fff',
                },
              },
              error: {
                duration: 5000,
                iconTheme: {
                  primary: '#ef4444',
                  secondary: '#fff',
                },
              },
            }}
          />
        </div>
      </Router>
    </ThemeProvider>
  );
}

export default App;
