import React, { useState } from 'react';
import { Link, usePage, router } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { useTheme } from '@/components/ThemeProvider';
import Breadcrumb, { BreadcrumbItem } from '@/components/Breadcrumb';
import {
    Menu,
    LayoutDashboard,
    Server,
    Workflow,
    FileText,
    Settings,
    ChevronLeft,
    ChevronRight,
    ChevronDown,
    ChevronUp,
    Zap,
    User,
    Moon,
    Bot,
    Sun,
    LogOut,
    Users,
    Library,
    Sparkles,
    UserCircle,
    Shield
} from 'lucide-react';

interface AppLayoutProps {
    children: React.ReactNode;
    title?: string;
    breadcrumbs?: BreadcrumbItem[];
}

export default function AppLayout({ children, title, breadcrumbs }: AppLayoutProps) {
    const [isSidebarOpen, setIsSidebarOpen] = useState(true);
    const { theme, setTheme } = useTheme();
    const { auth } = usePage().props as any;

    const toggleSidebar = () => {
        setIsSidebarOpen(!isSidebarOpen);
    };

    const toggleDarkMode = () => {
        setTheme(theme === 'dark' ? 'light' : 'dark');
    };

    const handleLogout = () => {
        router.post('/logout');
    };

    const [expandedGroups, setExpandedGroups] = useState<string[]>(['ai']);

    const toggleGroup = (groupKey: string) => {
        setExpandedGroups(prev =>
            prev.includes(groupKey)
                ? prev.filter(key => key !== groupKey)
                : [...prev, groupKey]
        );
    };

    const navigationItems = [
        { name: 'Dashboard', href: route('dashboard'), icon: LayoutDashboard },
        { name: 'Instances', href: route('instances.index'), icon: Server },
        { name: 'Workflows', href: route('workflows.index'), icon: Workflow },
        { name: 'Templates', href: route('templates.index'), icon: FileText },
    ];

    // AI Group with submenus
    const aiGroup = {
        name: 'AI Automation',
        key: 'ai',
        icon: Sparkles,
        items: [
            { name: 'Generator', href: route('ai.workflows.index'), icon: Bot },
            { name: 'Library', href: route('ai.history.browse'), icon: Library },
        ]
    };

    // Add AI Providers to AI group for admins
    if (auth?.user?.role === 'admin') {
        aiGroup.items.push({ name: 'Providers', href: route('admin.ai-providers.index'), icon: Settings });
    }

    // Add Users menu item for admins
    const adminItems = [];
    if (auth?.user?.role === 'admin') {
        adminItems.push({ name: 'Users', href: route('users.index'), icon: Users });
    }

    return (
        <div className="flex h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
            {/* Sidebar */}
            <aside
                className={`flex-shrink-0 bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-r border-slate-200/60 dark:border-slate-700/60 transition-all duration-300 ease-in-out shadow-xl
                ${isSidebarOpen ? 'w-72' : 'w-0 overflow-hidden md:w-20 md:overflow-visible'}`}
            >
                {/* Logo Section */}
                <div className="p-6 border-b border-slate-200/60 dark:border-slate-700/60">
                    <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-blue-500 to-teal-600 rounded-xl flex items-center justify-center">
                            <Zap className="w-6 h-6 text-white" />
                        </div>
                        {isSidebarOpen && (
                            <div>
                                <h1 className="text-xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-slate-100 dark:to-slate-300 bg-clip-text text-transparent">
                                    Workflow Manager
                                </h1>
                                <p className="text-xs text-slate-500 dark:text-slate-400">Workflow Management</p>
                            </div>
                        )}
                    </div>
                </div>

                {/* Navigation */}
                <nav className="p-4 space-y-2">
                    {/* Regular Navigation Items */}
                    {navigationItems.map((item) => {
                        const Icon = item.icon;
                        const isActive = window.location.pathname === new URL(item.href).pathname;

                        return (
                            <Link
                                key={item.name}
                                href={item.href}
                                className={`flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200 group
                                    ${isActive
                                        ? 'bg-gradient-to-r from-blue-500 to-teal-600 text-white shadow-lg shadow-blue-500/25'
                                        : 'text-slate-600 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800 hover:text-slate-900 dark:hover:text-slate-100'
                                    }`}
                            >
                                <Icon className={`w-5 h-5 ${isActive ? 'text-white' : 'text-slate-500 dark:text-slate-400 group-hover:text-slate-700 dark:group-hover:text-slate-200'}`} />
                                {isSidebarOpen && (
                                    <span className="font-medium">{item.name}</span>
                                )}
                            </Link>
                        );
                    })}

                    {/* AI Group */}
                    <div className="space-y-1">
                        <button
                            onClick={() => toggleGroup(aiGroup.key)}
                            className="w-full flex items-center justify-between px-4 py-3 rounded-xl transition-all duration-200 group text-slate-600 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800 hover:text-slate-900 dark:hover:text-slate-100"
                        >
                            <div className="flex items-center space-x-3">
                                <aiGroup.icon className="w-5 h-5 text-slate-500 dark:text-slate-400 group-hover:text-slate-700 dark:group-hover:text-slate-200" />
                                {isSidebarOpen && (
                                    <span className="font-medium">{aiGroup.name}</span>
                                )}
                            </div>
                            {isSidebarOpen && (
                                expandedGroups.includes(aiGroup.key) ?
                                    <ChevronUp className="w-4 h-4 text-slate-400 dark:text-slate-500" /> :
                                    <ChevronDown className="w-4 h-4 text-slate-400 dark:text-slate-500" />
                            )}
                        </button>

                        {expandedGroups.includes(aiGroup.key) && isSidebarOpen && (
                            <div className="ml-4 space-y-1">
                                {aiGroup.items.map((subItem) => {
                                    const SubIcon = subItem.icon;
                                    const isSubActive = window.location.pathname === new URL(subItem.href).pathname;

                                    return (
                                        <Link
                                            key={subItem.name}
                                            href={subItem.href}
                                            className={`flex items-center space-x-3 px-4 py-2 rounded-lg transition-all duration-200 group
                                                ${isSubActive
                                                    ? 'bg-gradient-to-r from-blue-500 to-teal-500 text-white shadow-lg shadow-blue-500/25'
                                                    : 'text-slate-600 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-800 hover:text-slate-900 dark:hover:text-slate-100'
                                                }`}
                                        >
                                            <SubIcon className={`w-4 h-4 ${isSubActive ? 'text-white' : 'text-slate-400 dark:text-slate-500 group-hover:text-slate-600 dark:group-hover:text-slate-300'}`} />
                                            <span className="text-sm font-medium">{subItem.name}</span>
                                        </Link>
                                    );
                                })}
                            </div>
                        )}
                    </div>

                    {/* Admin Items */}
                    {adminItems.map((item) => {
                        const Icon = item.icon;
                        const isActive = window.location.pathname === new URL(item.href).pathname;

                        return (
                            <Link
                                key={item.name}
                                href={item.href}
                                className={`flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200 group
                                    ${isActive
                                        ? 'bg-gradient-to-r from-blue-500 to-teal-600 text-white shadow-lg shadow-blue-500/25'
                                        : 'text-slate-600 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800 hover:text-slate-900 dark:hover:text-slate-100'
                                    }`}
                            >
                                <Icon className={`w-5 h-5 ${isActive ? 'text-white' : 'text-slate-500 dark:text-slate-400 group-hover:text-slate-700 dark:group-hover:text-slate-200'}`} />
                                {isSidebarOpen && (
                                    <span className="font-medium">{item.name}</span>
                                )}
                            </Link>
                        );
                    })}
                </nav>

                {/* Sidebar Toggle Button */}
                <div className="absolute bottom-6 left-4">
                    <Button
                        variant="ghost"
                        size="icon"
                        onClick={toggleSidebar}
                        className="w-10 h-10 rounded-xl bg-slate-100 dark:bg-slate-800 hover:bg-slate-200 dark:hover:bg-slate-700 border border-slate-200 dark:border-slate-700"
                    >
                        {isSidebarOpen ? (
                            <ChevronLeft className="w-4 h-4" />
                        ) : (
                            <ChevronRight className="w-4 h-4" />
                        )}
                    </Button>
                </div>
            </aside>

            {/* Main Content Area */}
            <main className="flex-1 flex flex-col overflow-hidden">
                {/* Header */}
                <header className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-b border-slate-200/60 dark:border-slate-700/60 px-6 py-4 shadow-sm">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={toggleSidebar}
                                className="md:hidden w-10 h-10 rounded-xl hover:bg-slate-100 dark:hover:bg-slate-800"
                            >
                                <Menu className="h-5 w-5" />
                            </Button>
                            <div>
                                {breadcrumbs && breadcrumbs.length > 0 && (
                                    <Breadcrumb items={breadcrumbs} className="mb-2" />
                                )}
                                <h2 className="text-2xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-slate-100 dark:to-slate-300 bg-clip-text text-transparent">
                                    {title || `Welcome back, ${auth?.user?.name || 'User'}`}
                                </h2>
                                <p className="text-sm text-slate-500 dark:text-slate-400">
                                    {title ?
                                        `Role: ${auth?.user?.role?.charAt(0).toUpperCase() + auth?.user?.role?.slice(1) || 'Unknown'}` :
                                        `Role: ${auth?.user?.role?.charAt(0).toUpperCase() + auth?.user?.role?.slice(1) || 'Unknown'} • Manage your N8N workflows and automations`
                                    }
                                </p>
                            </div>
                        </div>

                        {/* Header Actions */}
                        <div className="flex items-center space-x-3">
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={toggleDarkMode}
                                className="w-10 h-10 rounded-xl hover:bg-slate-100 dark:hover:bg-slate-800"
                            >
                                {theme === 'dark' ? (
                                    <Sun className="h-5 w-5" />
                                ) : (
                                    <Moon className="h-5 w-5" />
                                )}
                            </Button>
                            <Sheet>
                                <SheetTrigger asChild>
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        className="w-10 h-10 rounded-xl hover:bg-slate-100 dark:hover:bg-slate-800"
                                        title={`Logged in as ${auth?.user?.email || 'Unknown'}`}
                                    >
                                        <User className="h-5 w-5" />
                                    </Button>
                                </SheetTrigger>
                                <SheetContent side="right" className="w-80">
                                    <SheetHeader>
                                        <SheetTitle className="flex items-center gap-3">
                                            <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-teal-600 flex items-center justify-center">
                                                <UserCircle className="w-6 h-6 text-white" />
                                            </div>
                                            <div className="text-left">
                                                <div className="font-semibold text-slate-900 dark:text-slate-100">
                                                    {auth?.user?.name || 'User'}
                                                </div>
                                                <div className="text-sm text-slate-500 dark:text-slate-400">
                                                    {auth?.user?.email || 'No email'}
                                                </div>
                                            </div>
                                        </SheetTitle>
                                    </SheetHeader>

                                    <div className="mt-8 space-y-4">
                                        {/* Profile Section */}
                                        <div className="space-y-2">
                                            <h3 className="text-sm font-medium text-slate-600 dark:text-slate-300 uppercase tracking-wide">
                                                Account
                                            </h3>
                                            <Link
                                                href="/profile"
                                                className="flex items-center gap-3 p-3 rounded-xl hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors group"
                                            >
                                                <div className="w-8 h-8 rounded-lg bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center group-hover:bg-blue-200 dark:group-hover:bg-blue-900/50 transition-colors">
                                                    <User className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                                                </div>
                                                <div>
                                                    <div className="font-medium text-slate-900 dark:text-slate-100">
                                                        Profile Settings
                                                    </div>
                                                    <div className="text-xs text-slate-500 dark:text-slate-400">
                                                        Manage your account details
                                                    </div>
                                                </div>
                                            </Link>

                                            <div className="flex items-center gap-3 p-3 rounded-xl hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors group">
                                                <div className="w-8 h-8 rounded-lg bg-green-100 dark:bg-green-900/30 flex items-center justify-center group-hover:bg-green-200 dark:group-hover:bg-green-900/50 transition-colors">
                                                    <Shield className="w-4 h-4 text-green-600 dark:text-green-400" />
                                                </div>
                                                <div>
                                                    <div className="font-medium text-slate-900 dark:text-slate-100">
                                                        Role: {auth?.user?.role?.charAt(0).toUpperCase() + auth?.user?.role?.slice(1) || 'Unknown'}
                                                    </div>
                                                    <div className="text-xs text-slate-500 dark:text-slate-400">
                                                        Account permissions
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Theme Section */}
                                        <div className="space-y-2">
                                            <h3 className="text-sm font-medium text-slate-600 dark:text-slate-300 uppercase tracking-wide">
                                                Preferences
                                            </h3>
                                            <div
                                                onClick={toggleDarkMode}
                                                className="flex items-center gap-3 p-3 rounded-xl hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors group cursor-pointer"
                                            >
                                                <div className="w-8 h-8 rounded-lg bg-amber-100 dark:bg-amber-900/30 flex items-center justify-center group-hover:bg-amber-200 dark:group-hover:bg-amber-900/50 transition-colors">
                                                    {theme === 'dark' ? (
                                                        <Sun className="w-4 h-4 text-amber-600 dark:text-amber-400" />
                                                    ) : (
                                                        <Moon className="w-4 h-4 text-amber-600 dark:text-amber-400" />
                                                    )}
                                                </div>
                                                <div>
                                                    <div className="font-medium text-slate-900 dark:text-slate-100">
                                                        {theme === 'dark' ? 'Switch to Light Mode' : 'Switch to Dark Mode'}
                                                    </div>
                                                    <div className="text-xs text-slate-500 dark:text-slate-400">
                                                        Change appearance theme
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Logout Section */}
                                        <div className="pt-4 border-t border-slate-200 dark:border-slate-700">
                                            <div
                                                onClick={handleLogout}
                                                className="flex items-center gap-3 p-3 rounded-xl hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors group cursor-pointer"
                                            >
                                                <div className="w-8 h-8 rounded-lg bg-red-100 dark:bg-red-900/30 flex items-center justify-center group-hover:bg-red-200 dark:group-hover:bg-red-900/50 transition-colors">
                                                    <LogOut className="w-4 h-4 text-red-600 dark:text-red-400" />
                                                </div>
                                                <div>
                                                    <div className="font-medium text-red-600 dark:text-red-400">
                                                        Sign Out
                                                    </div>
                                                    <div className="text-xs text-red-500 dark:text-red-400/70">
                                                        End your current session
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </SheetContent>
                            </Sheet>
                        </div>
                    </div>
                </header>

                {/* Page Content */}
                <div className="flex-1 overflow-y-auto bg-transparent">
                    {children}
                </div>
            </main>
        </div>
    );
}
