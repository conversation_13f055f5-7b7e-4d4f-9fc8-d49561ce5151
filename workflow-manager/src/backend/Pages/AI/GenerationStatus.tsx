import React, { useState, useEffect } from 'react';
import AppLayout from '@/Layouts/AppLayout';
import { PageProps } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
    Bot,
    CheckCircle2,
    XCircle,
    Clock,
    Loader2,
    Download,
    Rocket,
    Eye,
    History,
    ArrowLeft
} from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import ReactFlow, { Controls, Background } from 'reactflow';
import 'reactflow/dist/style.css';
import axios from 'axios';
import { Link } from '@inertiajs/react';

interface GenerationHistory {
    id: number;
    status: 'pending' | 'generating' | 'completed' | 'failed' | 'deployed';
    workflow_name: string;
    generated_workflow: any;
    error_message: string | null;
    tokens_used: number;
    cost: number;
    generation_time_ms: number;
    provider: string;
    template: string | null;
    created_at: string;
}

interface N8nInstance {
    id: number;
    name: string;
    url: string;
}

interface DeploymentHistory {
    id: number;
    instance_name: string;
    deployed_at: string;
    status: 'success' | 'failed';
}

interface GenerationStatusProps extends Record<string, unknown> {
    history: GenerationHistory;
    instances: N8nInstance[];
    deploymentHistory: DeploymentHistory[];
}

export default function GenerationStatus({
    history: initialHistory,
    instances,
    deploymentHistory = []
}: PageProps<GenerationStatusProps>) {
    const [history, setHistory] = useState<GenerationHistory>(initialHistory);
    const [selectedInstance, setSelectedInstance] = useState<string>('');
    const [isDeploying, setIsDeploying] = useState(false);
    const [showWorkflowPreview, setShowWorkflowPreview] = useState(false);

    // Poll for status updates if generation is in progress
    useEffect(() => {
        if (history.status === 'pending' || history.status === 'generating') {
            const interval = setInterval(async () => {
                try {
                    const response = await axios.get(`/ai/history/${history.id}/status`);
                    setHistory(response.data);

                    if (response.data.status === 'completed' || response.data.status === 'failed') {
                        clearInterval(interval);
                    }
                } catch (error) {
                    console.error('Failed to fetch status:', error);
                }
            }, 2000);

            return () => clearInterval(interval);
        }
    }, [history.id, history.status]);

    const handleDeploy = async () => {
        if (!selectedInstance) return;

        setIsDeploying(true);
        try {
            const response = await axios.post(`/ai/history/${history.id}/deploy`, {
                instance_id: parseInt(selectedInstance)
            });

            if (response.data.success) {
                setHistory(prev => ({ ...prev, status: 'deployed' }));
                alert('Workflow deployed successfully!');
            }
        } catch (error: any) {
            console.error('Deployment failed:', error);
            alert('Deployment failed: ' + (error.response?.data?.message || error.message));
        } finally {
            setIsDeploying(false);
        }
    };



    const getStatusIcon = () => {
        switch (history.status) {
            case 'pending':
            case 'generating':
                return <Loader2 className="w-6 h-6 animate-spin text-blue-500" />;
            case 'completed':
            case 'deployed':
                return <CheckCircle2 className="w-6 h-6 text-green-500" />;
            case 'failed':
                return <XCircle className="w-6 h-6 text-red-500" />;
            default:
                return <Clock className="w-6 h-6 text-gray-500" />;
        }
    };

    const getStatusText = () => {
        switch (history.status) {
            case 'pending':
                return 'Queued for generation';
            case 'generating':
                return 'Generating workflow...';
            case 'completed':
                return 'Generation completed';
            case 'deployed':
                return 'Deployed to N8N';
            case 'failed':
                return 'Generation failed';
            default:
                return 'Unknown status';
        }
    };

    const getStatusColor = () => {
        switch (history.status) {
            case 'pending':
            case 'generating':
                return 'bg-blue-100 text-blue-800';
            case 'completed':
            case 'deployed':
                return 'bg-green-100 text-green-800';
            case 'failed':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    // Transform workflow data for React Flow preview
    const getWorkflowPreview = () => {
        if (!history.generated_workflow || !history.generated_workflow.nodes) {
            return { nodes: [], edges: [] };
        }

        const nodes = history.generated_workflow.nodes.map((node: any, index: number) => ({
            id: node.id || `node-${index}`,
            type: 'default',
            position: { x: index * 200, y: 100 },
            data: { label: node.name || node.type || 'Node' },
        }));

        const edges: any[] = [];
        if (history.generated_workflow.connections) {
            // Handle N8N connection format
            Object.entries(history.generated_workflow.connections).forEach(([sourceNode, connections]: [string, any]) => {
                if (connections && connections.main && Array.isArray(connections.main)) {
                    connections.main.forEach((connectionGroup: any[], outputIndex: number) => {
                        if (Array.isArray(connectionGroup)) {
                            connectionGroup.forEach((connection: any, connectionIndex: number) => {
                                if (connection && connection.node) {
                                    edges.push({
                                        id: `e${sourceNode}-${connection.node}-${outputIndex}-${connectionIndex}`,
                                        source: sourceNode,
                                        target: connection.node,
                                    });
                                }
                            });
                        }
                    });
                }
            });
        }

        return { nodes, edges };
    };

    return (
        <AppLayout>
            <div className="p-8 max-w-6xl mx-auto">
                {/* Header */}
                <div className="flex items-center gap-4 mb-8">
                    <div className="flex items-center gap-3">
                        <div className="p-3 rounded-2xl bg-gradient-to-br from-blue-500 to-teal-500 shadow-lg">
                            <Bot className="w-8 h-8 text-white" />
                        </div>
                        <div>
                            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-teal-600 bg-clip-text text-transparent">
                                Workflow Generation
                            </h1>
                            <p className="text-slate-600 mt-1">
                                {history.workflow_name || 'AI Generated Workflow'}
                            </p>
                        </div>
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Left Column - Status & Details */}
                    <div className="lg:col-span-1 space-y-6">
                        {/* Status Card */}
                        <Card className="glass-card shadow-lg">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    {getStatusIcon()}
                                    Generation Status
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <Badge className={`${getStatusColor()} px-3 py-1`}>
                                    {getStatusText()}
                                </Badge>

                                {history.error_message && (
                                    <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                                        <p className="text-sm text-red-800">{history.error_message}</p>
                                    </div>
                                )}

                                <div className="space-y-2 text-sm">
                                    <div className="flex justify-between">
                                        <span className="text-slate-600">Provider:</span>
                                        <span className="font-medium">{history.provider}</span>
                                    </div>
                                    {history.template && (
                                        <div className="flex justify-between">
                                            <span className="text-slate-600">Template:</span>
                                            <span className="font-medium">{history.template}</span>
                                        </div>
                                    )}
                                    {history.tokens_used > 0 && (
                                        <div className="flex justify-between">
                                            <span className="text-slate-600">Tokens Used:</span>
                                            <span className="font-medium">{history.tokens_used.toLocaleString()}</span>
                                        </div>
                                    )}
                                    {history.cost > 0 && (
                                        <div className="flex justify-between">
                                            <span className="text-slate-600">Cost:</span>
                                            <span className="font-medium">${history.cost.toFixed(4)}</span>
                                        </div>
                                    )}
                                    {history.generation_time_ms > 0 && (
                                        <div className="flex justify-between">
                                            <span className="text-slate-600">Generation Time:</span>
                                            <span className="font-medium">{(history.generation_time_ms / 1000).toFixed(1)}s</span>
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Deployment Card */}
                        {history.status === 'completed' && (
                            <Card className="glass-card shadow-lg">
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Rocket className="w-5 h-5" />
                                        Deploy Workflow
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div>
                                        <Label>Select N8N Instance</Label>
                                        <Select value={selectedInstance} onValueChange={setSelectedInstance}>
                                            <SelectTrigger className="mt-2">
                                                <SelectValue placeholder="Choose instance" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {instances.map((instance) => (
                                                    <SelectItem key={instance.id} value={instance.id.toString()}>
                                                        {instance.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <Button
                                        onClick={handleDeploy}
                                        disabled={!selectedInstance || isDeploying}
                                        className="w-full"
                                    >
                                        {isDeploying ? (
                                            <>
                                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                                Deploying...
                                            </>
                                        ) : (
                                            <>
                                                <Rocket className="w-4 h-4 mr-2" />
                                                Deploy to N8N
                                            </>
                                        )}
                                    </Button>
                                </CardContent>
                            </Card>
                        )}

                        {/* Deployment History Card */}
                        {(history.status === 'completed' || history.status === 'deployed') && (
                            <Card className="glass-card shadow-lg">
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <History className="w-5 h-5" />
                                        Deployment History to Instances
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    {deploymentHistory.length > 0 ? (
                                        <div className="space-y-3">
                                            {deploymentHistory.map((deployment) => (
                                                <div
                                                    key={deployment.id}
                                                    className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-800 rounded-lg"
                                                >
                                                    <div>
                                                        <p className="font-medium text-sm">{deployment.instance_name}</p>
                                                        <p className="text-xs text-slate-600 dark:text-slate-400">
                                                            {new Date(deployment.deployed_at).toLocaleString()}
                                                        </p>
                                                    </div>
                                                    <Badge
                                                        className={
                                                            deployment.status === 'success'
                                                                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                                                : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                                                        }
                                                    >
                                                        {deployment.status === 'success' ? 'Success' : 'Failed'}
                                                    </Badge>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <div className="text-center py-6">
                                            <History className="w-12 h-12 text-slate-300 mx-auto mb-3" />
                                            <p className="text-slate-500 text-sm">
                                                No deployment history yet
                                            </p>
                                            <p className="text-slate-400 text-xs mt-1">
                                                Deploy this workflow to see history here
                                            </p>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        )}
                    </div>

                    {/* Right Column - Workflow Preview */}
                    <div className="lg:col-span-2">
                        {history.status === 'completed' || history.status === 'deployed' ? (
                            <Card className="glass-card shadow-lg">
                                <CardHeader>
                                    <CardTitle className="flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                            <Eye className="w-5 h-5" />
                                            Workflow Preview
                                        </div>
                                        <div className="flex gap-2">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => setShowWorkflowPreview(!showWorkflowPreview)}
                                            >
                                                {showWorkflowPreview ? 'Hide' : 'Show'} Visual
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => {
                                                    const dataStr = JSON.stringify(history.generated_workflow, null, 2);
                                                    const dataBlob = new Blob([dataStr], { type: 'application/json' });
                                                    const url = URL.createObjectURL(dataBlob);
                                                    const link = document.createElement('a');
                                                    link.href = url;
                                                    link.download = `${history.workflow_name || 'workflow'}.json`;
                                                    link.click();
                                                }}
                                            >
                                                <Download className="w-4 h-4 mr-2" />
                                                Download JSON
                                            </Button>
                                        </div>
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    {showWorkflowPreview ? (
                                        <div style={{ width: '100%', height: '400px' }} className="border rounded-lg">
                                            <ReactFlow
                                                nodes={getWorkflowPreview().nodes}
                                                edges={getWorkflowPreview().edges}
                                                fitView
                                                attributionPosition="bottom-left"
                                            >
                                                <Controls />
                                                <Background gap={12} size={1} />
                                            </ReactFlow>
                                        </div>
                                    ) : (
                                        <div className="bg-slate-50 rounded-lg p-4 max-h-96 overflow-auto">
                                            <pre className="text-sm">
                                                {JSON.stringify(history.generated_workflow, null, 2)}
                                            </pre>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        ) : (
                            <Card className="glass-card shadow-lg">
                                <CardContent className="flex items-center justify-center h-96">
                                    <div className="text-center">
                                        <Loader2 className="w-12 h-12 animate-spin text-purple-500 mx-auto mb-4" />
                                        <h3 className="text-lg font-medium text-slate-700 mb-2">
                                            {history.status === 'pending' ? 'Waiting in queue...' : 'Generating your workflow...'}
                                        </h3>
                                        <p className="text-slate-500">
                                            This may take a few moments depending on the complexity.
                                        </p>
                                    </div>
                                </CardContent>
                            </Card>
                        )}
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
