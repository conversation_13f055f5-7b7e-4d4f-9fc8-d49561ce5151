import React, { useState, useEffect } from 'react';
import { Head } from '@inertiajs/react';
import AppLayout from '@/Layouts/AppLayout';
import { generateBreadcrumbs } from '@/utils/breadcrumbs';
import { PageProps } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
    Bot,
    Sparkles,
    Zap,
    Clock,
    Target,
    ChevronRight,
    Lightbulb,
    Rocket,
    Settings
} from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Link } from '@inertiajs/react';
import axios from 'axios';

interface AiProvider {
    id: number;
    name: string;
    provider_key: string;
    model: string;
    description: string;
    rate_limit_per_minute: number;
}

interface AiWorkflowCategory {
    id: number;
    name: string;
    description: string;
    icon: string;
    color: string;
    active_templates: AiWorkflowTemplate[];
}

interface AiWorkflowTemplate {
    id: number;
    name: string;
    description: string;
    prompt_template: string;
    prompt_variables: Record<string, string>;
    usage_count: number;
    avg_rating: number;
    is_featured: boolean;
    recommended_provider: AiProvider;
}

interface N8nInstance {
    id: number;
    name: string;
    url: string;
}

interface UserStats {
    total_generations: number;
    successful_generations: number;
    deployed_workflows: number;
    success_rate: number;
}

interface WorkflowGeneratorProps extends Record<string, unknown> {
    categories: AiWorkflowCategory[];
    providers: AiProvider[];
    instances: N8nInstance[];
    userStats: UserStats;
}

export default function WorkflowGenerator({
    categories,
    providers,
    instances,
    userStats
}: PageProps<WorkflowGeneratorProps>) {
    const [selectedCategory, setSelectedCategory] = useState<AiWorkflowCategory | null>(null);
    const [selectedTemplate, setSelectedTemplate] = useState<AiWorkflowTemplate | null>(null);
    const [selectedProvider, setSelectedProvider] = useState<string>('');
    const [selectedInstance, setSelectedInstance] = useState<string>('');
    const [prompt, setPrompt] = useState('');
    const [promptVariables, setPromptVariables] = useState<Record<string, string>>({});
    const [isGenerating, setIsGenerating] = useState(false);

    const handleCategorySelect = (category: AiWorkflowCategory) => {
        setSelectedCategory(category);
        setSelectedTemplate(null);
        setPrompt('');
        setPromptVariables({});
    };

    const handleTemplateSelect = (template: AiWorkflowTemplate) => {
        setSelectedTemplate(template);
        setPrompt('');

        // Initialize prompt variables
        const variables: Record<string, string> = {};
        Object.keys(template.prompt_variables || {}).forEach(key => {
            variables[key] = '';
        });
        setPromptVariables(variables);

        // Set recommended provider if available
        if (template.recommended_provider) {
            setSelectedProvider(template.recommended_provider.id.toString());
        }
    };

    const handleGenerate = async () => {
        if (!prompt.trim() || !selectedProvider) {
            return;
        }

        setIsGenerating(true);

        try {
            const response = await axios.post('/ai/generate', {
                prompt: prompt.trim(),
                provider_id: parseInt(selectedProvider),
                template_id: selectedTemplate?.id || null,
                instance_id: selectedInstance && selectedInstance !== 'none' ? parseInt(selectedInstance) : null,
                prompt_variables: promptVariables,
            });

            if (response.data.success) {
                // Redirect to generation status page or show success message
                window.location.href = `/ai/history/${response.data.history_id}/status`;
            }
        } catch (error: any) {
            console.error('Generation failed:', error);
            alert('Generation failed: ' + (error.response?.data?.message || error.message));
        } finally {
            setIsGenerating(false);
        }
    };

    const breadcrumbs = generateBreadcrumbs('/ai/workflows');

    return (
        <AppLayout title="AI Workflow Generator" breadcrumbs={breadcrumbs}>
            <Head title="AI Workflow Generator" />
            <div className="p-8 max-w-7xl mx-auto">
                {/* Header */}
                <div className="mb-8">
                    <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-3">
                            <div className="p-3 rounded-2xl bg-gradient-to-br from-blue-500 to-teal-500 shadow-lg">
                                <Bot className="w-8 h-8 text-white" />
                            </div>
                            <div>
                                <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-teal-600 bg-clip-text text-transparent">
                                    AI Workflow Generator
                                </h1>
                                <p className="text-slate-600 mt-1">
                                    Create powerful N8N workflows using artificial intelligence
                                </p>
                            </div>
                        </div>
                        <Link href="/ai/history/browse">
                            <Button variant="outline" className="flex items-center gap-2">
                                <Clock className="w-4 h-4" />
                                View Library
                            </Button>
                        </Link>
                    </div>

                    {/* User Stats */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <Card className="glass-card">
                            <CardContent className="p-4">
                                <div className="flex items-center gap-2">
                                    <Sparkles className="w-5 h-5 text-purple-500" />
                                    <div>
                                        <p className="text-sm text-slate-600">Generated</p>
                                        <p className="text-xl font-bold">{userStats.total_generations}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        <Card className="glass-card">
                            <CardContent className="p-4">
                                <div className="flex items-center gap-2">
                                    <Rocket className="w-5 h-5 text-green-500" />
                                    <div>
                                        <p className="text-sm text-slate-600">Deployed</p>
                                        <p className="text-xl font-bold">{userStats.deployed_workflows}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        <Card className="glass-card">
                            <CardContent className="p-4">
                                <div className="flex items-center gap-2">
                                    <Target className="w-5 h-5 text-blue-500" />
                                    <div>
                                        <p className="text-sm text-slate-600">Success Rate</p>
                                        <p className="text-xl font-bold">{userStats.success_rate}%</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>

                <div className="flex-1">
                    {/* Left Column - Categories & Templates */}
                   {/* <div className="lg:col-span-1">
                        <Card className="glass-card shadow-lg">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Lightbulb className="w-5 h-5" />
                                    Workflow Templates
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {categories.map((category) => (
                                    <div key={category.id}>
                                        <Button
                                            variant={selectedCategory?.id === category.id ? "default" : "ghost"}
                                            className="w-full justify-start h-auto p-3"
                                            onClick={() => handleCategorySelect(category)}
                                        >
                                            <div className="flex items-center gap-3">
                                                <div
                                                    className="w-8 h-8 rounded-lg flex items-center justify-center text-white text-sm"
                                                    style={{ backgroundColor: category.color }}
                                                >
                                                    {category.active_templates.length}
                                                </div>
                                                <div className="text-left">
                                                    <p className="font-medium">{category.name}</p>
                                                    <p className="text-xs text-slate-500">{category.description}</p>
                                                </div>
                                            </div>
                                        </Button>

                                        {selectedCategory?.id === category.id && (
                                            <div className="ml-4 mt-2 space-y-2">
                                                {category.active_templates.map((template) => (
                                                    <Button
                                                        key={template.id}
                                                        variant={selectedTemplate?.id === template.id ? "secondary" : "ghost"}
                                                        className="w-full justify-start text-left h-auto p-2"
                                                        onClick={() => handleTemplateSelect(template)}
                                                    >
                                                        <div>
                                                            <div className="flex items-center gap-2">
                                                                <p className="font-medium text-sm">{template.name}</p>
                                                                {template.is_featured && (
                                                                    <Badge variant="secondary" className="text-xs">
                                                                        Featured
                                                                    </Badge>
                                                                )}
                                                            </div>
                                                            <p className="text-xs text-slate-500 mt-1">
                                                                {template.description}
                                                            </p>
                                                        </div>
                                                    </Button>
                                                ))}
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </CardContent>
                        </Card>
                    </div> */ }

                    {/* Right Column - Generation Form */}
                    <div>
                        <Card className="glass-card shadow-lg">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Settings className="w-5 h-5" />
                                    Generate Workflow
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                {/* Template Variables */}
                                {selectedTemplate && Object.keys(selectedTemplate.prompt_variables || {}).length > 0 && (
                                    <div>
                                        <Label className="text-base font-medium">Template Variables</Label>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                                            {Object.entries(selectedTemplate.prompt_variables || {}).map(([key, description]) => (
                                                <div key={key}>
                                                    <Label htmlFor={key} className="text-sm">
                                                        {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                                    </Label>
                                                    <Input
                                                        id={key}
                                                        placeholder={description}
                                                        value={promptVariables[key] || ''}
                                                        onChange={(e) => setPromptVariables(prev => ({
                                                            ...prev,
                                                            [key]: e.target.value
                                                        }))}
                                                        className="mt-1"
                                                    />
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}

                                {/* Prompt Input */}
                                <div>
                                    <Label htmlFor="prompt" className="text-base font-medium">
                                        Workflow Description
                                    </Label>
                                    <Textarea
                                        id="prompt"
                                        placeholder={selectedTemplate
                                            ? "Describe any additional requirements or modifications..."
                                            : "Describe the workflow you want to create. Be specific about triggers, actions, and data flow..."
                                        }
                                        value={prompt}
                                        onChange={(e) => setPrompt(e.target.value)}
                                        className="mt-2 min-h-[120px]"
                                    />
                                    <p className="text-sm text-slate-500 mt-1">
                                        {prompt.length}/2000 characters
                                    </p>
                                </div>

                                {/* Provider Selection */}
                                <div>
                                    <Label className="text-base font-medium">AI Provider</Label>
                                    <Select value={selectedProvider} onValueChange={setSelectedProvider}>
                                        <SelectTrigger className="mt-2">
                                            <SelectValue placeholder="Select AI provider" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {providers.map((provider) => (
                                                <SelectItem key={provider.id} value={provider.id.toString()}>
                                                    <div className="flex items-center justify-between w-full">
                                                        <div>
                                                            <p className="font-medium">{provider.name}</p>
                                                            <p className="text-xs text-slate-500">{provider.model}</p>
                                                        </div>
                                                    </div>
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                {/* Instance Selection */}
                                <div>
                                    <Label className="text-base font-medium">Target N8N Instance (Optional)</Label>
                                    <Select value={selectedInstance} onValueChange={setSelectedInstance}>
                                        <SelectTrigger className="mt-2">
                                            <SelectValue placeholder="Select N8N instance for deployment" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="none">None (Generate only)</SelectItem>
                                            {instances.map((instance) => (
                                                <SelectItem key={instance.id} value={instance.id.toString()}>
                                                    {instance.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>



                                {/* Generate Button */}
                                <Button
                                    onClick={handleGenerate}
                                    disabled={!prompt.trim() || !selectedProvider || isGenerating}
                                    className="w-full h-12 text-base"
                                    size="lg"
                                >
                                    {isGenerating ? (
                                        <>
                                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                            Generating Workflow...
                                        </>
                                    ) : (
                                        <>
                                            <Zap className="w-5 h-5 mr-2" />
                                            Generate Workflow
                                        </>
                                    )}
                                </Button>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
