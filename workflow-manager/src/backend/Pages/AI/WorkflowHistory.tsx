import React, { useState, useEffect } from 'react';
import { Head } from '@inertiajs/react';
import AppLayout from '@/Layouts/AppLayout';
import { generateBreadcrumbs } from '@/utils/breadcrumbs';
import { PageProps } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
    Bot,
    Search,
    Filter,
    Calendar,
    Clock,
    CheckCircle2,
    XCircle,
    AlertCircle,
    Loader2,
    Eye,
    Download,
    Rocket,
    Star,
    ArrowRight,
    RefreshCw
} from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Link } from '@inertiajs/react';
import axios from 'axios';

interface GenerationHistory {
    id: number;
    status: 'pending' | 'generating' | 'completed' | 'failed' | 'deployed';
    workflow_name: string;
    generated_workflow: any;
    error_message: string | null;
    tokens_used: number;
    generation_time_ms: number;
    provider: {
        id: number;
        name: string;
        provider_key: string;
    };
    template: {
        id: number;
        name: string;
    } | null;
    n8n_instance: {
        id: number;
        name: string;
        url: string;
    } | null;
    rating: number | null;
    created_at: string;
    updated_at: string;
}

interface PaginatedHistory {
    data: GenerationHistory[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
}

interface UserStats {
    total_generations: number;
    successful_generations: number;
    deployed_workflows: number;
    success_rate: number;
}

interface WorkflowHistoryProps extends Record<string, unknown> {
    userStats: UserStats;
}

export default function WorkflowHistory({
    userStats
}: PageProps<WorkflowHistoryProps>) {
    const [history, setHistory] = useState<PaginatedHistory | null>(null);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [currentPage, setCurrentPage] = useState(1);

    const fetchHistory = async (page = 1, search = '', status = 'all') => {
        setLoading(true);
        try {
            const params = new URLSearchParams({
                page: page.toString(),
            });

            if (search) params.append('search', search);
            if (status !== 'all') params.append('status', status);

            const response = await axios.get(`/ai/history?${params}`);
            setHistory(response.data);
        } catch (error) {
            console.error('Failed to fetch history:', error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchHistory(currentPage, searchTerm, statusFilter);
    }, [currentPage, searchTerm, statusFilter]);

    const handleSearch = (value: string) => {
        setSearchTerm(value);
        setCurrentPage(1);
    };

    const handleStatusFilter = (value: string) => {
        setStatusFilter(value);
        setCurrentPage(1);
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'completed': return <CheckCircle2 className="w-4 h-4 text-green-500" />;
            case 'failed': return <XCircle className="w-4 h-4 text-red-500" />;
            case 'deployed': return <Rocket className="w-4 h-4 text-blue-500" />;
            case 'generating': return <Loader2 className="w-4 h-4 text-yellow-500 animate-spin" />;
            case 'pending': return <Clock className="w-4 h-4 text-gray-500" />;
            default: return <AlertCircle className="w-4 h-4 text-gray-500" />;
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'completed': return 'bg-green-100 text-green-800 border-green-200';
            case 'failed': return 'bg-red-100 text-red-800 border-red-200';
            case 'deployed': return 'bg-blue-100 text-blue-800 border-blue-200';
            case 'generating': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
            case 'pending': return 'bg-gray-100 text-gray-800 border-gray-200';
            default: return 'bg-gray-100 text-gray-800 border-gray-200';
        }
    };

    const getProviderIcon = (providerKey: string) => {
        switch (providerKey) {
            case 'openai': return '🤖';
            case 'claude': return '🧠';
            case 'gemini': return '💎';
            case 'deepseek': return '🔍';
            case 'aimlapi': return '🚀';
            default: return '🤖';
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const downloadWorkflow = (workflow: any, name: string) => {
        const blob = new Blob([JSON.stringify(workflow, null, 2)], {
            type: 'application/json'
        });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${name || 'workflow'}.json`;
        link.click();
        URL.revokeObjectURL(url);
    };

    const breadcrumbs = generateBreadcrumbs('/ai/history/browse');

    return (
        <AppLayout title="Workflow Library" breadcrumbs={breadcrumbs}>
            <Head title="Workflow Library" />
            <div className="p-8 max-w-7xl mx-auto">
                {/* Header */}
                <div className="flex items-center justify-between mb-8">
                    <div className="flex items-center gap-4">
                        <div className="p-3 rounded-2xl bg-gradient-to-br from-blue-500 to-teal-500 shadow-lg">
                            <Bot className="w-8 h-8 text-white" />
                        </div>
                        <div>
                            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-teal-600 bg-clip-text text-transparent">
                                Workflow Library
                            </h1>
                            <p className="text-slate-600 mt-1">
                                Browse and manage your AI-generated N8N workflows
                            </p>
                        </div>
                    </div>
                    <Link href="/ai/workflows">
                        <Button className="bg-gradient-to-r from-blue-600 to-teal-600 hover:from-blue-700 hover:to-teal-700">
                            <Bot className="w-4 h-4 mr-2" />
                            Generate New Workflow
                        </Button>
                    </Link>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <Card className="border-l-4 border-l-blue-500">
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-slate-600">Total Generated</p>
                                    <p className="text-2xl font-bold text-slate-900">{userStats.total_generations}</p>
                                </div>
                                <Bot className="w-8 h-8 text-blue-500" />
                            </div>
                        </CardContent>
                    </Card>
                    <Card className="border-l-4 border-l-green-500">
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-slate-600">Successful</p>
                                    <p className="text-2xl font-bold text-slate-900">{userStats.successful_generations}</p>
                                </div>
                                <CheckCircle2 className="w-8 h-8 text-green-500" />
                            </div>
                        </CardContent>
                    </Card>
                    <Card className="border-l-4 border-l-purple-500">
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-slate-600">Deployed</p>
                                    <p className="text-2xl font-bold text-slate-900">{userStats.deployed_workflows}</p>
                                </div>
                                <Rocket className="w-8 h-8 text-purple-500" />
                            </div>
                        </CardContent>
                    </Card>
                    <Card className="border-l-4 border-l-orange-500">
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-slate-600">Success Rate</p>
                                    <p className="text-2xl font-bold text-slate-900">{userStats.success_rate}%</p>
                                </div>
                                <Star className="w-8 h-8 text-orange-500" />
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Filters */}
                <Card className="mb-6">
                    <CardContent className="p-6">
                        <div className="flex flex-col md:flex-row gap-4">
                            <div className="flex-1">
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
                                    <Input
                                        placeholder="Search workflows..."
                                        value={searchTerm}
                                        onChange={(e) => handleSearch(e.target.value)}
                                        className="pl-10"
                                    />
                                </div>
                            </div>
                            <div className="flex gap-4">
                                <Select value={statusFilter} onValueChange={handleStatusFilter}>
                                    <SelectTrigger className="w-48">
                                        <Filter className="w-4 h-4 mr-2" />
                                        <SelectValue placeholder="Filter by status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Status</SelectItem>
                                        <SelectItem value="completed">Completed</SelectItem>
                                        <SelectItem value="deployed">Deployed</SelectItem>
                                        <SelectItem value="failed">Failed</SelectItem>
                                        <SelectItem value="generating">Generating</SelectItem>
                                        <SelectItem value="pending">Pending</SelectItem>
                                    </SelectContent>
                                </Select>
                                <Button
                                    variant="outline"
                                    onClick={() => fetchHistory(currentPage, searchTerm, statusFilter)}
                                    disabled={loading}
                                >
                                    <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Workflow History */}
                {loading ? (
                    <div className="flex items-center justify-center py-12">
                        <Loader2 className="w-8 h-8 animate-spin text-purple-600" />
                        <span className="ml-2 text-slate-600">Loading workflows...</span>
                    </div>
                ) : history && history.data.length > 0 ? (
                    <div className="space-y-4">
                        {history.data.map((item) => (
                            <Card key={item.id} className="hover:shadow-lg transition-shadow">
                                <CardContent className="p-6">
                                    <div className="flex items-start justify-between">
                                        <div className="flex-1">
                                            <div className="flex items-center gap-3 mb-2">
                                                <h3 className="text-lg font-semibold text-slate-900">
                                                    {item.workflow_name || 'Untitled Workflow'}
                                                </h3>
                                                <Badge className={`${getStatusColor(item.status)} border`}>
                                                    {getStatusIcon(item.status)}
                                                    <span className="ml-1 capitalize">{item.status}</span>
                                                </Badge>
                                                {item.rating && (
                                                    <div className="flex items-center gap-1">
                                                        <Star className="w-4 h-4 text-yellow-500 fill-current" />
                                                        <span className="text-sm text-slate-600">{item.rating}/5</span>
                                                    </div>
                                                )}
                                            </div>

                                            <div className="flex items-center gap-6 text-sm text-slate-600 mb-3">
                                                <div className="flex items-center gap-2">
                                                    <span className="text-lg">{getProviderIcon(item.provider.provider_key)}</span>
                                                    <span>{item.provider.name}</span>
                                                </div>
                                                {item.template && (
                                                    <div className="flex items-center gap-2">
                                                        <span>Template: {item.template.name}</span>
                                                    </div>
                                                )}
                                                <div className="flex items-center gap-2">
                                                    <Calendar className="w-4 h-4" />
                                                    <span>{formatDate(item.created_at)}</span>
                                                </div>
                                                {item.generation_time_ms && (
                                                    <div className="flex items-center gap-2">
                                                        <Clock className="w-4 h-4" />
                                                        <span>{(item.generation_time_ms / 1000).toFixed(1)}s</span>
                                                    </div>
                                                )}
                                            </div>

                                            {item.error_message && (
                                                <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-3">
                                                    <p className="text-sm text-red-800">{item.error_message}</p>
                                                </div>
                                            )}
                                        </div>

                                        <div className="flex items-center gap-2 ml-4">
                                            {item.status === 'completed' && item.generated_workflow && (
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => downloadWorkflow(item.generated_workflow, item.workflow_name)}
                                                >
                                                    <Download className="w-4 h-4" />
                                                </Button>
                                            )}
                                            <Link href={`/ai/history/${item.id}/status`}>
                                                <Button variant="outline" size="sm">
                                                    <Eye className="w-4 h-4 mr-2" />
                                                    View Details
                                                </Button>
                                            </Link>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}

                        {/* Pagination */}
                        {history.last_page > 1 && (
                            <div className="flex items-center justify-center gap-2 mt-8">
                                <Button
                                    variant="outline"
                                    disabled={currentPage === 1}
                                    onClick={() => setCurrentPage(currentPage - 1)}
                                >
                                    Previous
                                </Button>
                                <span className="text-sm text-slate-600">
                                    Page {currentPage} of {history.last_page}
                                </span>
                                <Button
                                    variant="outline"
                                    disabled={currentPage === history.last_page}
                                    onClick={() => setCurrentPage(currentPage + 1)}
                                >
                                    Next
                                </Button>
                            </div>
                        )}
                    </div>
                ) : (
                    <Card>
                        <CardContent className="p-12 text-center">
                            <Bot className="w-16 h-16 text-slate-300 mx-auto mb-4" />
                            <h3 className="text-lg font-semibold text-slate-900 mb-2">No workflows generated yet</h3>
                            <p className="text-slate-600 mb-6">
                                Start creating AI-powered N8N workflows to see them here.
                            </p>
                            <Link href="/ai/workflows">
                                <Button className="bg-gradient-to-r from-blue-600 to-teal-600 hover:from-blue-700 hover:to-teal-700">
                                    <Bot className="w-4 h-4 mr-2" />
                                    Generate Your First Workflow
                                    <ArrowRight className="w-4 h-4 ml-2" />
                                </Button>
                            </Link>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
