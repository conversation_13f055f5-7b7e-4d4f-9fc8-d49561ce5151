import React, { useState } from 'react';
import AppLayout from '@/Layouts/AppLayout';
import { PageProps } from '@/types';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import {
    Bot,
    Settings,
    Key,
    CheckCircle2,
    XCircle,
    TestTube,
    Save,
    Eye,
    EyeOff
} from 'lucide-react';
import axios from 'axios';

interface AiProvider {
    id: number;
    name: string;
    provider_key: string;
    model: string;
    description: string;
    is_active: boolean;
    has_api_key: boolean;
    rate_limit_per_minute: number;
    settings: Record<string, any>;
    created_at: string;
    updated_at: string;
}

interface AiProviderStats {
    total: number;
    active: number;
    configured: number;
}

interface AiProvidersIndexProps extends Record<string, unknown> {
    providers: AiProvider[];
    stats: AiProviderStats;
}

export default function AiProvidersIndex({
    providers: initialProviders,
    stats
}: PageProps<AiProvidersIndexProps>) {
    const [providers, setProviders] = useState<AiProvider[]>(initialProviders);
    const [editingProvider, setEditingProvider] = useState<AiProvider | null>(null);
    const [formData, setFormData] = useState<Partial<AiProvider> & { api_key?: string }>({});
    const [showApiKey, setShowApiKey] = useState(false);
    const [isSaving, setIsSaving] = useState(false);
    const [isTesting, setIsTesting] = useState(false);

    const handleEdit = (provider: AiProvider) => {
        setEditingProvider(provider);
        setFormData({
            model: provider.model,
            description: provider.description,
            is_active: provider.is_active,
            rate_limit_per_minute: provider.rate_limit_per_minute,
            api_key: '', // Don't pre-fill API key for security
        });
        setShowApiKey(false);
    };

    const handleSave = async () => {
        if (!editingProvider) return;

        setIsSaving(true);
        try {
            const response = await axios.put(`/admin/ai-providers/${editingProvider.id}`, formData);

            if (response.data.success) {
                // Update the provider in the list
                setProviders(prev => prev.map(p =>
                    p.id === editingProvider.id ? response.data.provider : p
                ));
                setEditingProvider(null);
                setFormData({});
                alert('Provider updated successfully!');
            }
        } catch (error: any) {
            console.error('Save failed:', error);
            alert('Failed to save: ' + (error.response?.data?.message || error.message));
        } finally {
            setIsSaving(false);
        }
    };

    const handleTestConnection = async (provider: AiProvider) => {
        setIsTesting(true);
        try {
            const response = await axios.post(`/admin/ai-providers/${provider.id}/test`);

            if (response.data.success) {
                alert('✅ Connection successful!');
            } else {
                alert('❌ Connection failed: ' + response.data.message);
            }
        } catch (error: any) {
            console.error('Test failed:', error);
            alert('❌ Connection test failed: ' + (error.response?.data?.message || error.message));
        } finally {
            setIsTesting(false);
        }
    };

    const getProviderIcon = (providerKey: string) => {
        switch (providerKey) {
            case 'openai': return '🤖';
            case 'claude': return '🧠';
            case 'gemini': return '💎';
            case 'deepseek': return '🔍';
            case 'aimlapi': return '🚀';
            default: return '🤖';
        }
    };

    return (
        <AppLayout>
            <div className="p-8 max-w-7xl mx-auto">
                {/* Header */}
                <div className="mb-8">
                    <div className="flex items-center gap-3 mb-4">
                        <div className="p-3 rounded-2xl bg-gradient-to-br from-blue-500 to-purple-500 shadow-lg">
                            <Settings className="w-8 h-8 text-white" />
                        </div>
                        <div>
                            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                AI Provider Management
                            </h1>
                            <p className="text-slate-600 mt-1">
                                Configure API keys and settings for AI providers
                            </p>
                        </div>
                    </div>

                    {/* Stats */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <Card className="glass-card">
                            <CardContent className="p-4">
                                <div className="flex items-center gap-2">
                                    <Bot className="w-5 h-5 text-blue-500" />
                                    <div>
                                        <p className="text-sm text-slate-600">Total Providers</p>
                                        <p className="text-xl font-bold">{stats.total}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        <Card className="glass-card">
                            <CardContent className="p-4">
                                <div className="flex items-center gap-2">
                                    <CheckCircle2 className="w-5 h-5 text-green-500" />
                                    <div>
                                        <p className="text-sm text-slate-600">Active</p>
                                        <p className="text-xl font-bold">{stats.active}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        <Card className="glass-card">
                            <CardContent className="p-4">
                                <div className="flex items-center gap-2">
                                    <Key className="w-5 h-5 text-amber-500" />
                                    <div>
                                        <p className="text-sm text-slate-600">Configured</p>
                                        <p className="text-xl font-bold">{stats.configured}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Providers List */}
                    <div>
                        <Card className="glass-card shadow-lg">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Bot className="w-5 h-5" />
                                    AI Providers
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {providers.map((provider) => (
                                    <div key={provider.id} className="p-4 border rounded-lg hover:bg-slate-50 transition-colors">
                                        <div className="flex items-center justify-between mb-3">
                                            <div className="flex items-center gap-3">
                                                <span className="text-2xl">{getProviderIcon(provider.provider_key)}</span>
                                                <div>
                                                    <h3 className="font-semibold">{provider.name}</h3>
                                                    <p className="text-sm text-slate-500">{provider.model}</p>
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-2">
                                                {provider.has_api_key ? (
                                                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                                                        <Key className="w-3 h-3 mr-1" />
                                                        Configured
                                                    </Badge>
                                                ) : (
                                                    <Badge variant="secondary" className="bg-red-100 text-red-800">
                                                        <XCircle className="w-3 h-3 mr-1" />
                                                        No API Key
                                                    </Badge>
                                                )}
                                                {provider.is_active ? (
                                                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                                                        Active
                                                    </Badge>
                                                ) : (
                                                    <Badge variant="secondary" className="bg-gray-100 text-gray-800">
                                                        Inactive
                                                    </Badge>
                                                )}
                                            </div>
                                        </div>

                                        <p className="text-sm text-slate-600 mb-3">{provider.description}</p>

                                        <div className="flex gap-2">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => handleEdit(provider)}
                                            >
                                                <Settings className="w-4 h-4 mr-2" />
                                                Configure
                                            </Button>
                                            {provider.has_api_key && (
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => handleTestConnection(provider)}
                                                    disabled={isTesting}
                                                >
                                                    <TestTube className="w-4 h-4 mr-2" />
                                                    Test
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                ))}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Configuration Form */}
                    <div>
                        {editingProvider ? (
                            <Card className="glass-card shadow-lg">
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <span className="text-2xl">{getProviderIcon(editingProvider.provider_key)}</span>
                                        Configure {editingProvider.name}
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-6">
                                    {/* API Key */}
                                    <div>
                                        <Label htmlFor="api_key" className="text-base font-medium">
                                            API Key
                                        </Label>
                                        <div className="relative mt-2">
                                            <Input
                                                id="api_key"
                                                type={showApiKey ? "text" : "password"}
                                                placeholder="Enter API key..."
                                                value={formData.api_key || ''}
                                                onChange={(e) => setFormData(prev => ({ ...prev, api_key: e.target.value }))}
                                                className="pr-10"
                                            />
                                            <button
                                                type="button"
                                                onClick={() => setShowApiKey(!showApiKey)}
                                                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600"
                                            >
                                                {showApiKey ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                                            </button>
                                        </div>
                                        <p className="text-sm text-slate-500 mt-1">
                                            Leave empty to keep existing API key
                                        </p>
                                    </div>

                                    {/* Model */}
                                    <div>
                                        <Label htmlFor="model" className="text-base font-medium">
                                            Model
                                        </Label>
                                        <Input
                                            id="model"
                                            placeholder="e.g., gpt-4, claude-3-5-sonnet"
                                            value={formData.model || ''}
                                            onChange={(e) => setFormData(prev => ({ ...prev, model: e.target.value }))}
                                            className="mt-2"
                                        />
                                    </div>

                                    {/* Description */}
                                    <div>
                                        <Label htmlFor="description" className="text-base font-medium">
                                            Description
                                        </Label>
                                        <Textarea
                                            id="description"
                                            placeholder="Provider description..."
                                            value={formData.description || ''}
                                            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                                            className="mt-2"
                                            rows={3}
                                        />
                                    </div>

                                    {/* Rate Limit */}
                                    <div>
                                        <Label htmlFor="rate_limit" className="text-base font-medium">
                                            Rate Limit (requests per minute)
                                        </Label>
                                        <Input
                                            id="rate_limit"
                                            type="number"
                                            placeholder="e.g., 3500"
                                            value={formData.rate_limit_per_minute || ''}
                                            onChange={(e) => setFormData(prev => ({
                                                ...prev,
                                                rate_limit_per_minute: parseInt(e.target.value) || 0
                                            }))}
                                            className="mt-2"
                                        />
                                    </div>

                                    {/* Active Toggle */}
                                    <div className="flex items-center justify-between">
                                        <Label htmlFor="is_active" className="text-base font-medium">
                                            Enable Provider
                                        </Label>
                                        <Switch
                                            id="is_active"
                                            checked={formData.is_active || false}
                                            onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
                                        />
                                    </div>

                                    {/* Actions */}
                                    <div className="flex gap-3 pt-4">
                                        <Button
                                            onClick={handleSave}
                                            disabled={isSaving}
                                            className="flex-1"
                                        >
                                            {isSaving ? (
                                                <>
                                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                                    Saving...
                                                </>
                                            ) : (
                                                <>
                                                    <Save className="w-4 h-4 mr-2" />
                                                    Save Configuration
                                                </>
                                            )}
                                        </Button>
                                        <Button
                                            variant="outline"
                                            onClick={() => {
                                                setEditingProvider(null);
                                                setFormData({});
                                            }}
                                        >
                                            Cancel
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        ) : (
                            <Card className="glass-card shadow-lg">
                                <CardContent className="flex items-center justify-center h-96">
                                    <div className="text-center">
                                        <Settings className="w-12 h-12 text-slate-400 mx-auto mb-4" />
                                        <h3 className="text-lg font-medium text-slate-700 mb-2">
                                            Select a Provider to Configure
                                        </h3>
                                        <p className="text-slate-500">
                                            Choose a provider from the list to configure its API key and settings.
                                        </p>
                                    </div>
                                </CardContent>
                            </Card>
                        )}
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
