import React, { useState } from 'react';
import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import AppLayout from '@/Layouts/AppLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
    Plus,
    Search,
    Eye,
    Edit,
    Trash2,
    Power,
    PowerOff,
    TestTube,
    Mail,
    MessageSquare,
    Webhook,
    Users,
    Hash,
    CheckCircle,
    XCircle,
    Clock
} from 'lucide-react';

interface AlertChannel {
    id: number;
    name: string;
    description: string;
    type: 'email' | 'slack' | 'webhook' | 'teams' | 'discord';
    is_active: boolean;
    last_tested_at?: string;
    test_status?: 'pending' | 'success' | 'failed';
    test_error?: string;
    creator: {
        id: number;
        name: string;
    };
    created_at: string;
}

interface Props {
    channels: {
        data: AlertChannel[];
        links: any;
        meta: any;
    };
    stats: {
        total: number;
        active: number;
        by_type: Record<string, number>;
    };
}

const typeIcons = {
    email: Mail,
    slack: MessageSquare,
    webhook: Webhook,
    teams: Users,
    discord: Hash,
};

const typeColors = {
    email: 'bg-blue-100 text-blue-800 border-blue-200',
    slack: 'bg-green-100 text-green-800 border-green-200',
    webhook: 'bg-purple-100 text-purple-800 border-purple-200',
    teams: 'bg-indigo-100 text-indigo-800 border-indigo-200',
    discord: 'bg-violet-100 text-violet-800 border-violet-200',
};

export default function Index({ channels, stats }: Props) {
    const [searchTerm, setSearchTerm] = useState('');

    const handleToggleChannel = (channelId: number) => {
        router.post(`/alerts/channels/${channelId}/toggle`, {}, {
            preserveScroll: true,
        });
    };

    const handleTestChannel = (channelId: number) => {
        router.post(`/alerts/channels/${channelId}/test`, {}, {
            preserveScroll: true,
        });
    };

    const handleDeleteChannel = (channelId: number) => {
        if (confirm('Are you sure you want to delete this alert channel?')) {
            router.delete(`/alerts/channels/${channelId}`, {
                preserveScroll: true,
            });
        }
    };

    const filteredChannels = channels.data.filter(channel =>
        channel.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        channel.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        channel.type.toLowerCase().includes(searchTerm.toLowerCase())
    );

    return (
        <AppLayout>
            <Head title="Alert Channels" />

            <div className="p-8 space-y-8">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Alert Channels</h1>
                        <p className="text-gray-600 mt-1">Configure notification channels for your alert rules</p>
                    </div>
                    <Link href="/alerts/channels/create">
                        <Button className="bg-gradient-to-r from-blue-500 to-teal-600 hover:from-blue-600 hover:to-teal-700">
                            <Plus className="w-4 h-4 mr-2" />
                            Create Channel
                        </Button>
                    </Link>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Channels</CardTitle>
                            <MessageSquare className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Active Channels</CardTitle>
                            <Power className="h-4 w-4 text-green-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-600">{stats.active}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Email Channels</CardTitle>
                            <Mail className="h-4 w-4 text-blue-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-blue-600">{stats.by_type.email || 0}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Slack Channels</CardTitle>
                            <MessageSquare className="h-4 w-4 text-green-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-600">{stats.by_type.slack || 0}</div>
                        </CardContent>
                    </Card>
                </div>

                {/* Search */}
                <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                        placeholder="Search alert channels..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                    />
                </div>

                {/* Alert Channels List */}
                <div className="space-y-4">
                    {filteredChannels.length === 0 ? (
                        <Card>
                            <CardContent className="flex flex-col items-center justify-center py-12">
                                <MessageSquare className="w-12 h-12 text-gray-400 mb-4" />
                                <h3 className="text-lg font-medium text-gray-900 mb-2">No alert channels found</h3>
                                <p className="text-gray-600 text-center mb-4">
                                    {searchTerm ? 'No channels match your search criteria.' : 'Get started by creating your first alert channel.'}
                                </p>
                                {!searchTerm && (
                                    <Link href="/alerts/channels/create">
                                        <Button>
                                            <Plus className="w-4 h-4 mr-2" />
                                            Create Channel
                                        </Button>
                                    </Link>
                                )}
                            </CardContent>
                        </Card>
                    ) : (
                        filteredChannels.map((channel) => {
                            const TypeIcon = typeIcons[channel.type];
                            return (
                                <Card key={channel.id} className="hover:shadow-md transition-shadow">
                                    <CardContent className="p-6">
                                        <div className="flex items-start justify-between">
                                            <div className="flex-1">
                                                <div className="flex items-center gap-3 mb-2">
                                                    <TypeIcon className="w-5 h-5 text-gray-600" />
                                                    <h3 className="text-lg font-semibold text-gray-900">{channel.name}</h3>
                                                    <Badge className={typeColors[channel.type]}>
                                                        {channel.type.toUpperCase()}
                                                    </Badge>
                                                    {channel.is_active ? (
                                                        <Badge className="bg-green-100 text-green-800 border-green-200">
                                                            <Power className="w-3 h-3 mr-1" />
                                                            Active
                                                        </Badge>
                                                    ) : (
                                                        <Badge className="bg-gray-100 text-gray-800 border-gray-200">
                                                            <PowerOff className="w-3 h-3 mr-1" />
                                                            Inactive
                                                        </Badge>
                                                    )}
                                                </div>

                                                {channel.description && (
                                                    <p className="text-gray-600 mb-3">{channel.description}</p>
                                                )}

                                                <div className="flex items-center gap-6 text-sm text-gray-500">
                                                    <div className="flex items-center gap-1">
                                                        <span>Created by: {channel.creator.name}</span>
                                                    </div>
                                                    {channel.last_tested_at && (
                                                        <div className="flex items-center gap-1">
                                                            <Clock className="w-4 h-4" />
                                                            <span>Last tested: {new Date(channel.last_tested_at).toLocaleString()}</span>
                                                        </div>
                                                    )}
                                                    {channel.test_status && (
                                                        <div className="flex items-center gap-1">
                                                            {channel.test_status === 'success' ? (
                                                                <>
                                                                    <CheckCircle className="w-4 h-4 text-green-600" />
                                                                    <span className="text-green-600">Test passed</span>
                                                                </>
                                                            ) : channel.test_status === 'failed' ? (
                                                                <>
                                                                    <XCircle className="w-4 h-4 text-red-600" />
                                                                    <span className="text-red-600">Test failed</span>
                                                                </>
                                                            ) : (
                                                                <>
                                                                    <Clock className="w-4 h-4 text-yellow-600" />
                                                                    <span className="text-yellow-600">Test pending</span>
                                                                </>
                                                            )}
                                                        </div>
                                                    )}
                                                </div>

                                                {channel.test_error && (
                                                    <div className="mt-2 text-sm text-red-600 bg-red-50 p-2 rounded">
                                                        Test error: {channel.test_error}
                                                    </div>
                                                )}
                                            </div>

                                            <div className="flex items-center gap-2 ml-4">
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => handleToggleChannel(channel.id)}
                                                >
                                                    {channel.is_active ? (
                                                        <PowerOff className="w-4 h-4" />
                                                    ) : (
                                                        <Power className="w-4 h-4" />
                                                    )}
                                                </Button>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => handleTestChannel(channel.id)}
                                                >
                                                    <TestTube className="w-4 h-4" />
                                                </Button>
                                                <Link href={`/alerts/channels/${channel.id}`}>
                                                    <Button variant="outline" size="sm">
                                                        <Eye className="w-4 h-4" />
                                                    </Button>
                                                </Link>
                                                <Link href={`/alerts/channels/${channel.id}/edit`}>
                                                    <Button variant="outline" size="sm">
                                                        <Edit className="w-4 h-4" />
                                                    </Button>
                                                </Link>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => handleDeleteChannel(channel.id)}
                                                    className="text-red-600 hover:text-red-700"
                                                >
                                                    <Trash2 className="w-4 h-4" />
                                                </Button>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            );
                        })
                    )}
                </div>
            </div>
        </AppLayout>
    );
}
