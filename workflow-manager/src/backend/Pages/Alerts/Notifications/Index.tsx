import React, { useState } from 'react';
import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import AppLayout from '@/Layouts/AppLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import {
    Bell,
    Search,
    Eye,
    Trash2,
    CheckCircle,
    XCircle,
    Clock,
    AlertTriangle,
    Mail,
    MessageSquare,
    Webhook,
    Users,
    Hash,
    Filter,
    Calendar
} from 'lucide-react';

interface AlertNotification {
    id: number;
    title: string;
    message: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    status: 'pending' | 'sent' | 'failed' | 'acknowledged';
    channel: 'email' | 'slack' | 'webhook' | 'teams' | 'discord' | 'database';
    triggered_at: string;
    acknowledged_at?: string;
    acknowledged_by?: {
        id: number;
        name: string;
    };
    alert_rule: {
        id: number;
        name: string;
    };
    n8n_instance?: {
        id: number;
        name: string;
    };
    workflow?: {
        id: number;
        name: string;
    };
}

interface Props {
    notifications: {
        data: AlertNotification[];
        links: any;
        meta: any;
    };
    filters: {
        status?: string;
        severity?: string;
        date_from?: string;
        date_to?: string;
    };
    stats: {
        total: number;
        pending: number;
        unacknowledged: number;
        failed: number;
    };
    statuses: Record<string, string>;
    severityLevels: Record<string, string>;
}

const severityColors = {
    low: 'bg-blue-100 text-blue-800 border-blue-200',
    medium: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    high: 'bg-orange-100 text-orange-800 border-orange-200',
    critical: 'bg-red-100 text-red-800 border-red-200',
};

const statusColors = {
    pending: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    sent: 'bg-green-100 text-green-800 border-green-200',
    failed: 'bg-red-100 text-red-800 border-red-200',
    acknowledged: 'bg-blue-100 text-blue-800 border-blue-200',
};

const channelIcons = {
    email: Mail,
    slack: MessageSquare,
    webhook: Webhook,
    teams: Users,
    discord: Hash,
    database: Bell,
};

export default function Index({ notifications, filters, stats, statuses, severityLevels }: Props) {
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedNotifications, setSelectedNotifications] = useState<number[]>([]);

    const handleAcknowledge = (notificationId: number) => {
        router.post(`/alerts/notifications/${notificationId}/acknowledge`, {}, {
            preserveScroll: true,
        });
    };

    const handleBulkAcknowledge = () => {
        if (selectedNotifications.length === 0) return;

        router.post('/alerts/notifications/bulk-acknowledge', {
            notification_ids: selectedNotifications
        }, {
            preserveScroll: true,
            onSuccess: () => setSelectedNotifications([])
        });
    };

    const handleDelete = (notificationId: number) => {
        if (confirm('Are you sure you want to delete this notification?')) {
            router.delete(`/alerts/notifications/${notificationId}`, {
                preserveScroll: true,
            });
        }
    };

    const handleBulkDelete = () => {
        if (selectedNotifications.length === 0) return;

        if (confirm(`Are you sure you want to delete ${selectedNotifications.length} notifications?`)) {
            router.post('/alerts/notifications/bulk-destroy', {
                notification_ids: selectedNotifications
            }, {
                preserveScroll: true,
                onSuccess: () => setSelectedNotifications([])
            });
        }
    };

    const handleSelectAll = (checked: boolean) => {
        if (checked) {
            setSelectedNotifications(notifications.data.map(n => n.id));
        } else {
            setSelectedNotifications([]);
        }
    };

    const handleSelectNotification = (notificationId: number, checked: boolean) => {
        if (checked) {
            setSelectedNotifications([...selectedNotifications, notificationId]);
        } else {
            setSelectedNotifications(selectedNotifications.filter(id => id !== notificationId));
        }
    };

    const handleFilterChange = (key: string, value: string) => {
        const newFilters = { ...filters, [key]: value === 'all' ? undefined : value };
        router.get('/alerts/notifications', newFilters, { preserveState: true });
    };

    const filteredNotifications = notifications.data.filter(notification =>
        notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        notification.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
        notification.alert_rule.name.toLowerCase().includes(searchTerm.toLowerCase())
    );

    return (
        <AppLayout>
            <Head title="Alert Notifications" />

            <div className="p-8 space-y-8">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Alert Notifications</h1>
                        <p className="text-gray-600 mt-1">View and manage alert notifications from your monitoring rules</p>
                    </div>
                    {selectedNotifications.length > 0 && (
                        <div className="flex items-center gap-2">
                            <Button
                                variant="outline"
                                onClick={handleBulkAcknowledge}
                                className="text-blue-600 hover:text-blue-700"
                            >
                                <CheckCircle className="w-4 h-4 mr-2" />
                                Acknowledge ({selectedNotifications.length})
                            </Button>
                            <Button
                                variant="outline"
                                onClick={handleBulkDelete}
                                className="text-red-600 hover:text-red-700"
                            >
                                <Trash2 className="w-4 h-4 mr-2" />
                                Delete ({selectedNotifications.length})
                            </Button>
                        </div>
                    )}
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Notifications</CardTitle>
                            <Bell className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Pending</CardTitle>
                            <Clock className="h-4 w-4 text-yellow-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Unacknowledged</CardTitle>
                            <AlertTriangle className="h-4 w-4 text-orange-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-orange-600">{stats.unacknowledged}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Failed</CardTitle>
                            <XCircle className="h-4 w-4 text-red-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-red-600">{stats.failed}</div>
                        </CardContent>
                    </Card>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Filter className="w-5 h-5" />
                            Filters
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Status</label>
                                <Select
                                    value={filters.status || 'all'}
                                    onValueChange={(value) => handleFilterChange('status', value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Statuses</SelectItem>
                                        {Object.entries(statuses).map(([key, label]) => (
                                            <SelectItem key={key} value={key}>{label}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>

                            <div className="space-y-2">
                                <label className="text-sm font-medium">Severity</label>
                                <Select
                                    value={filters.severity || 'all'}
                                    onValueChange={(value) => handleFilterChange('severity', value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Severities</SelectItem>
                                        {Object.entries(severityLevels).map(([key, label]) => (
                                            <SelectItem key={key} value={key}>{label}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>

                            <div className="space-y-2">
                                <label className="text-sm font-medium">Date From</label>
                                <Input
                                    type="date"
                                    value={filters.date_from || ''}
                                    onChange={(e) => handleFilterChange('date_from', e.target.value)}
                                />
                            </div>

                            <div className="space-y-2">
                                <label className="text-sm font-medium">Date To</label>
                                <Input
                                    type="date"
                                    value={filters.date_to || ''}
                                    onChange={(e) => handleFilterChange('date_to', e.target.value)}
                                />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Search */}
                <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                        placeholder="Search notifications..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                    />
                </div>

                {/* Notifications List */}
                <div className="space-y-4">
                    {filteredNotifications.length === 0 ? (
                        <Card>
                            <CardContent className="flex flex-col items-center justify-center py-12">
                                <Bell className="w-12 h-12 text-gray-400 mb-4" />
                                <h3 className="text-lg font-medium text-gray-900 mb-2">No notifications found</h3>
                                <p className="text-gray-600 text-center">
                                    {searchTerm || Object.keys(filters).length > 0
                                        ? 'No notifications match your search criteria.'
                                        : 'No alert notifications have been triggered yet.'}
                                </p>
                            </CardContent>
                        </Card>
                    ) : (
                        <>
                            {/* Select All Header */}
                            <Card>
                                <CardContent className="p-4">
                                    <div className="flex items-center gap-3">
                                        <Checkbox
                                            checked={selectedNotifications.length === notifications.data.length}
                                            onCheckedChange={handleSelectAll}
                                        />
                                        <span className="text-sm font-medium">
                                            Select All ({notifications.data.length} notifications)
                                        </span>
                                    </div>
                                </CardContent>
                            </Card>

                            {filteredNotifications.map((notification) => {
                                const ChannelIcon = channelIcons[notification.channel];
                                return (
                                    <Card key={notification.id} className="hover:shadow-md transition-shadow">
                                        <CardContent className="p-6">
                                            <div className="flex items-start gap-4">
                                                <Checkbox
                                                    checked={selectedNotifications.includes(notification.id)}
                                                    onCheckedChange={(checked: boolean) =>
                                                        handleSelectNotification(notification.id, checked)
                                                    }
                                                />

                                                <div className="flex-1">
                                                    <div className="flex items-center gap-3 mb-2">
                                                        <h3 className="text-lg font-semibold text-gray-900">{notification.title}</h3>
                                                        <Badge className={severityColors[notification.severity]}>
                                                            {notification.severity.toUpperCase()}
                                                        </Badge>
                                                        <Badge className={statusColors[notification.status]}>
                                                            {notification.status.toUpperCase()}
                                                        </Badge>
                                                        <div className="flex items-center gap-1">
                                                            <ChannelIcon className="w-4 h-4" />
                                                            <span className="text-sm text-gray-500">{notification.channel}</span>
                                                        </div>
                                                    </div>

                                                    <p className="text-gray-600 mb-3">{notification.message}</p>

                                                    <div className="flex items-center gap-6 text-sm text-gray-500">
                                                        <div className="flex items-center gap-1">
                                                            <span>Rule: {notification.alert_rule.name}</span>
                                                        </div>
                                                        {notification.n8n_instance && (
                                                            <div className="flex items-center gap-1">
                                                                <span>Instance: {notification.n8n_instance.name}</span>
                                                            </div>
                                                        )}
                                                        {notification.workflow && (
                                                            <div className="flex items-center gap-1">
                                                                <span>Workflow: {notification.workflow.name}</span>
                                                            </div>
                                                        )}
                                                        <div className="flex items-center gap-1">
                                                            <Clock className="w-4 h-4" />
                                                            <span>{new Date(notification.triggered_at).toLocaleString()}</span>
                                                        </div>
                                                    </div>

                                                    {notification.acknowledged_at && notification.acknowledged_by && (
                                                        <div className="mt-2 text-sm text-blue-600 bg-blue-50 p-2 rounded">
                                                            Acknowledged by {notification.acknowledged_by.name} on {new Date(notification.acknowledged_at).toLocaleString()}
                                                        </div>
                                                    )}
                                                </div>

                                                <div className="flex items-center gap-2">
                                                    {notification.status !== 'acknowledged' && (
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() => handleAcknowledge(notification.id)}
                                                            className="text-blue-600 hover:text-blue-700"
                                                        >
                                                            <CheckCircle className="w-4 h-4" />
                                                        </Button>
                                                    )}
                                                    <Link href={`/alerts/notifications/${notification.id}`}>
                                                        <Button variant="outline" size="sm">
                                                            <Eye className="w-4 h-4" />
                                                        </Button>
                                                    </Link>
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        onClick={() => handleDelete(notification.id)}
                                                        className="text-red-600 hover:text-red-700"
                                                    >
                                                        <Trash2 className="w-4 h-4" />
                                                    </Button>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                );
                            })}
                        </>
                    )}
                </div>
            </div>
        </AppLayout>
    );
}
