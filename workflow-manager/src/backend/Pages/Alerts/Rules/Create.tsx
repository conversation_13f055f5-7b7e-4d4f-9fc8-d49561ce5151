import React, { useState } from 'react';
import { Head, useF<PERSON>, Link } from '@inertiajs/react';
import AppLayout from '@/Layouts/AppLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { ArrowLeft, Save, AlertTriangle } from 'lucide-react';

interface N8nInstance {
    id: number;
    name: string;
    url: string;
}

interface Workflow {
    id: number;
    name: string;
    n8n_instance_id: number;
    n8n_instance: {
        id: number;
        name: string;
    };
}

interface AlertChannel {
    id: number;
    name: string;
    type: string;
}

interface Props {
    instances: N8nInstance[];
    workflows: Workflow[];
    channels: AlertChannel[];
    types: Record<string, string>;
    severityLevels: Record<string, string>;
    operators: Record<string, string>;
}

export default function Create({ instances, workflows, channels, types, severityLevels, operators }: Props) {
    const { data, setData, post, processing, errors } = useForm({
        name: '',
        description: '',
        type: '',
        severity: 'medium',
        is_active: true,
        n8n_instance_id: '',
        workflow_id: '',
        threshold_value: '',
        threshold_operator: '>',
        time_window: '300',
        send_email: true,
        send_slack: false,
        cooldown_minutes: '15',
        channels: [] as number[],
    });

    const [filteredWorkflows, setFilteredWorkflows] = useState<Workflow[]>(workflows);

    const handleInstanceChange = (instanceId: string) => {
        setData('n8n_instance_id', instanceId);
        setData('workflow_id', '');

        if (instanceId) {
            const filtered = workflows.filter(w => w.n8n_instance_id === parseInt(instanceId));
            setFilteredWorkflows(filtered);
        } else {
            setFilteredWorkflows(workflows);
        }
    };

    const handleChannelToggle = (channelId: number) => {
        const currentChannels = data.channels;
        if (currentChannels.includes(channelId)) {
            setData('channels', currentChannels.filter(id => id !== channelId));
        } else {
            setData('channels', [...currentChannels, channelId]);
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post('/alerts/rules');
    };

    const requiresThreshold = ['execution_time', 'success_rate'].includes(data.type);

    return (
        <AppLayout>
            <Head title="Create Alert Rule" />

            <div className="p-8 space-y-8">
                {/* Header */}
                <div className="flex items-center gap-4">
                    <Link href="/alerts/rules">
                        <Button variant="outline" size="sm">
                            <ArrowLeft className="w-4 h-4 mr-2" />
                            Back to Rules
                        </Button>
                    </Link>
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Create Alert Rule</h1>
                        <p className="text-gray-600 mt-1">Configure a new alert rule to monitor your N8N instances</p>
                    </div>
                </div>

                <form onSubmit={handleSubmit} className="space-y-8">
                    {/* Basic Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Basic Information</CardTitle>
                            <CardDescription>Configure the basic settings for your alert rule</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="name">Rule Name *</Label>
                                    <Input
                                        id="name"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        placeholder="e.g., Critical Workflow Failures"
                                        className={errors.name ? 'border-red-500' : ''}
                                    />
                                    {errors.name && <p className="text-sm text-red-600">{errors.name}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="severity">Severity Level *</Label>
                                    <Select value={data.severity} onValueChange={(value) => setData('severity', value)}>
                                        <SelectTrigger className={errors.severity ? 'border-red-500' : ''}>
                                            <SelectValue placeholder="Select severity" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {Object.entries(severityLevels).map(([key, label]) => (
                                                <SelectItem key={key} value={key}>{label}</SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.severity && <p className="text-sm text-red-600">{errors.severity}</p>}
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="description">Description</Label>
                                <Textarea
                                    id="description"
                                    value={data.description}
                                    onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setData('description', e.target.value)}
                                    placeholder="Describe what this alert rule monitors..."
                                    rows={3}
                                />
                            </div>

                            <div className="flex items-center space-x-2">
                                <Switch
                                    id="is_active"
                                    checked={data.is_active}
                                    onCheckedChange={(checked) => setData('is_active', checked)}
                                />
                                <Label htmlFor="is_active">Enable this alert rule</Label>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Alert Type & Target */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Alert Type & Target</CardTitle>
                            <CardDescription>Define what this alert rule should monitor</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="type">Alert Type *</Label>
                                <Select value={data.type} onValueChange={(value) => setData('type', value)}>
                                    <SelectTrigger className={errors.type ? 'border-red-500' : ''}>
                                        <SelectValue placeholder="Select alert type" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {Object.entries(types).map(([key, label]) => (
                                            <SelectItem key={key} value={key}>{label}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                {errors.type && <p className="text-sm text-red-600">{errors.type}</p>}
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="n8n_instance_id">N8N Instance</Label>
                                    <Select
                                        value={data.n8n_instance_id}
                                        onValueChange={handleInstanceChange}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select instance (optional)" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="">All Instances</SelectItem>
                                            {instances.map((instance) => (
                                                <SelectItem key={instance.id} value={instance.id.toString()}>
                                                    {instance.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="workflow_id">Workflow</Label>
                                    <Select
                                        value={data.workflow_id}
                                        onValueChange={(value) => setData('workflow_id', value)}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select workflow (optional)" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="">All Workflows</SelectItem>
                                            {filteredWorkflows.map((workflow) => (
                                                <SelectItem key={workflow.id} value={workflow.id.toString()}>
                                                    {workflow.name} ({workflow.n8n_instance.name})
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Conditions */}
                    {requiresThreshold && (
                        <Card>
                            <CardHeader>
                                <CardTitle>Alert Conditions</CardTitle>
                                <CardDescription>Define when this alert should trigger</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="threshold_operator">Operator</Label>
                                        <Select
                                            value={data.threshold_operator}
                                            onValueChange={(value) => setData('threshold_operator', value)}
                                        >
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {Object.entries(operators).map(([key, label]) => (
                                                    <SelectItem key={key} value={key}>{label}</SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="threshold_value">
                                            Threshold Value {data.type === 'execution_time' ? '(seconds)' : '(%)'}
                                        </Label>
                                        <Input
                                            id="threshold_value"
                                            type="number"
                                            value={data.threshold_value}
                                            onChange={(e) => setData('threshold_value', e.target.value)}
                                            placeholder={data.type === 'execution_time' ? '300' : '95'}
                                        />
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="time_window">Time Window (seconds)</Label>
                                        <Input
                                            id="time_window"
                                            type="number"
                                            value={data.time_window}
                                            onChange={(e) => setData('time_window', e.target.value)}
                                            placeholder="300"
                                        />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {/* Notification Settings */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Notification Settings</CardTitle>
                            <CardDescription>Configure how and when notifications are sent</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="cooldown_minutes">Cooldown Period (minutes)</Label>
                                <Input
                                    id="cooldown_minutes"
                                    type="number"
                                    value={data.cooldown_minutes}
                                    onChange={(e) => setData('cooldown_minutes', e.target.value)}
                                    placeholder="15"
                                />
                                <p className="text-sm text-gray-500">
                                    Minimum time between notifications to prevent spam
                                </p>
                            </div>

                            {channels.length > 0 && (
                                <div className="space-y-2">
                                    <Label>Notification Channels</Label>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                        {channels.map((channel) => (
                                            <div key={channel.id} className="flex items-center space-x-2">
                                                <input
                                                    type="checkbox"
                                                    id={`channel-${channel.id}`}
                                                    checked={data.channels.includes(channel.id)}
                                                    onChange={() => handleChannelToggle(channel.id)}
                                                    className="rounded border-gray-300"
                                                />
                                                <Label htmlFor={`channel-${channel.id}`} className="text-sm">
                                                    {channel.name} ({channel.type})
                                                </Label>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Actions */}
                    <div className="flex items-center justify-end gap-4">
                        <Link href="/alerts/rules">
                            <Button variant="outline">Cancel</Button>
                        </Link>
                        <Button type="submit" disabled={processing}>
                            <Save className="w-4 h-4 mr-2" />
                            {processing ? 'Creating...' : 'Create Alert Rule'}
                        </Button>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
}
