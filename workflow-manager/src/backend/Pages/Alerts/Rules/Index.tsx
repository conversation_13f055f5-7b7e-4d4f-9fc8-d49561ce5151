import React, { useState } from 'react';
import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import AppLayout from '@/Layouts/AppLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
    AlertTriangle, 
    Plus, 
    Search, 
    Settings, 
    Eye, 
    Edit, 
    Trash2,
    Power,
    PowerOff,
    TestTube,
    Clock,
    Target
} from 'lucide-react';

interface AlertRule {
    id: number;
    name: string;
    description: string;
    type: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    is_active: boolean;
    n8n_instance?: {
        id: number;
        name: string;
    };
    workflow?: {
        id: number;
        name: string;
    };
    creator: {
        id: number;
        name: string;
    };
    last_triggered_at?: string;
    trigger_count: number;
    cooldown_minutes: number;
    created_at: string;
}

interface Props {
    alertRules: {
        data: AlertRule[];
        links: any;
        meta: any;
    };
    stats: {
        total: number;
        active: number;
        triggered_today: number;
    };
}

const severityColors = {
    low: 'bg-blue-100 text-blue-800 border-blue-200',
    medium: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    high: 'bg-orange-100 text-orange-800 border-orange-200',
    critical: 'bg-red-100 text-red-800 border-red-200',
};

const typeLabels = {
    workflow_failure: 'Workflow Failure',
    instance_down: 'Instance Down',
    execution_time: 'Execution Time',
    success_rate: 'Success Rate',
    custom: 'Custom',
};

export default function Index({ alertRules, stats }: Props) {
    const [searchTerm, setSearchTerm] = useState('');

    const handleToggleRule = (ruleId: number) => {
        router.post(`/alerts/rules/${ruleId}/toggle`, {}, {
            preserveScroll: true,
        });
    };

    const handleTestRule = (ruleId: number) => {
        router.post(`/alerts/rules/${ruleId}/test`, {}, {
            preserveScroll: true,
        });
    };

    const handleDeleteRule = (ruleId: number) => {
        if (confirm('Are you sure you want to delete this alert rule?')) {
            router.delete(`/alerts/rules/${ruleId}`, {
                preserveScroll: true,
            });
        }
    };

    const filteredRules = alertRules.data.filter(rule =>
        rule.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        rule.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        typeLabels[rule.type as keyof typeof typeLabels]?.toLowerCase().includes(searchTerm.toLowerCase())
    );

    return (
        <AppLayout>
            <Head title="Alert Rules" />

            <div className="p-8 space-y-8">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Alert Rules</h1>
                        <p className="text-gray-600 mt-1">Configure and manage alert rules for monitoring your N8N instances</p>
                    </div>
                    <Link href="/alerts/rules/create">
                        <Button className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700">
                            <Plus className="w-4 h-4 mr-2" />
                            Create Alert Rule
                        </Button>
                    </Link>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Rules</CardTitle>
                            <Settings className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Active Rules</CardTitle>
                            <Power className="h-4 w-4 text-green-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-600">{stats.active}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Triggered Today</CardTitle>
                            <AlertTriangle className="h-4 w-4 text-orange-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-orange-600">{stats.triggered_today}</div>
                        </CardContent>
                    </Card>
                </div>

                {/* Search */}
                <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                        placeholder="Search alert rules..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                    />
                </div>

                {/* Alert Rules List */}
                <div className="space-y-4">
                    {filteredRules.length === 0 ? (
                        <Card>
                            <CardContent className="flex flex-col items-center justify-center py-12">
                                <AlertTriangle className="w-12 h-12 text-gray-400 mb-4" />
                                <h3 className="text-lg font-medium text-gray-900 mb-2">No alert rules found</h3>
                                <p className="text-gray-600 text-center mb-4">
                                    {searchTerm ? 'No rules match your search criteria.' : 'Get started by creating your first alert rule.'}
                                </p>
                                {!searchTerm && (
                                    <Link href="/alerts/rules/create">
                                        <Button>
                                            <Plus className="w-4 h-4 mr-2" />
                                            Create Alert Rule
                                        </Button>
                                    </Link>
                                )}
                            </CardContent>
                        </Card>
                    ) : (
                        filteredRules.map((rule) => (
                            <Card key={rule.id} className="hover:shadow-md transition-shadow">
                                <CardContent className="p-6">
                                    <div className="flex items-start justify-between">
                                        <div className="flex-1">
                                            <div className="flex items-center gap-3 mb-2">
                                                <h3 className="text-lg font-semibold text-gray-900">{rule.name}</h3>
                                                <Badge className={severityColors[rule.severity]}>
                                                    {rule.severity.toUpperCase()}
                                                </Badge>
                                                <Badge variant="outline">
                                                    {typeLabels[rule.type as keyof typeof typeLabels]}
                                                </Badge>
                                                {rule.is_active ? (
                                                    <Badge className="bg-green-100 text-green-800 border-green-200">
                                                        <Power className="w-3 h-3 mr-1" />
                                                        Active
                                                    </Badge>
                                                ) : (
                                                    <Badge className="bg-gray-100 text-gray-800 border-gray-200">
                                                        <PowerOff className="w-3 h-3 mr-1" />
                                                        Inactive
                                                    </Badge>
                                                )}
                                            </div>
                                            
                                            {rule.description && (
                                                <p className="text-gray-600 mb-3">{rule.description}</p>
                                            )}

                                            <div className="flex items-center gap-6 text-sm text-gray-500">
                                                {rule.n8n_instance && (
                                                    <div className="flex items-center gap-1">
                                                        <Target className="w-4 h-4" />
                                                        <span>Instance: {rule.n8n_instance.name}</span>
                                                    </div>
                                                )}
                                                {rule.workflow && (
                                                    <div className="flex items-center gap-1">
                                                        <Target className="w-4 h-4" />
                                                        <span>Workflow: {rule.workflow.name}</span>
                                                    </div>
                                                )}
                                                <div className="flex items-center gap-1">
                                                    <Clock className="w-4 h-4" />
                                                    <span>Cooldown: {rule.cooldown_minutes}m</span>
                                                </div>
                                                <div className="flex items-center gap-1">
                                                    <AlertTriangle className="w-4 h-4" />
                                                    <span>Triggered: {rule.trigger_count} times</span>
                                                </div>
                                            </div>

                                            {rule.last_triggered_at && (
                                                <div className="mt-2 text-sm text-gray-500">
                                                    Last triggered: {new Date(rule.last_triggered_at).toLocaleString()}
                                                </div>
                                            )}
                                        </div>

                                        <div className="flex items-center gap-2 ml-4">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => handleToggleRule(rule.id)}
                                            >
                                                {rule.is_active ? (
                                                    <PowerOff className="w-4 h-4" />
                                                ) : (
                                                    <Power className="w-4 h-4" />
                                                )}
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => handleTestRule(rule.id)}
                                            >
                                                <TestTube className="w-4 h-4" />
                                            </Button>
                                            <Link href={`/alerts/rules/${rule.id}`}>
                                                <Button variant="outline" size="sm">
                                                    <Eye className="w-4 h-4" />
                                                </Button>
                                            </Link>
                                            <Link href={`/alerts/rules/${rule.id}/edit`}>
                                                <Button variant="outline" size="sm">
                                                    <Edit className="w-4 h-4" />
                                                </Button>
                                            </Link>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => handleDeleteRule(rule.id)}
                                                className="text-red-600 hover:text-red-700"
                                            >
                                                <Trash2 className="w-4 h-4" />
                                            </Button>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        ))
                    )}
                </div>

                {/* Pagination would go here if needed */}
            </div>
        </AppLayout>
    );
}
