import React, { useEffect, useState } from 'react';
import { Head } from '@inertiajs/react';
import AppLayout from '@/Layouts/AppLayout';
import MetricCard from '@/components/MetricCard';
import LineChartCard from '@/components/LineChartCard';
import LiveExecutionTracker from '@/components/LiveExecutionTracker';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import axios from 'axios';
import { Activity, CheckCircle2, TrendingUp, Zap, AlertTriangle } from 'lucide-react';

interface DashboardData {
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
    avgExecutionTime: number;
    monthlyExecutions: { name: string; executions: number }[];
    recentExecutions: any[]; // Replace any with a proper type
    topWorkflows: any[]; // Replace any with a proper type
    trends?: {
        totalExecutions: { value: number; isPositive: boolean };
        successfulExecutions: { value: number; isPositive: boolean };
        failedExecutions: { value: number; isPositive: boolean };
        avgExecutionTime: { value: number; isPositive: boolean };
    };
    dataSource?: 'real_n8n_api' | 'cached_database' | 'no_instances_connected';
    lastSyncMessage?: string;
    dataAge?: string;
    instanceCount?: number;
    message?: string;
}

interface Props {
    dashboardData: DashboardData;
}

export default function Index({ dashboardData: initialDashboardData }: Props) {
    const [dashboardData, setDashboardData] = useState<DashboardData>(initialDashboardData);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchDashboardData = () => {
            axios.get('/api/dashboard-data')
                .then(response => {
                    setDashboardData(response.data);
                    setError(null);
                })
                .catch(err => {
                    setError('Failed to load dashboard data.');
                    console.error(err);
                });
        };

        // Set up polling for real-time updates (every 30 seconds)
        const interval = setInterval(fetchDashboardData, 30000);

        // Clean up the interval when the component unmounts
        return () => clearInterval(interval);
    }, []);

    if (error) {
        return (
            <AppLayout>
                <div className="p-8">
                    <Card className="glass-card p-8 rounded-2xl border-red-200">
                        <div className="flex flex-col items-center justify-center space-y-4">
                            <div className="p-4 rounded-2xl bg-gradient-to-br from-red-500 to-orange-500 shadow-lg">
                                <AlertTriangle className="w-8 h-8 text-white" />
                            </div>
                            <div className="text-center">
                                <h3 className="text-lg font-semibold text-red-600">
                                    Error Loading Dashboard
                                </h3>
                                <p className="text-sm text-slate-500 mt-1">
                                    {error}
                                </p>
                            </div>
                        </div>
                    </Card>
                </div>
            </AppLayout>
        );
    }

    if (!dashboardData) {
        return (
            <AppLayout>
                <div className="p-8">
                    <Card className="glass-card p-8 rounded-2xl">
                        <div className="flex flex-col items-center justify-center space-y-4">
                            <div className="p-4 rounded-2xl bg-gradient-to-br from-slate-400 to-slate-600 shadow-lg">
                                <Activity className="w-8 h-8 text-white" />
                            </div>
                            <div className="text-center">
                                <h3 className="text-lg font-semibold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-slate-100 dark:to-slate-300 bg-clip-text text-transparent">
                                    No Data Available
                                </h3>
                                <p className="text-sm text-slate-500 dark:text-slate-400 mt-1">
                                    Start by creating some workflows to see analytics here.
                                </p>
                            </div>
                        </div>
                    </Card>
                </div>
            </AppLayout>
        );
    }

    return (
        <AppLayout title="Dashboard">
            <Head title="Dashboard" />
            <div className="p-8 space-y-8">
                {/* Hero Section */}
                <div className="relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-teal-600/10 rounded-3xl" />
                    <div className="relative p-8 rounded-3xl bg-white/50 dark:bg-slate-900/50 backdrop-blur-xl border border-slate-200/60 dark:border-slate-700/60">
                        <div className="flex items-center justify-between">
                            <div>
                                <h1 className="text-4xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-slate-100 dark:to-slate-300 bg-clip-text text-transparent mb-2">
                                    Analytics Dashboard
                                </h1>
                                <p className="text-lg text-slate-600 dark:text-slate-300">
                                    Monitor your N8N workflows and automation performance
                                </p>
                            </div>
                            <div className="hidden md:flex items-center space-x-4">
                                <div className="p-4 rounded-2xl bg-gradient-to-br from-blue-500 to-teal-600 shadow-lg">
                                    <TrendingUp className="w-8 h-8 text-white" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Data Source Alert for Cached Data */}
                {dashboardData.dataSource === 'cached_database' && (
                    <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-xl p-4">
                        <div className="flex items-center space-x-3">
                            <div className="w-3 h-3 bg-amber-500 rounded-full animate-pulse"></div>
                            <div>
                                <h3 className="text-sm font-medium text-amber-800 dark:text-amber-200">
                                    Showing Cached Data
                                </h3>
                                <p className="text-xs text-amber-700 dark:text-amber-300 mt-1">
                                    {dashboardData.lastSyncMessage}
                                    {dashboardData.dataAge && (
                                        <span className="block mt-1">{dashboardData.dataAge}</span>
                                    )}
                                </p>
                            </div>
                        </div>
                    </div>
                )}

                {/* Metrics Grid */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                    <MetricCard
                        title="Total Executions"
                        value={dashboardData.totalExecutions.toLocaleString()}
                        description="Total workflows executed across all instances"
                        icon={<Activity className="h-5 w-5" />}
                        gradient="from-blue-500 to-cyan-500"
                        trend={dashboardData.trends?.totalExecutions}
                    />
                    <MetricCard
                        title="Successful Executions"
                        value={dashboardData.successfulExecutions.toLocaleString()}
                        description="Successfully completed workflows"
                        icon={<CheckCircle2 className="h-5 w-5" />}
                        gradient="from-emerald-500 to-green-500"
                        trend={dashboardData.trends?.successfulExecutions}
                    />
                    <MetricCard
                        title="Failed Executions"
                        value={dashboardData.failedExecutions.toLocaleString()}
                        description="Workflows that encountered errors"
                        icon={<AlertTriangle className="h-5 w-5" />}
                        gradient="from-red-500 to-orange-500"
                        trend={dashboardData.trends?.failedExecutions}
                    />
                    <MetricCard
                        title="Avg. Execution Time"
                        value={`${dashboardData.avgExecutionTime}s`}
                        description="Average workflow completion time"
                        icon={<Zap className="h-5 w-5" />}
                        gradient="from-amber-500 to-orange-500"
                        trend={dashboardData.trends?.avgExecutionTime}
                    />
                </div>

                {/* Charts and Activity Section */}
                <div className="grid gap-6 lg:grid-cols-7">
                    <div className="lg:col-span-4">
                        <Card className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-slate-200/60 dark:border-slate-700/60 shadow-lg">
                            <CardHeader className="pb-4">
                                <CardTitle className="text-xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-slate-100 dark:to-slate-300 bg-clip-text text-transparent">
                                    Monthly Executions Trend
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <LineChartCard
                                    title=""
                                    data={dashboardData.monthlyExecutions}
                                    dataKey="name"
                                    lineKey="executions"
                                />
                            </CardContent>
                        </Card>
                    </div>

                    <Card className="lg:col-span-3 bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-slate-200/60 dark:border-slate-700/60 shadow-lg">
                        <CardHeader className="pb-4">
                            <CardTitle className="text-xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-slate-100 dark:to-slate-300 bg-clip-text text-transparent">
                                Recent Activity
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {dashboardData.recentExecutions.slice(0, 5).map((execution) => (
                                    <div key={execution.id} className="flex items-center justify-between p-4 rounded-xl bg-slate-50 dark:bg-slate-800 border border-slate-200/60 dark:border-slate-700/60">
                                        <div className="flex items-center space-x-3">
                                            <div className={`w-3 h-3 rounded-full ${
                                                execution.status === 'success'
                                                    ? 'bg-emerald-500'
                                                    : 'bg-red-500'
                                            }`} />
                                            <div>
                                                <p className="font-medium text-slate-900 dark:text-slate-100 truncate max-w-[150px]">
                                                    {execution.workflow.name}
                                                </p>
                                                <p className="text-xs text-slate-500 dark:text-slate-400">
                                                    {new Date(execution.created_at).toLocaleTimeString()}
                                                </p>
                                            </div>
                                        </div>
                                        <Badge
                                            variant={execution.status === 'success' ? 'default' : 'destructive'}
                                            className="text-xs"
                                        >
                                            {execution.status}
                                        </Badge>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Live Execution Tracking */}
                <LiveExecutionTracker refreshInterval={5000} maxExecutions={8} />

                {/* No Workflows Info - Show when no workflows available */}
                {(!dashboardData.topWorkflows || dashboardData.topWorkflows.length === 0) && (
                    <div className="text-center py-4">
                        <p className="text-sm text-slate-500 dark:text-slate-400">
                            {dashboardData.dataSource === 'cached_database'
                                ? 'No workflow performance data available in cache. Connect N8N instances to start collecting workflow data.'
                                : dashboardData.totalExecutions === 0
                                ? 'No workflow executions found. Start running workflows to see performance analytics.'
                                : 'Workflows need at least 3 executions to appear in performance rankings.'
                            }
                        </p>
                    </div>
                )}

                {/* Top Workflows Section - Only show if there are workflows */}
                {dashboardData.topWorkflows && dashboardData.topWorkflows.length > 0 && (
                    <Card className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-slate-200/60 dark:border-slate-700/60 shadow-lg">
                        <CardHeader className="pb-4">
                            <div className="flex items-center justify-between">
                                <CardTitle className="text-xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-slate-100 dark:to-slate-300 bg-clip-text text-transparent">
                                    Top Performing Workflows
                                </CardTitle>
                                {/* Data Source Indicator */}
                                {dashboardData.dataSource === 'cached_database' && (
                                    <div className="flex items-center space-x-2">
                                        <div className="w-2 h-2 bg-amber-500 rounded-full animate-pulse"></div>
                                        <span className="text-xs text-amber-600 dark:text-amber-400 font-medium">
                                            CACHED DATA
                                        </span>
                                    </div>
                                )}
                                {dashboardData.dataSource === 'real_n8n_api' && (
                                    <div className="flex items-center space-x-2">
                                        <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                                        <span className="text-xs text-emerald-600 dark:text-emerald-400 font-medium">
                                            LIVE DATA
                                        </span>
                                    </div>
                                )}
                            </div>
                            {/* Show cache message if applicable */}
                            {dashboardData.dataSource === 'cached_database' && dashboardData.lastSyncMessage && (
                                <div className="mt-2 p-2 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
                                    <p className="text-xs text-amber-700 dark:text-amber-300">
                                        ⚠️ {dashboardData.lastSyncMessage}
                                        {dashboardData.dataAge && (
                                            <span className="block mt-1 text-amber-600 dark:text-amber-400">
                                                {dashboardData.dataAge}
                                            </span>
                                        )}
                                    </p>
                                </div>
                            )}
                        </CardHeader>
                        <CardContent>
                            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                                {dashboardData.topWorkflows.slice(0, 6).map((workflow, index) => (
                                    <div
                                        key={workflow.name}
                                        className="p-4 rounded-xl bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-700 border border-slate-200/60 dark:border-slate-700/60 hover:shadow-lg transition-all duration-300"
                                    >
                                        <div className="flex items-center justify-between mb-3">
                                            <div className={`w-8 h-8 rounded-lg bg-gradient-to-br ${
                                                index === 0 ? 'from-yellow-400 to-orange-500' :
                                                index === 1 ? 'from-gray-400 to-gray-600' :
                                                index === 2 ? 'from-amber-600 to-yellow-700' :
                                                'from-blue-500 to-teal-600'
                                            } flex items-center justify-center text-white font-bold text-sm`}>
                                                {index + 1}
                                            </div>
                                            <div className="text-right">
                                                <div className="text-2xl font-bold text-slate-900 dark:text-slate-100">
                                                    {workflow.performance_score}
                                                </div>
                                                <div className="text-xs text-slate-500 dark:text-slate-400">
                                                    performance score
                                                </div>
                                            </div>
                                        </div>
                                        <h3 className="font-semibold text-slate-900 dark:text-slate-100 truncate mb-2">
                                            {workflow.name}
                                        </h3>
                                        <div className="space-y-1 text-xs">
                                            <div className="flex justify-between">
                                                <span className="text-slate-500 dark:text-slate-400">Success Rate:</span>
                                                <span className={`font-medium ${
                                                    workflow.success_rate >= 90 ? 'text-emerald-600 dark:text-emerald-400' :
                                                    workflow.success_rate >= 70 ? 'text-yellow-600 dark:text-yellow-400' :
                                                    'text-red-600 dark:text-red-400'
                                                }`}>
                                                    {workflow.success_rate}%
                                                </span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-slate-500 dark:text-slate-400">Executions:</span>
                                                <span className="text-slate-900 dark:text-slate-100 font-medium">
                                                    {workflow.execution_count}
                                                </span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-slate-500 dark:text-slate-400">Avg Time:</span>
                                                <span className="text-slate-900 dark:text-slate-100 font-medium">
                                                    {workflow.avg_execution_time}s
                                                </span>
                                            </div>
                                        </div>
                                        <div className="flex items-center mt-3 space-x-2">
                                            <div className={`w-2 h-2 rounded-full ${
                                                workflow.is_active ? 'bg-emerald-500' : 'bg-slate-400'
                                            }`} />
                                            <span className="text-xs text-slate-500 dark:text-slate-400">
                                                {workflow.is_active ? 'Active' : 'Inactive'}
                                            </span>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
