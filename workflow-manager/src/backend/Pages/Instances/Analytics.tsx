import React, { useEffect, useState } from 'react';
import AppLayout from '@/Layouts/AppLayout';
import { PageProps } from '@/types';
import MetricCard from '@/components/MetricCard';
import LineChartCard from '@/components/LineChartCard';
import { LoadingCard } from '@/components/LoadingSpinner';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Activity, CheckCircle2, XCircle, Clock, AlertTriangle, Play, Zap } from 'lucide-react';
import axios from 'axios';

interface N8nInstance {
    id: number;
    name: string;
    url: string;
}

interface AnalyticsData {
    instance: N8nInstance;
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
    avgExecutionTime: number;
    monthlyExecutions: { name: string; executions: number }[];
    activeWorkflows?: number;
    runningExecutions?: number;
    connectionStatus?: {
        success: boolean;
        message: string;
        response_time: number;
        n8n_version?: string;
    };
    dataSource?: 'n8n_api' | 'database_fallback';
    warning?: string;
    connectionError?: boolean;
}

export default function Analytics({ instance }: PageProps<{ instance: N8nInstance }>) {
    const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const PageHeader = () => (
        <div>
            <h2 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
                Analytics for {instance.name}
            </h2>
            <p className="mt-2 text-slate-600">
                Instance performance metrics and insights
            </p>
        </div>
    );

    useEffect(() => {
        const fetchAnalytics = (isInitial = false) => {
            axios.get(`/api/analytics/instance/${instance.id}`)
                .then(response => {
                    setAnalyticsData(response.data);
                    setError(null);
                })
                .catch(err => {
                    if (isInitial) {
                        if (err.response?.status === 503) {
                            setError(`Connection Error: ${err.response.data.error || 'Cannot connect to N8N instance'}`);
                        } else {
                            setError('Failed to load analytics data.');
                        }
                    }
                    console.error('Analytics fetch failed:', err);
                })
                .finally(() => {
                    if (isInitial) {
                        setLoading(false);
                    }
                });
        };

        // Initial load
        fetchAnalytics(true);

        // Set up polling for real-time updates (every 30 seconds)
        const interval = setInterval(() => fetchAnalytics(false), 30000);

        // Cleanup interval on unmount
        return () => clearInterval(interval);
    }, [instance.id]);

    if (loading) {
        return (
            <AppLayout>
                <div className="p-8 space-y-6">
                    <PageHeader />
                    <LoadingCard />
                </div>
            </AppLayout>
        );
    }

    if (error) {
        return (
            <AppLayout>
                <div className="p-8 space-y-6">
                    <PageHeader />
                    <Card className="glass-card p-8 rounded-2xl border-red-200">
                        <div className="flex flex-col items-center justify-center space-y-4">
                            <div className="p-4 rounded-2xl bg-gradient-to-br from-red-500 to-orange-500 shadow-lg">
                                <AlertTriangle className="w-8 h-8 text-white" />
                            </div>
                            <div className="text-center">
                                <h3 className="text-lg font-semibold text-red-600">
                                    Error Loading Analytics
                                </h3>
                                <p className="text-sm text-slate-500 mt-1">
                                    {error}
                                </p>
                            </div>
                        </div>
                    </Card>
                </div>
            </AppLayout>
        );
    }

    if (!analyticsData) {
        return (
            <AppLayout>
                <div className="p-8 space-y-6">
                    <PageHeader />
                    <Card className="glass-card p-8 rounded-2xl">
                        <div className="flex flex-col items-center justify-center space-y-4">
                            <div className="p-4 rounded-2xl bg-gradient-to-br from-slate-400 to-slate-600 shadow-lg">
                                <Activity className="w-8 h-8 text-white" />
                            </div>
                            <div className="text-center">
                                <h3 className="text-lg font-semibold bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
                                    No Analytics Data
                                </h3>
                                <p className="text-sm text-slate-500 mt-1">
                                    No analytics data available for this instance yet.
                                </p>
                            </div>
                        </div>
                    </Card>
                </div>
            </AppLayout>
        );
    }

    return (
        <AppLayout>
            <div className="p-8 space-y-8">
                <PageHeader />

                {/* Connection Status & Data Source Info */}
                {analyticsData.dataSource && (
                    <div className="flex flex-wrap gap-4 items-center justify-between">
                        <div className="flex items-center gap-3">
                            {analyticsData.dataSource === 'n8n_api' ? (
                                <div className="flex items-center gap-2 px-3 py-1.5 bg-green-100 text-green-700 rounded-full text-sm font-medium">
                                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                    Live N8N Data
                                </div>
                            ) : (
                                <div className="flex items-center gap-2 px-3 py-1.5 bg-amber-100 text-amber-700 rounded-full text-sm font-medium">
                                    <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                                    Cached Data
                                </div>
                            )}

                            {analyticsData.connectionStatus && (
                                <div className="text-sm text-slate-600">
                                    Response: {analyticsData.connectionStatus.response_time}ms
                                    {analyticsData.connectionStatus.n8n_version && (
                                        <span className="ml-2">• N8N v{analyticsData.connectionStatus.n8n_version}</span>
                                    )}
                                </div>
                            )}
                        </div>

                        {analyticsData.warning && (
                            <div className="flex items-center gap-2 px-3 py-1.5 bg-amber-100 text-amber-700 rounded-lg text-sm">
                                <AlertTriangle className="w-4 h-4" />
                                {analyticsData.warning}
                            </div>
                        )}
                    </div>
                )}

                {/* Metrics Grid */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6">
                    <MetricCard
                        title="Total Executions"
                        value={analyticsData.totalExecutions.toLocaleString()}
                        description="Total workflows executed on this instance"
                        icon={<Activity className="h-5 w-5" />}
                        gradient="from-blue-500 to-cyan-500"
                    />
                    <MetricCard
                        title="Successful Executions"
                        value={analyticsData.successfulExecutions.toLocaleString()}
                        description="Workflows completed successfully"
                        icon={<CheckCircle2 className="h-5 w-5" />}
                        gradient="from-emerald-500 to-green-500"
                    />
                    <MetricCard
                        title="Failed Executions"
                        value={analyticsData.failedExecutions.toLocaleString()}
                        description="Workflows that encountered errors"
                        icon={<XCircle className="h-5 w-5" />}
                        gradient="from-red-500 to-orange-500"
                    />
                    <MetricCard
                        title="Avg. Execution Time"
                        value={`${analyticsData.avgExecutionTime}s`}
                        description="Average workflow completion time"
                        icon={<Clock className="h-5 w-5" />}
                        gradient="from-amber-500 to-orange-500"
                    />

                    {/* Additional metrics if available from N8N API */}
                    {analyticsData.activeWorkflows !== undefined && (
                        <MetricCard
                            title="Active Workflows"
                            value={analyticsData.activeWorkflows.toLocaleString()}
                            description="Currently active workflows"
                            icon={<Play className="h-5 w-5" />}
                            gradient="from-purple-500 to-indigo-500"
                        />
                    )}

                    {analyticsData.runningExecutions !== undefined && (
                        <MetricCard
                            title="Running Executions"
                            value={analyticsData.runningExecutions.toLocaleString()}
                            description="Currently executing workflows"
                            icon={<Zap className="h-5 w-5" />}
                            gradient="from-teal-500 to-cyan-500"
                        />
                    )}
                </div>

                {/* Chart Section */}
                <Card className="glass-card shadow-lg">
                    <CardHeader>
                        <CardTitle className="text-xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-slate-100 dark:to-slate-300 bg-clip-text text-transparent">
                            Monthly Executions Trend
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <LineChartCard
                            title=""
                            data={analyticsData.monthlyExecutions}
                            dataKey="name"
                            lineKey="executions"
                        />
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
