import React, { useState } from 'react';
import AppLayout from '@/Layouts/AppLayout';
import { useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle } from '@/components/ui/sheet';
import { Server, Key, Globe } from 'lucide-react';
import { router } from '@inertiajs/react';

export default function Create() {
    const [open, setOpen] = useState(true);
    const { data, setData, post, processing, errors, reset } = useForm({
        name: '',
        url: '',
        api_key: '',
    });

    function submit(e: React.FormEvent) {
        e.preventDefault();
        post(route('instances.store'), {
            onSuccess: () => {
                setOpen(false);
                reset();
                router.visit(route('instances.index'));
            }
        });
    }

    function handleClose() {
        setOpen(false);
        router.visit(route('instances.index'));
    }

    return (
        <AppLayout>
            <Sheet open={open} onOpenChange={setOpen}>
                <SheetContent className="w-[400px] sm:w-[540px]">
                    <SheetHeader>
                        <SheetTitle className="flex items-center gap-2">
                            <Server className="w-5 h-5" />
                            Add New N8N Instance
                        </SheetTitle>
                        <SheetDescription>
                            Connect a new N8N instance to your dashboard for monitoring and management.
                        </SheetDescription>
                    </SheetHeader>

                    <form onSubmit={submit} className="mt-6 space-y-6">
                        <div className="space-y-2">
                            <Label htmlFor="name" className="flex items-center gap-2">
                                <Server className="w-4 h-4" />
                                Instance Name
                            </Label>
                            <Input
                                id="name"
                                type="text"
                                value={data.name}
                                onChange={(e) => setData('name', e.target.value)}
                                placeholder="e.g., Production N8N, Development N8N"
                                className="w-full"
                            />
                            {errors.name && <p className="text-sm text-destructive">{errors.name}</p>}
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="url" className="flex items-center gap-2">
                                <Globe className="w-4 h-4" />
                                N8N Instance URL
                            </Label>
                            <Input
                                id="url"
                                type="url"
                                value={data.url}
                                onChange={(e) => setData('url', e.target.value)}
                                placeholder="https://your-n8n-instance.com"
                                className="w-full"
                            />
                            {errors.url && <p className="text-sm text-destructive">{errors.url}</p>}
                            <p className="text-sm text-muted-foreground">
                                Enter the full URL to your N8N instance (including https://)
                            </p>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="api_key" className="flex items-center gap-2">
                                <Key className="w-4 h-4" />
                                API Key
                            </Label>
                            <Input
                                id="api_key"
                                type="password"
                                value={data.api_key}
                                onChange={(e) => setData('api_key', e.target.value)}
                                placeholder="Enter your N8N API key"
                                className="w-full"
                            />
                            {errors.api_key && <p className="text-sm text-destructive">{errors.api_key}</p>}
                            <p className="text-sm text-muted-foreground">
                                You can find your API key in N8N Settings → API Keys
                            </p>
                        </div>

                        <div className="bg-muted/50 p-4 rounded-lg space-y-2">
                            <h4 className="font-medium text-sm">How to get your N8N API Key:</h4>
                            <div className="text-sm text-muted-foreground space-y-1">
                                <p>1. Log into your N8N instance</p>
                                <p>2. Go to Settings → API Keys</p>
                                <p>3. Click "Create API Key"</p>
                                <p>4. Copy the generated key and paste it above</p>
                            </div>
                        </div>

                        <div className="flex gap-3 pt-4">
                            <Button type="submit" disabled={processing} className="flex-1">
                                {processing ? 'Creating Instance...' : 'Create Instance'}
                            </Button>
                            <Button type="button" variant="outline" onClick={handleClose}>
                                Cancel
                            </Button>
                        </div>
                    </form>
                </SheetContent>
            </Sheet>
        </AppLayout>
    );
}
