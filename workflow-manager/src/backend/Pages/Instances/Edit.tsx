import React, { useState } from 'react';
import { Head, useF<PERSON>, Link, router } from '@inertiajs/react';
import AppLayout from '@/Layouts/AppLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
    Edit as EditIcon,
    Server,
    Globe,
    Key,
    Eye,
    EyeOff,
    ArrowLeft,
    Save,
    AlertCircle
} from 'lucide-react';
import { PageProps, N8nInstance } from '@/types';
import { generateBreadcrumbs } from '@/utils/breadcrumbs';

interface EditForm {
    name: string;
    url: string;
    api_key: string;
}

export default function Edit({ instance }: PageProps<{ instance: N8nInstance }>) {
    const [showApiKey, setShowApiKey] = useState(false);

    const { data, setData, put, processing, errors, reset } = useForm<EditForm>({
        name: instance.name,
        url: instance.url,
        api_key: '', // Don't pre-fill API key for security
    });

    const breadcrumbs = generateBreadcrumbs(`/instances/${instance.id}/edit`, {
        instanceName: instance.name
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        // Prepare data - only include api_key if it's not empty
        const submitData: any = {
            name: data.name,
            url: data.url,
        };

        // Only include api_key if it's not empty
        if (data.api_key && data.api_key.trim() !== '') {
            submitData.api_key = data.api_key;
        }

        router.put(route('instances.update', instance.id), submitData, {
            onSuccess: () => {
                // Will redirect automatically
            },
            onError: () => {
                reset('api_key');
            }
        });
    };

    return (
        <AppLayout title={`Edit Instance: ${instance.name}`} breadcrumbs={breadcrumbs}>
            <Head title={`Edit Instance: ${instance.name}`} />

            <div className="p-8">
                <div className="max-w-2xl mx-auto">
                    {/* Header */}
                    <div className="flex items-center gap-4 mb-8">
                        <Link href={route('instances.index')}>
                            <Button variant="outline" size="icon">
                                <ArrowLeft className="w-4 h-4" />
                            </Button>
                        </Link>
                        <div>
                            <h1 className="text-3xl font-bold flex items-center gap-3">
                                <EditIcon className="w-8 h-8 text-blue-600" />
                                Edit Instance
                            </h1>
                            <p className="text-muted-foreground mt-1">
                                Update the configuration for "{instance.name}"
                            </p>
                        </div>
                    </div>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Server className="w-5 h-5" />
                                Instance Configuration
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={handleSubmit} className="space-y-6">
                                {/* Instance Name */}
                                <div className="space-y-2">
                                    <Label htmlFor="name" className="flex items-center gap-2">
                                        <Server className="w-4 h-4" />
                                        Instance Name
                                    </Label>
                                    <Input
                                        id="name"
                                        type="text"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        placeholder="e.g., Production N8N, Development N8N"
                                        className="w-full"
                                        required
                                    />
                                    {errors.name && (
                                        <p className="text-sm text-destructive flex items-center gap-1">
                                            <AlertCircle className="w-3 h-3" />
                                            {errors.name}
                                        </p>
                                    )}
                                </div>

                                {/* Instance URL */}
                                <div className="space-y-2">
                                    <Label htmlFor="url" className="flex items-center gap-2">
                                        <Globe className="w-4 h-4" />
                                        Instance URL
                                    </Label>
                                    <Input
                                        id="url"
                                        type="url"
                                        value={data.url}
                                        onChange={(e) => setData('url', e.target.value)}
                                        placeholder="https://your-n8n-instance.com"
                                        className="w-full"
                                        required
                                    />
                                    {errors.url && (
                                        <p className="text-sm text-destructive flex items-center gap-1">
                                            <AlertCircle className="w-3 h-3" />
                                            {errors.url}
                                        </p>
                                    )}
                                </div>

                                {/* API Key */}
                                <div className="space-y-2">
                                    <Label htmlFor="api_key" className="flex items-center gap-2">
                                        <Key className="w-4 h-4" />
                                        API Key
                                    </Label>
                                    <div className="relative">
                                        <Input
                                            id="api_key"
                                            type={showApiKey ? "text" : "password"}
                                            value={data.api_key}
                                            onChange={(e) => setData('api_key', e.target.value)}
                                            placeholder="Leave empty to keep current API key"
                                            className="w-full pr-10"
                                        />
                                        <Button
                                            type="button"
                                            variant="ghost"
                                            size="icon"
                                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                            onClick={() => setShowApiKey(!showApiKey)}
                                        >
                                            {showApiKey ? (
                                                <EyeOff className="w-4 h-4" />
                                            ) : (
                                                <Eye className="w-4 h-4" />
                                            )}
                                        </Button>
                                    </div>
                                    {errors.api_key && (
                                        <p className="text-sm text-destructive flex items-center gap-1">
                                            <AlertCircle className="w-3 h-3" />
                                            {errors.api_key}
                                        </p>
                                    )}
                                    <p className="text-sm text-muted-foreground">
                                        Leave empty to keep the current API key. Enter a new key to update it.
                                    </p>
                                </div>

                                {/* API Key Help */}
                                <Alert>
                                    <AlertCircle className="h-4 w-4" />
                                    <AlertDescription>
                                        <strong>How to get your N8N API Key:</strong>
                                        <ol className="list-decimal list-inside mt-2 space-y-1">
                                            <li>Log into your N8N instance</li>
                                            <li>Go to Settings → API Keys</li>
                                            <li>Click "Create API Key"</li>
                                            <li>Copy the generated key and paste it above</li>
                                        </ol>
                                    </AlertDescription>
                                </Alert>

                                {/* Form Actions */}
                                <div className="flex gap-3 pt-4">
                                    <Button
                                        type="submit"
                                        disabled={processing}
                                        className="flex-1"
                                    >
                                        <Save className="w-4 h-4 mr-2" />
                                        {processing ? 'Updating Instance...' : 'Update Instance'}
                                    </Button>
                                    <Link href={route('instances.index')}>
                                        <Button type="button" variant="outline">
                                            Cancel
                                        </Button>
                                    </Link>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
