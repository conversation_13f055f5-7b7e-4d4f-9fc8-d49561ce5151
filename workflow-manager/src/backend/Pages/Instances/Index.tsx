import React, { useEffect, useState } from 'react';
import { Head } from '@inertiajs/react';
import AppLayout from '@/Layouts/AppLayout';
import { PageProps } from '@/types';
import { Link, useForm, router, usePage } from '@inertiajs/react';
import { generateBreadcrumbs } from '@/utils/breadcrumbs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Server, Key, Globe, Plus, Trash2, Edit } from 'lucide-react';
import toast from 'react-hot-toast';

interface N8nInstance {
    id: number;
    name: string;
    url: string;
    health_status: string;
    last_checked_at: string;
    permissions?: {
        can_view: boolean;
        can_edit: boolean;
        can_delete: boolean;
    };
}

export default function Index({ instances: initialInstances }: PageProps<{ instances: N8nInstance[] }>) {
    const [instances, setInstances] = useState(initialInstances);
    const [sheetOpen, setSheetOpen] = useState(false);
    const { auth } = usePage().props as any;



    const { data, setData, post, processing, errors, reset } = useForm({
        name: '',
        url: '',
        api_key: '',
    });

    const checkHealth = async (instanceId: number) => {
        const instance = instances.find(i => i.id === instanceId);
        const instanceName = instance?.name || `Instance #${instanceId}`;

        // Show loading toast
        const loadingToast = toast.loading(`Checking health for ${instanceName}...`);

        try {
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
            const response = await fetch(route('instances.check-health', instanceId), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken || ''
                },
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const updatedInstance = await response.json();
            setInstances(instances.map(instance =>
                instance.id === updatedInstance.id
                    ? { ...updatedInstance, permissions: instance.permissions } // Preserve permissions
                    : instance
            ));

            // Dismiss loading toast and show success/error based on health status
            toast.dismiss(loadingToast);

            if (updatedInstance.health_status === 'online') {
                toast.success(`${instanceName} is healthy and online!`, {
                    icon: '✅',
                });
            } else {
                toast.error(`${instanceName} is offline or unreachable`, {
                    icon: '❌',
                });
            }
        } catch (error) {
            // Dismiss loading toast and show error
            toast.dismiss(loadingToast);
            toast.error(`Failed to check health for ${instanceName}`, {
                icon: '⚠️',
            });
            console.error('Health check failed:', error);
        }
    };

    const handleCreateInstance = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('instances.store'), {
            onSuccess: () => {
                setSheetOpen(false);
                reset();
                // Refresh the page to show the new instance
                window.location.reload();
            }
        });
    };

    const handleDelete = (instanceId: number, instanceName: string) => {
        const confirmMessage = `⚠️ DELETE INSTANCE: "${instanceName}"

This action will permanently delete:
• The N8N instance connection
• All associated workflows
• All workflow execution history
• All related data

This action cannot be undone. Are you absolutely sure?`;

        if (confirm(confirmMessage)) {
            router.delete(route('instances.destroy', instanceId), {
                onSuccess: () => {
                    // Remove the instance from local state to update UI immediately
                    setInstances(instances.filter(instance => instance.id !== instanceId));
                },
                onError: (errors) => {
                    console.error('Failed to delete instance:', errors);
                    alert('Failed to delete instance. Please try again or contact support.');
                }
            });
        }
    };

    // Silent health check for initial load (no toasts)
    const checkHealthSilent = async (instanceId: number) => {
        try {
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
            const response = await fetch(route('instances.check-health', instanceId), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken || ''
                },
            });

            if (response.ok) {
                const updatedInstance = await response.json();
                setInstances(prev => prev.map(instance =>
                    instance.id === updatedInstance.id
                        ? { ...updatedInstance, permissions: instance.permissions } // Preserve permissions
                        : instance
                ));
            }
        } catch (error) {
            console.error('Silent health check failed:', error);
        }
    };

    useEffect(() => {
        instances.forEach(instance => checkHealthSilent(instance.id));
    }, []);

    const breadcrumbs = generateBreadcrumbs('/instances');

    return (
        <AppLayout title="Instances" breadcrumbs={breadcrumbs}>
            <Head title="Instances" />
            <div className="p-8">


                <div className="flex justify-between items-center">
                    <h2 className="text-3xl font-bold">Manage Instances</h2>
                    {/* Only show Add Instance button to admins and managers */}
                    {(auth?.user?.role === 'admin' || auth?.user?.role === 'manager') && (
                        <Sheet open={sheetOpen} onOpenChange={setSheetOpen}>
                            <SheetTrigger asChild>
                                <Button>
                                    <Plus className="w-4 h-4 mr-2" />
                                    Add Instance
                                </Button>
                            </SheetTrigger>
                        <SheetContent className="w-[400px] sm:w-[540px]">
                            <SheetHeader>
                                <SheetTitle className="flex items-center gap-2">
                                    <Server className="w-5 h-5" />
                                    Add New N8N Instance
                                </SheetTitle>
                                <SheetDescription>
                                    Connect a new N8N instance to your dashboard for monitoring and management.
                                </SheetDescription>
                            </SheetHeader>

                            <form onSubmit={handleCreateInstance} className="mt-6 space-y-6">
                                <div className="space-y-2">
                                    <Label htmlFor="name" className="flex items-center gap-2">
                                        <Server className="w-4 h-4" />
                                        Instance Name
                                    </Label>
                                    <Input
                                        id="name"
                                        type="text"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        placeholder="e.g., Production N8N, Development N8N"
                                        className="w-full"
                                    />
                                    {errors.name && <p className="text-sm text-destructive">{errors.name}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="url" className="flex items-center gap-2">
                                        <Globe className="w-4 h-4" />
                                        N8N Instance URL
                                    </Label>
                                    <Input
                                        id="url"
                                        type="url"
                                        value={data.url}
                                        onChange={(e) => setData('url', e.target.value)}
                                        placeholder="https://your-n8n-instance.com"
                                        className="w-full"
                                    />
                                    {errors.url && <p className="text-sm text-destructive">{errors.url}</p>}
                                    <p className="text-sm text-muted-foreground">
                                        Enter the full URL to your N8N instance (including https://)
                                    </p>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="api_key" className="flex items-center gap-2">
                                        <Key className="w-4 h-4" />
                                        API Key
                                    </Label>
                                    <Input
                                        id="api_key"
                                        type="password"
                                        value={data.api_key}
                                        onChange={(e) => setData('api_key', e.target.value)}
                                        placeholder="Enter your N8N API key"
                                        className="w-full"
                                    />
                                    {errors.api_key && <p className="text-sm text-destructive">{errors.api_key}</p>}
                                    <p className="text-sm text-muted-foreground">
                                        You can find your API key in N8N Settings → API Keys
                                    </p>
                                </div>

                                <div className="bg-muted/50 p-4 rounded-lg space-y-2">
                                    <h4 className="font-medium text-sm">How to get your N8N API Key:</h4>
                                    <div className="text-sm text-muted-foreground space-y-1">
                                        <p>1. Log into your N8N instance</p>
                                        <p>2. Go to Settings → API Keys</p>
                                        <p>3. Click "Create API Key"</p>
                                        <p>4. Copy the generated key and paste it above</p>
                                    </div>
                                </div>

                                <div className="flex gap-3 pt-4">
                                    <Button type="submit" disabled={processing} className="flex-1">
                                        {processing ? 'Creating Instance...' : 'Create Instance'}
                                    </Button>
                                    <Button type="button" variant="outline" onClick={() => setSheetOpen(false)}>
                                        Cancel
                                    </Button>
                                </div>
                            </form>
                        </SheetContent>
                    </Sheet>
                    )}
                </div>

                <div className="mt-8">
                    {instances.length === 0 ? (
                        <p className="text-muted-foreground">
                            You haven't added any N8N instances yet.
                        </p>
                    ) : (
                        <ul className="space-y-4">
                            {instances.map((instance) => (
                                <li key={instance.id} className="p-4 border rounded-lg flex items-center justify-between">
                                    <div className="flex items-start space-x-3">
                                        {/* Connection Status Indicator */}
                                        <div className="flex items-center space-x-2 mt-1">
                                            <div
                                                className={`w-3 h-3 rounded-full ${
                                                    instance.health_status === 'online' ? 'bg-emerald-500' : 'bg-gray-300'
                                                }`}
                                                title={instance.health_status === 'online' ? 'Instance is connected and healthy' : 'Instance is offline or unreachable'}
                                            />
                                        </div>
                                        <div>
                                            <p className="font-semibold">{instance.name}</p>
                                            <p className="text-sm text-muted-foreground">{instance.url}</p>
                                            <p className="text-xs text-muted-foreground">Last checked: {instance.last_checked_at ? new Date(instance.last_checked_at).toLocaleString() : 'Never'}</p>
                                        </div>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <Button variant="outline" size="sm" onClick={() => checkHealth(instance.id)}>Check Health</Button>
                                        <Link href={route('instances.analytics', instance.id)}>
                                            <Button variant="outline" size="sm">View Analytics</Button>
                                        </Link>
                                        {/* Edit button - backend already sets can_edit=true for admins */}
                                        {instance.permissions?.can_edit && (
                                            <Link href={route('instances.edit', instance.id)}>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 border-blue-200 hover:border-blue-300"
                                                    title="Edit Instance"
                                                >
                                                    <Edit className="w-4 h-4" />
                                                </Button>
                                            </Link>
                                        )}
                                        {/* Delete button - backend already sets can_delete=true for admins */}
                                        {instance.permissions?.can_delete && (
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => handleDelete(instance.id, instance.name)}
                                                className="text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200 hover:border-red-300"
                                                title="Delete Instance"
                                            >
                                                <Trash2 className="w-4 h-4" />
                                            </Button>
                                        )}
                                    </div>
                                </li>
                            ))}
                        </ul>
                    )}
                </div>
            </div>
        </AppLayout>
    );
}
