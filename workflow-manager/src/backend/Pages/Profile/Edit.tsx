import React, { useState } from 'react';
import { Head, useForm } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { User, Lock, Trash2, CheckCircle, Eye, EyeOff, Mail } from 'lucide-react';
import AppLayout from '@/Layouts/AppLayout';
import { User as UserType } from '@/types';

interface Props {
    user: UserType;
    status?: string;
}

interface ProfileForm {
    name: string;
    email: string;
}

interface PasswordForm {
    current_password: string;
    password: string;
    password_confirmation: string;
}

interface DeleteForm {
    password: string;
}

export default function Edit({ user, status }: Props) {
    const [showCurrentPassword, setShowCurrentPassword] = useState(false);
    const [showPassword, setShowPassword] = useState(false);
    const [showPasswordConfirmation, setShowPasswordConfirmation] = useState(false);
    const [showDeletePassword, setShowDeletePassword] = useState(false);

    const profileForm = useForm<ProfileForm>({
        name: user.name,
        email: user.email,
    });

    const passwordForm = useForm<PasswordForm>({
        current_password: '',
        password: '',
        password_confirmation: '',
    });

    const deleteForm = useForm<DeleteForm>({
        password: '',
    });

    const handleProfileSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        profileForm.patch('/profile');
    };

    const handlePasswordSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        passwordForm.put('/password', {
            onSuccess: () => {
                passwordForm.reset();
            },
            onError: () => {
                passwordForm.reset('current_password', 'password', 'password_confirmation');
            }
        });
    };

    const handleDeleteSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
            deleteForm.delete('/profile');
        }
    };

    return (
        <AppLayout>
            <Head title="Profile" />

            <div className="py-12">
                <div className="max-w-4xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-6 text-gray-900 dark:text-gray-100">
                            <div className="mb-6">
                                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Profile Settings</h1>
                                <p className="text-gray-600 dark:text-gray-400">Manage your account settings and preferences.</p>
                            </div>

                            {status && (
                                <Alert className="mb-6 border-green-200 bg-green-50 dark:bg-green-900/20">
                                    <CheckCircle className="h-4 w-4 text-green-600" />
                                    <AlertDescription className="text-green-800 dark:text-green-200">
                                        {status === 'profile-updated' && 'Your profile has been updated successfully.'}
                                        {status === 'password-updated' && 'Your password has been updated successfully.'}
                                    </AlertDescription>
                                </Alert>
                            )}

                            <Tabs defaultValue="profile" className="space-y-6">
                                <TabsList className="grid w-full grid-cols-3">
                                    <TabsTrigger value="profile" className="flex items-center gap-2">
                                        <User className="w-4 h-4" />
                                        Profile
                                    </TabsTrigger>
                                    <TabsTrigger value="password" className="flex items-center gap-2">
                                        <Lock className="w-4 h-4" />
                                        Password
                                    </TabsTrigger>
                                    <TabsTrigger value="danger" className="flex items-center gap-2">
                                        <Trash2 className="w-4 h-4" />
                                        Danger Zone
                                    </TabsTrigger>
                                </TabsList>

                                <TabsContent value="profile">
                                    <Card>
                                        <CardHeader>
                                            <CardTitle>Profile Information</CardTitle>
                                            <CardDescription>
                                                Update your account's profile information and email address.
                                            </CardDescription>
                                        </CardHeader>
                                        <CardContent>
                                            <form onSubmit={handleProfileSubmit} className="space-y-4">
                                                <div className="space-y-2">
                                                    <Label htmlFor="name">Name</Label>
                                                    <div className="relative">
                                                        <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                                                        <Input
                                                            id="name"
                                                            type="text"
                                                            value={profileForm.data.name}
                                                            onChange={(e) => profileForm.setData('name', e.target.value)}
                                                            className="pl-10"
                                                            required
                                                        />
                                                    </div>
                                                    {profileForm.errors.name && (
                                                        <p className="text-sm text-red-600 dark:text-red-400">{profileForm.errors.name}</p>
                                                    )}
                                                </div>

                                                <div className="space-y-2">
                                                    <Label htmlFor="email">Email</Label>
                                                    <div className="relative">
                                                        <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                                                        <Input
                                                            id="email"
                                                            type="email"
                                                            value={profileForm.data.email}
                                                            onChange={(e) => profileForm.setData('email', e.target.value)}
                                                            className="pl-10"
                                                            required
                                                        />
                                                    </div>
                                                    {profileForm.errors.email && (
                                                        <p className="text-sm text-red-600 dark:text-red-400">{profileForm.errors.email}</p>
                                                    )}
                                                    {user.email_verified_at === null && (
                                                        <p className="text-sm text-yellow-600 dark:text-yellow-400">
                                                            Your email address is unverified.
                                                        </p>
                                                    )}
                                                </div>

                                                <Button
                                                    type="submit"
                                                    disabled={profileForm.processing}
                                                    className="bg-blue-600 hover:bg-blue-700"
                                                >
                                                    {profileForm.processing ? 'Saving...' : 'Save Changes'}
                                                </Button>
                                            </form>
                                        </CardContent>
                                    </Card>
                                </TabsContent>

                                <TabsContent value="password">
                                    <Card>
                                        <CardHeader>
                                            <CardTitle>Update Password</CardTitle>
                                            <CardDescription>
                                                Ensure your account is using a long, random password to stay secure.
                                            </CardDescription>
                                        </CardHeader>
                                        <CardContent>
                                            <form onSubmit={handlePasswordSubmit} className="space-y-4">
                                                <div className="space-y-2">
                                                    <Label htmlFor="current_password">Current Password</Label>
                                                    <div className="relative">
                                                        <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                                                        <Input
                                                            id="current_password"
                                                            type={showCurrentPassword ? 'text' : 'password'}
                                                            value={passwordForm.data.current_password}
                                                            onChange={(e) => passwordForm.setData('current_password', e.target.value)}
                                                            className="pl-10 pr-10"
                                                            required
                                                        />
                                                        <button
                                                            type="button"
                                                            onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                                                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                                                        >
                                                            {showCurrentPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                                                        </button>
                                                    </div>
                                                    {passwordForm.errors.current_password && (
                                                        <p className="text-sm text-red-600 dark:text-red-400">{passwordForm.errors.current_password}</p>
                                                    )}
                                                </div>

                                                <div className="space-y-2">
                                                    <Label htmlFor="password">New Password</Label>
                                                    <div className="relative">
                                                        <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                                                        <Input
                                                            id="password"
                                                            type={showPassword ? 'text' : 'password'}
                                                            value={passwordForm.data.password}
                                                            onChange={(e) => passwordForm.setData('password', e.target.value)}
                                                            className="pl-10 pr-10"
                                                            required
                                                        />
                                                        <button
                                                            type="button"
                                                            onClick={() => setShowPassword(!showPassword)}
                                                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                                                        >
                                                            {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                                                        </button>
                                                    </div>
                                                    {passwordForm.errors.password && (
                                                        <p className="text-sm text-red-600 dark:text-red-400">{passwordForm.errors.password}</p>
                                                    )}
                                                </div>

                                                <div className="space-y-2">
                                                    <Label htmlFor="password_confirmation">Confirm Password</Label>
                                                    <div className="relative">
                                                        <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                                                        <Input
                                                            id="password_confirmation"
                                                            type={showPasswordConfirmation ? 'text' : 'password'}
                                                            value={passwordForm.data.password_confirmation}
                                                            onChange={(e) => passwordForm.setData('password_confirmation', e.target.value)}
                                                            className="pl-10 pr-10"
                                                            required
                                                        />
                                                        <button
                                                            type="button"
                                                            onClick={() => setShowPasswordConfirmation(!showPasswordConfirmation)}
                                                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                                                        >
                                                            {showPasswordConfirmation ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                                                        </button>
                                                    </div>
                                                    {passwordForm.errors.password_confirmation && (
                                                        <p className="text-sm text-red-600 dark:text-red-400">{passwordForm.errors.password_confirmation}</p>
                                                    )}
                                                </div>

                                                <Button
                                                    type="submit"
                                                    disabled={passwordForm.processing}
                                                    className="bg-blue-600 hover:bg-blue-700"
                                                >
                                                    {passwordForm.processing ? 'Updating...' : 'Update Password'}
                                                </Button>
                                            </form>
                                        </CardContent>
                                    </Card>
                                </TabsContent>

                                <TabsContent value="danger">
                                    <Card className="border-red-200 dark:border-red-800">
                                        <CardHeader>
                                            <CardTitle className="text-red-600 dark:text-red-400">Delete Account</CardTitle>
                                            <CardDescription>
                                                Once your account is deleted, all of its resources and data will be permanently deleted. Before deleting your account, please download any data or information that you wish to retain.
                                            </CardDescription>
                                        </CardHeader>
                                        <CardContent>
                                            <form onSubmit={handleDeleteSubmit} className="space-y-4">
                                                <div className="space-y-2">
                                                    <Label htmlFor="delete_password">Password</Label>
                                                    <div className="relative">
                                                        <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                                                        <Input
                                                            id="delete_password"
                                                            type={showDeletePassword ? 'text' : 'password'}
                                                            value={deleteForm.data.password}
                                                            onChange={(e) => deleteForm.setData('password', e.target.value)}
                                                            className="pl-10 pr-10"
                                                            placeholder="Enter your password to confirm"
                                                            required
                                                        />
                                                        <button
                                                            type="button"
                                                            onClick={() => setShowDeletePassword(!showDeletePassword)}
                                                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                                                        >
                                                            {showDeletePassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                                                        </button>
                                                    </div>
                                                    {deleteForm.errors.password && (
                                                        <p className="text-sm text-red-600 dark:text-red-400">{deleteForm.errors.password}</p>
                                                    )}
                                                </div>

                                                <Button
                                                    type="submit"
                                                    variant="destructive"
                                                    disabled={deleteForm.processing}
                                                    className="bg-red-600 hover:bg-red-700"
                                                >
                                                    {deleteForm.processing ? 'Deleting...' : 'Delete Account'}
                                                </Button>
                                            </form>
                                        </CardContent>
                                    </Card>
                                </TabsContent>
                            </Tabs>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
