import React from 'react';
import AppLayout from '@/Layouts/AppLayout';
import { useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { PageProps } from '@/types';

export default function Create() {
    const { data, setData, post, processing, errors } = useForm({
        name: '',
        description: '',
        tags: [] as string[],
        schema: {
            nodes: [],
            connections: {}
        },
    });

    function submit(e: React.FormEvent) {
        e.preventDefault();
        post(route('templates.store'));
    }

    return (
        <AppLayout>
            <div className="p-8">
                <h2 className="text-3xl font-bold">Create New Template</h2>
                <p className="mt-2 text-muted-foreground">
                    Create a new workflow template for reuse.
                </p>

                <form onSubmit={submit} className="mt-8 space-y-6">
                    <div>
                        <label htmlFor="name" className="block text-sm font-medium">
                            Template Name
                        </label>
                        <Input
                            id="name"
                            type="text"
                            value={data.name}
                            onChange={(e) => setData('name', e.target.value)}
                            className="mt-1"
                            required
                        />
                        {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
                    </div>

                    <div>
                        <label htmlFor="description" className="block text-sm font-medium">
                            Description
                        </label>
                        <textarea
                            id="description"
                            value={data.description}
                            onChange={(e) => setData('description', e.target.value)}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                            rows={3}
                            required
                        />
                        {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
                    </div>

                    <div>
                        <label htmlFor="tags" className="block text-sm font-medium">
                            Tags (comma-separated)
                        </label>
                        <Input
                            id="tags"
                            type="text"
                            value={data.tags.join(', ')}
                            onChange={(e) => setData('tags', e.target.value.split(',').map(tag => tag.trim()))}
                            className="mt-1"
                            placeholder="automation, webhook, email"
                        />
                        {errors.tags && <p className="mt-1 text-sm text-red-600">{errors.tags}</p>}
                    </div>

                    <div>
                        <label htmlFor="schema" className="block text-sm font-medium">
                            Workflow Schema (JSON)
                        </label>
                        <textarea
                            id="schema"
                            value={JSON.stringify(data.schema, null, 2)}
                            onChange={(e) => {
                                try {
                                    setData('schema', JSON.parse(e.target.value));
                                } catch {
                                    // Invalid JSON, keep the string value for now
                                }
                            }}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm font-mono text-sm"
                            rows={10}
                            required
                        />
                        {errors.schema && <p className="mt-1 text-sm text-red-600">{errors.schema}</p>}
                    </div>

                    <div className="flex justify-end space-x-4">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => window.history.back()}
                        >
                            Cancel
                        </Button>
                        <Button type="submit" disabled={processing}>
                            {processing ? 'Creating...' : 'Create Template'}
                        </Button>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
}
