import React, { useState, useEffect } from 'react';
import { Head } from '@inertiajs/react';
import AppLayout from '@/Layouts/AppLayout';
import { usePage, Link, router } from '@inertiajs/react';
import { generateBreadcrumbs } from '@/utils/breadcrumbs';
import { PageProps, Template } from '@/types';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import UseTemplateButton from '@/components/UseTemplateButton';
import {
    FileText,
    Search,
    Filter,
    Star,
    Download,
    Eye,
    Plus,
    Sparkles,
    Bot,
    TrendingUp,
    Clock,
    Users,
    Zap,
    Settings,
    ArrowRight,
    Grid3X3,
    List,
    SortAsc,
    SortDesc
} from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface TemplateStats {
    total_templates: number;
    ai_templates: number;
    regular_templates: number;
    most_popular: Template | null;
}

interface EnhancedTemplate extends Template {
    usage_count?: number;
    avg_rating?: number;
    category?: string;
    type: 'regular' | 'ai';
    preview_nodes?: Array<{ name: string; type: string }>;
}

export default function Index() {
    const { templates: initialTemplates } = usePage<PageProps & { templates: Template[] }>().props;
    const [templates, setTemplates] = useState<EnhancedTemplate[]>([]);
    const [filteredTemplates, setFilteredTemplates] = useState<EnhancedTemplate[]>([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedCategory, setSelectedCategory] = useState('all');
    const [selectedType, setSelectedType] = useState('all');
    const [sortBy, setSortBy] = useState('name');
    const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
    const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
    const [stats, setStats] = useState<TemplateStats>({
        total_templates: 0,
        ai_templates: 0,
        regular_templates: 0,
        most_popular: null
    });


    // Enhanced templates with additional metadata
    useEffect(() => {
        const enhancedTemplates: EnhancedTemplate[] = initialTemplates.map(template => ({
            ...template,
            type: 'regular' as const,
            usage_count: 0, // Real usage data will come from backend
            avg_rating: 0, // Real rating data will come from backend
            category: template.tags?.[0] || 'General',
            preview_nodes: template.schema?.nodes?.slice(0, 3) || []
        }));

        setTemplates(enhancedTemplates);
        setFilteredTemplates(enhancedTemplates);

        // Calculate stats
        setStats({
            total_templates: enhancedTemplates.length,
            ai_templates: 0, // Will be populated when AI templates are loaded
            regular_templates: enhancedTemplates.length,
            most_popular: null // Will be determined by real usage data
        });
    }, [initialTemplates]);
    // Filter and search logic
    useEffect(() => {
        let filtered = templates;

        // Search filter
        if (searchTerm) {
            filtered = filtered.filter(template =>
                template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                template.tags?.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
            );
        }

        // Category filter
        if (selectedCategory !== 'all') {
            filtered = filtered.filter(template => template.category === selectedCategory);
        }

        // Type filter
        if (selectedType !== 'all') {
            filtered = filtered.filter(template => template.type === selectedType);
        }

        // Sort
        filtered.sort((a, b) => {
            let aValue, bValue;
            switch (sortBy) {
                case 'name':
                    aValue = a.name.toLowerCase();
                    bValue = b.name.toLowerCase();
                    break;
                case 'usage':
                    aValue = a.usage_count || 0;
                    bValue = b.usage_count || 0;
                    break;
                case 'rating':
                    aValue = a.avg_rating || 0;
                    bValue = b.avg_rating || 0;
                    break;
                case 'created':
                    aValue = new Date(a.created_at).getTime();
                    bValue = new Date(b.created_at).getTime();
                    break;
                default:
                    aValue = a.name.toLowerCase();
                    bValue = b.name.toLowerCase();
            }

            if (sortOrder === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });

        setFilteredTemplates(filtered);
    }, [templates, searchTerm, selectedCategory, selectedType, sortBy, sortOrder]);

    const categories = Array.from(new Set(templates.map(t => t.category).filter(Boolean)));

    const getTypeIcon = (type: string) => {
        return type === 'ai' ? <Bot className="w-4 h-4" /> : <FileText className="w-4 h-4" />;
    };

    const getTypeColor = (type: string) => {
        return type === 'ai'
            ? 'bg-purple-100 text-purple-800 border-purple-200'
            : 'bg-blue-100 text-blue-800 border-blue-200';
    };

    const renderStars = (rating: number) => {
        return Array.from({ length: 5 }, (_, i) => (
            <Star
                key={i}
                className={`w-3 h-3 ${i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
            />
        ));
    };



    const breadcrumbs = generateBreadcrumbs('/templates');

    return (
        <AppLayout title="Templates" breadcrumbs={breadcrumbs}>
            <Head title="Templates" />
            <div className="p-8 max-w-7xl mx-auto">
                {/* Header */}
                <div className="flex items-center justify-between mb-8">
                    <div className="flex items-center gap-4">
                        <div className="p-3 rounded-2xl bg-gradient-to-br from-blue-500 to-teal-600 shadow-lg">
                            <FileText className="w-8 h-8 text-white" />
                        </div>
                        <div>
                            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-teal-600 bg-clip-text text-transparent">
                                Template Marketplace
                            </h1>
                            <p className="text-slate-600 mt-1">
                                Discover, use, and share powerful N8N workflow templates
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center gap-3">
                        <Link href="/templates/create">
                            <Button className="bg-gradient-to-r from-blue-600 to-teal-600 hover:from-blue-700 hover:to-teal-700">
                                <Plus className="w-4 h-4 mr-2" />
                                Create Template
                            </Button>
                        </Link>
                    </div>
                </div>

                {/* Filters and Search */}
                <Card className="mb-6">
                    <CardContent className="p-6">
                        <div className="flex flex-col lg:flex-row gap-4">
                            {/* Search */}
                            <div className="flex-1">
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
                                    <Input
                                        placeholder="Search templates..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="pl-10"
                                    />
                                </div>
                            </div>

                            {/* Filters */}
                            <div className="flex gap-4">
                                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                                    <SelectTrigger className="w-48">
                                        <Filter className="w-4 h-4 mr-2" />
                                        <SelectValue placeholder="Category" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Categories</SelectItem>
                                        {categories.map(category => (
                                            <SelectItem key={category} value={category || 'general'}>{category}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>

                                <Select value={selectedType} onValueChange={setSelectedType}>
                                    <SelectTrigger className="w-40">
                                        <SelectValue placeholder="Type" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Types</SelectItem>
                                        <SelectItem value="regular">Regular</SelectItem>
                                        <SelectItem value="ai">AI Templates</SelectItem>
                                    </SelectContent>
                                </Select>

                                <Select value={sortBy} onValueChange={setSortBy}>
                                    <SelectTrigger className="w-40">
                                        <SelectValue placeholder="Sort by" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="name">Name</SelectItem>
                                        <SelectItem value="usage">Usage</SelectItem>
                                        <SelectItem value="rating">Rating</SelectItem>
                                        <SelectItem value="created">Created</SelectItem>
                                    </SelectContent>
                                </Select>

                                <Button
                                    variant="outline"
                                    size="icon"
                                    onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                                >
                                    {sortOrder === 'asc' ? <SortAsc className="w-4 h-4" /> : <SortDesc className="w-4 h-4" />}
                                </Button>

                                <div className="flex border rounded-lg">
                                    <Button
                                        variant={viewMode === 'grid' ? 'default' : 'ghost'}
                                        size="icon"
                                        onClick={() => setViewMode('grid')}
                                        className="rounded-r-none"
                                    >
                                        <Grid3X3 className="w-4 h-4" />
                                    </Button>
                                    <Button
                                        variant={viewMode === 'list' ? 'default' : 'ghost'}
                                        size="icon"
                                        onClick={() => setViewMode('list')}
                                        className="rounded-l-none"
                                    >
                                        <List className="w-4 h-4" />
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
                {/* Templates Grid/List */}
                {filteredTemplates.length > 0 ? (
                    <div className={viewMode === 'grid'
                        ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                        : "space-y-4"
                    }>
                        {filteredTemplates.map((template) => (
                            <Card key={template.id} className="hover:shadow-lg transition-shadow group">
                                <CardHeader className="pb-3">
                                    <div className="flex items-start justify-between">
                                        <div className="flex-1">
                                            <CardTitle className="text-lg group-hover:text-blue-600 transition-colors">
                                                {template.name}
                                            </CardTitle>
                                            <CardDescription className="mt-1 line-clamp-2">
                                                {template.description}
                                            </CardDescription>
                                        </div>
                                        <Badge className={`${getTypeColor(template.type)} border ml-2`}>
                                            {getTypeIcon(template.type)}
                                            <span className="ml-1 capitalize">{template.type}</span>
                                        </Badge>
                                    </div>
                                </CardHeader>

                                <CardContent className="py-3">
                                    {/* Tags */}
                                    <div className="flex flex-wrap gap-1 mb-3">
                                        {template.tags?.slice(0, 3).map((tag: string) => (
                                            <Badge key={tag} variant="outline" className="text-xs">
                                                {tag}
                                            </Badge>
                                        ))}
                                        {template.tags && template.tags.length > 3 && (
                                            <Badge variant="outline" className="text-xs">
                                                +{template.tags.length - 3}
                                            </Badge>
                                        )}
                                    </div>

                                    {/* Preview Nodes */}
                                    {template.preview_nodes && template.preview_nodes.length > 0 && (
                                        <div className="mb-3">
                                            <p className="text-xs text-slate-600 mb-1">Preview nodes:</p>
                                            <div className="flex gap-1">
                                                {template.preview_nodes.map((node, index) => (
                                                    <Badge key={index} variant="secondary" className="text-xs">
                                                        {node.name}
                                                    </Badge>
                                                ))}
                                            </div>
                                        </div>
                                    )}

                                    {/* Stats */}
                                    <div className="flex items-center justify-between text-sm text-slate-600">
                                        <div className="flex items-center gap-3">
                                            {(template.usage_count || 0) > 0 && (
                                                <div className="flex items-center gap-1">
                                                    <Download className="w-3 h-3" />
                                                    <span>{template.usage_count}</span>
                                                </div>
                                            )}
                                            {(template.avg_rating || 0) > 0 && (
                                                <div className="flex items-center gap-1">
                                                    {renderStars(template.avg_rating || 0)}
                                                    <span className="ml-1">{template.avg_rating}</span>
                                                </div>
                                            )}
                                            {(template.usage_count || 0) === 0 && (template.avg_rating || 0) === 0 && (
                                                <span className="text-slate-400 text-xs">New template</span>
                                            )}
                                        </div>
                                        <div className="flex items-center gap-1">
                                            <Clock className="w-3 h-3" />
                                            <span>{new Date(template.created_at).toLocaleDateString()}</span>
                                        </div>
                                    </div>
                                </CardContent>

                                <CardFooter className="pt-3">
                                    <div className="flex items-center justify-between w-full">
                                        <Link href={route('templates.show', template.id)}>
                                            <Button variant="outline" size="sm">
                                                <Eye className="w-4 h-4 mr-2" />
                                                View Details
                                            </Button>
                                        </Link>
                                        <UseTemplateButton template={template} />
                                    </div>
                                </CardFooter>
                            </Card>
                        ))}
                    </div>
                ) : (
                    <Card>
                        <CardContent className="p-12 text-center">
                            <FileText className="w-16 h-16 text-slate-300 mx-auto mb-4" />
                            <h3 className="text-lg font-semibold text-slate-900 mb-2">No templates found</h3>
                            <p className="text-slate-600 mb-6">
                                {searchTerm || selectedCategory !== 'all' || selectedType !== 'all'
                                    ? 'Try adjusting your search or filters.'
                                    : 'Start by creating your first template.'}
                            </p>
                            <Link href="/templates/create">
                                <Button className="bg-gradient-to-r from-blue-600 to-teal-600 hover:from-blue-700 hover:to-teal-700">
                                    <Plus className="w-4 h-4 mr-2" />
                                    Create First Template
                                    <ArrowRight className="w-4 h-4 ml-2" />
                                </Button>
                            </Link>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
