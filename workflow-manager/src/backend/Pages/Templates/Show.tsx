import React, { useCallback } from 'react';
import { Head } from '@inertiajs/react';
import AppLayout from '@/Layouts/AppLayout';
import { PageProps, Template } from '@/types';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Link } from '@inertiajs/react';
import UseTemplateButton from '@/components/UseTemplateButton';
import ReactFlow, {
    Controls,
    Background,
    useNodesState,
    useEdgesState,
    addEdge,
    Connection,
    Edge,
    Node,
    BackgroundVariant,
    MiniMap,
    Position
} from 'reactflow';
import { generateBreadcrumbs } from '@/utils/breadcrumbs';

import 'reactflow/dist/style.css';

export default function Show({ template }: PageProps<{ template: Template }>) {
    // Transform template schema to ReactFlow format
    const initialNodes: Node[] = Array.isArray(template.schema?.nodes)
        ? template.schema.nodes.map((node: any) => ({
            id: node.id || node.name || `node-${Math.random()}`,
            type: 'default',
            position: {
                x: node.position?.[0] || node.position?.x || Math.random() * 400,
                y: node.position?.[1] || node.position?.y || Math.random() * 400
            },
            data: {
                label: node.name || node.type || 'Unnamed Node',
                nodeType: node.type,
                parameters: node.parameters
            },
            style: {
                background: '#fff',
                border: '2px solid #0ea5e9',
                borderRadius: '8px',
                fontSize: '12px',
                fontWeight: '500',
                width: 150,
                height: 40
            },
            sourcePosition: Position.Right,
            targetPosition: Position.Left
        }))
        : [];

    // Create comprehensive mapping for node name/ID resolution
    const nodeNameToId = new Map<string, string>();
    const nodeIdToName = new Map<string, string>();

    // Build mapping from template schema nodes
    if (Array.isArray(template.schema?.nodes)) {
        template.schema.nodes.forEach((schemaNode: any) => {
            const nodeId = schemaNode.id;
            const nodeName = schemaNode.name;

            if (nodeId && nodeName) {
                // Map both directions: name->id and id->name
                nodeNameToId.set(nodeName, nodeId);
                nodeNameToId.set(nodeId, nodeId); // Also map ID to itself
                nodeIdToName.set(nodeId, nodeName);

                console.log(`📝 Mapped: "${nodeName}" → "${nodeId}"`);
            }
        });
    }

    // Also map from processed ReactFlow nodes (as backup)
    initialNodes.forEach(node => {
        nodeNameToId.set(node.id, node.id);
        nodeIdToName.set(node.id, node.data.label);

        if (node.data.label && node.data.label !== node.id) {
            nodeNameToId.set(node.data.label, node.id);
        }
    });

    // Handle template connections format
    let initialEdges: Edge[] = [];

    if (template.schema?.connections && typeof template.schema.connections === 'object') {
        Object.entries(template.schema.connections).forEach(([sourceNodeName, connections]: [string, any]) => {
            if (connections && connections.main) {
                // Handle both array formats: [[{node: "target"}]] and [{node: "target"}]
                const mainConnections = Array.isArray(connections.main[0]) ? connections.main : [connections.main];

                mainConnections.forEach((connectionGroup: any[], outputIndex: number) => {
                    if (Array.isArray(connectionGroup)) {
                        connectionGroup.forEach((connection: any, connectionIndex: number) => {
                            if (connection && connection.node) {
                                // Resolve source node ID from name
                                const sourceId = nodeNameToId.get(sourceNodeName);
                                // Resolve target node ID from name
                                const targetId = nodeNameToId.get(connection.node);

                                // Check if nodes exist
                                const sourceExists = initialNodes.some(n => n.id === sourceId);
                                const targetExists = initialNodes.some(n => n.id === targetId);

                                // Only create edge if both nodes exist
                                if (sourceId && targetId && sourceExists && targetExists) {
                                    const edge = {
                                        id: `e${sourceId}-${targetId}-${outputIndex}-${connectionIndex}`,
                                        source: sourceId,
                                        target: targetId,
                                        type: 'smoothstep',
                                        style: { stroke: '#0ea5e9', strokeWidth: 2 },
                                        animated: false // Templates are static
                                    };
                                    initialEdges.push(edge);
                                }
                            }
                        });
                    }
                });
            }
        });
    } else if (Array.isArray(template.schema?.connections)) {
        initialEdges = template.schema.connections.map((connection: any, index: number) => {
            const sourceId = nodeNameToId.get(connection.fromNode || connection.source) || connection.fromNode || connection.source;
            const targetId = nodeNameToId.get(connection.toNode || connection.target) || connection.toNode || connection.target;

            return {
                id: `e${sourceId}-${targetId}-${index}`,
                source: sourceId,
                target: targetId,
                sourceHandle: connection.fromHandle || connection.sourceHandle,
                targetHandle: connection.toHandle || connection.targetHandle,
                type: 'smoothstep',
                style: { stroke: '#0ea5e9', strokeWidth: 2 },
                animated: false
            };
        }).filter(edge =>
            nodeNameToId.has(edge.source) && nodeNameToId.has(edge.target)
        );
    }

    const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
    const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

    const onConnect = useCallback(
        (params: Connection) => setEdges((eds) => addEdge(params, eds)),
        [setEdges]
    );

    const breadcrumbs = generateBreadcrumbs(`/templates/${template.id}`, {
        templateName: template.name
    });

    return (
        <AppLayout title={`Template: ${template.name}`} breadcrumbs={breadcrumbs}>
            <Head title={`Template: ${template.name}`} />
            <div className="p-8">
                <div className="flex justify-between items-start mb-8">
                    <div>
                        <h2 className="text-3xl font-bold">{template.name}</h2>
                        <p className="mt-2 text-muted-foreground">
                            {template.description}
                        </p>
                    </div>
                    <div className="flex space-x-2">
                        <Link href={route('templates.edit', template.id)}>
                            <Button variant="outline">Edit</Button>
                        </Link>
                        <UseTemplateButton template={template} />
                    </div>
                </div>

                {/* Template Statistics */}
                <div className="mb-8 grid grid-cols-4 gap-4 text-sm">
                    <div className="bg-slate-50 dark:bg-slate-800 p-3 rounded-lg">
                        <span className="font-medium">Nodes:</span> {nodes.length}
                    </div>
                    <div className="bg-slate-50 dark:bg-slate-800 p-3 rounded-lg">
                        <span className="font-medium">Connections:</span> {edges.length}
                    </div>
                    <div className="bg-slate-50 dark:bg-slate-800 p-3 rounded-lg">
                        <span className="font-medium">Tags:</span> {template.tags?.length || 0}
                    </div>
                    <div className="bg-slate-50 dark:bg-slate-800 p-3 rounded-lg">
                        <span className="font-medium">Type:</span>
                        <span className="ml-1 text-blue-600">Template</span>
                    </div>
                </div>

                {/* ReactFlow Visualization */}
                <div className="mb-8">
                    <h3 className="text-lg font-semibold mb-4">Template Preview</h3>
                    <div
                        style={{ width: '100%', height: '600px' }}
                        className="border rounded-lg bg-slate-50 dark:bg-slate-900"
                    >
                        {nodes.length > 0 ? (
                            <ReactFlow
                                nodes={nodes}
                                edges={edges}
                                onNodesChange={onNodesChange}
                                onEdgesChange={onEdgesChange}
                                onConnect={onConnect}
                                fitView
                                fitViewOptions={{ padding: 0.2 }}
                                attributionPosition="bottom-left"
                                proOptions={{ hideAttribution: true }}
                                defaultViewport={{ x: 0, y: 0, zoom: 1 }}
                                minZoom={0.1}
                                maxZoom={2}
                            >
                                <Controls
                                    position="top-left"
                                    showInteractive={false}
                                />
                                <Background
                                    variant={BackgroundVariant.Dots}
                                    gap={20}
                                    size={1}
                                    color="#94a3b8"
                                />
                                <MiniMap
                                    nodeColor="#0ea5e9"
                                    nodeStrokeWidth={3}
                                    position="bottom-right"
                                    style={{
                                        backgroundColor: '#f8fafc',
                                        border: '1px solid #e2e8f0'
                                    }}
                                />
                            </ReactFlow>
                        ) : (
                            <div className="flex items-center justify-center h-full text-slate-500">
                                <div className="text-center">
                                    <div className="w-16 h-16 mx-auto mb-4 bg-slate-200 dark:bg-slate-700 rounded-full flex items-center justify-center">
                                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                        </svg>
                                    </div>
                                    <p className="text-lg font-medium">No template data available</p>
                                    <p className="text-sm mt-1">This template doesn't have any nodes or connections to display.</p>
                                </div>
                            </div>
                        )}
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <div className="lg:col-span-2">
                        <Card>
                            <CardHeader>
                                <CardTitle>Workflow Schema</CardTitle>
                                <CardDescription>
                                    The structure and configuration of this template
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <pre className="bg-gray-50 dark:bg-gray-800 p-4 rounded-md overflow-auto text-sm">
                                    {JSON.stringify(template.schema, null, 2)}
                                </pre>
                            </CardContent>
                        </Card>
                    </div>

                    <div>
                        <Card>
                            <CardHeader>
                                <CardTitle>Template Details</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <h4 className="font-medium">Tags</h4>
                                    <div className="flex flex-wrap gap-2 mt-2">
                                        {template.tags.map((tag, index) => (
                                            <Badge key={index} variant="secondary">
                                                {tag}
                                            </Badge>
                                        ))}
                                    </div>
                                </div>

                                <div>
                                    <h4 className="font-medium">Nodes</h4>
                                    <p className="text-sm text-muted-foreground">
                                        {template.schema.nodes.length} nodes
                                    </p>
                                </div>

                                <div>
                                    <h4 className="font-medium">Created</h4>
                                    <p className="text-sm text-muted-foreground">
                                        {new Date(template.created_at).toLocaleDateString()}
                                    </p>
                                </div>

                                <div>
                                    <h4 className="font-medium">Last Updated</h4>
                                    <p className="text-sm text-muted-foreground">
                                        {new Date(template.updated_at).toLocaleDateString()}
                                    </p>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="mt-6">
                            <CardHeader>
                                <CardTitle>Node List</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-2">
                                    {template.schema.nodes.map((node, index) => (
                                        <div key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                                            <span className="font-medium">{node.name}</span>
                                            <Badge variant="outline">{node.type}</Badge>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>

                {/* Debug Information */}
                <details className="mt-8">
                    <summary className="cursor-pointer text-sm font-medium text-slate-600">
                        Debug Information
                    </summary>
                    <div className="mt-2 p-4 bg-slate-100 dark:bg-slate-800 rounded-lg text-xs">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                            <div>
                                <strong>Template Info:</strong>
                                <pre className="mt-1 overflow-x-auto">{JSON.stringify({
                                    id: template.id,
                                    name: template.name,
                                    hasSchema: !!template.schema,
                                    hasNodes: !!template.schema?.nodes,
                                    hasConnections: !!template.schema?.connections,
                                    nodeCount: template.schema?.nodes?.length || 0,
                                    connectionKeys: template.schema?.connections ? Object.keys(template.schema.connections) : []
                                }, null, 2)}</pre>
                            </div>
                            <div>
                                <strong>Processed Nodes ({nodes.length}):</strong>
                                <pre className="mt-1 overflow-x-auto">{JSON.stringify(nodes.map(n => ({
                                    id: n.id,
                                    label: n.data.label,
                                    position: n.position
                                })), null, 2)}</pre>
                            </div>
                            <div>
                                <strong>Processed Edges ({edges.length}):</strong>
                                <pre className="mt-1 overflow-x-auto">{JSON.stringify(edges.map(e => ({
                                    id: e.id,
                                    source: e.source,
                                    target: e.target,
                                    sourceHandle: e.sourceHandle,
                                    targetHandle: e.targetHandle
                                })), null, 2)}</pre>
                            </div>
                            <div>
                                <strong>Node Name Mapping:</strong>
                                <pre className="mt-1 overflow-x-auto">{JSON.stringify(
                                    Array.from(nodeNameToId.entries()).reduce((obj, [key, value]) => {
                                        obj[key] = value;
                                        return obj;
                                    }, {} as Record<string, string>), null, 2
                                )}</pre>
                            </div>
                            <div>
                                <strong>Raw Template Nodes:</strong>
                                <pre className="mt-1 overflow-x-auto">{JSON.stringify(template.schema?.nodes, null, 2)}</pre>
                            </div>
                            <div className="lg:col-span-1">
                                <strong>Raw Template Connections:</strong>
                                <pre className="mt-1 overflow-x-auto">{JSON.stringify(template.schema?.connections, null, 2)}</pre>
                            </div>
                        </div>
                    </div>
                </details>
            </div>
        </AppLayout>
    );
}
