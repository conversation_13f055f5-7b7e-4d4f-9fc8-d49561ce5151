import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AppLayout from '@/Layouts/AppLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
    User,
    Mail,
    Shield,
    Calendar,
    Server,
    ArrowLeft,
    Edit,
    Settings
} from 'lucide-react';

interface UserData {
    id: number;
    name: string;
    email: string;
    role: string;
    created_at: string;
    n8n_instances: Array<{
        id: number;
        name: string;
        url: string;
        pivot: {
            can_edit: boolean;
            can_delete: boolean;
        };
    }>;
}

interface Props {
    user: UserData;
    roles: Record<string, string>;
}

export default function Show({ user, roles }: Props) {
    const getRoleBadgeColor = (role: string) => {
        switch (role) {
            case 'admin':
                return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
            case 'manager':
                return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
            case 'viewer':
                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
            default:
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    return (
        <AppLayout>
            <Head title={`User: ${user.name}`} />

            <div className="p-6 max-w-4xl mx-auto space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                        <Link href="/users">
                            <Button variant="ghost" size="sm">
                                <ArrowLeft className="w-4 h-4 mr-2" />
                                Back to Users
                            </Button>
                        </Link>
                    </div>
                    <Link href={`/users/${user.id}/edit`}>
                        <Button className="bg-blue-600 hover:bg-blue-700">
                            <Edit className="w-4 h-4 mr-2" />
                            Edit User
                        </Button>
                    </Link>
                </div>

                {/* User Profile */}
                <Card>
                    <CardHeader>
                        <div className="flex items-center space-x-4">
                            <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-teal-600 rounded-full flex items-center justify-center">
                                <span className="text-white font-bold text-xl">
                                    {user.name.charAt(0).toUpperCase()}
                                </span>
                            </div>
                            <div className="flex-1">
                                <div className="flex items-center space-x-3">
                                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                                        {user.name}
                                    </h1>
                                    <Badge className={getRoleBadgeColor(user.role)}>
                                        <Shield className="w-3 h-3 mr-1" />
                                        {roles[user.role] || user.role}
                                    </Badge>
                                </div>
                                <p className="text-gray-600 dark:text-gray-400 mt-1">
                                    {user.email}
                                </p>
                            </div>
                        </div>
                    </CardHeader>
                </Card>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* User Details */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center space-x-2">
                                <User className="w-5 h-5" />
                                <span>User Details</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center space-x-3">
                                <Mail className="w-4 h-4 text-gray-400" />
                                <div>
                                    <p className="text-sm font-medium text-gray-900 dark:text-white">Email</p>
                                    <p className="text-sm text-gray-600 dark:text-gray-400">{user.email}</p>
                                </div>
                            </div>

                            <div className="flex items-center space-x-3">
                                <Shield className="w-4 h-4 text-gray-400" />
                                <div>
                                    <p className="text-sm font-medium text-gray-900 dark:text-white">Role</p>
                                    <p className="text-sm text-gray-600 dark:text-gray-400">
                                        {roles[user.role] || user.role}
                                    </p>
                                </div>
                            </div>

                            <div className="flex items-center space-x-3">
                                <Calendar className="w-4 h-4 text-gray-400" />
                                <div>
                                    <p className="text-sm font-medium text-gray-900 dark:text-white">Created</p>
                                    <p className="text-sm text-gray-600 dark:text-gray-400">
                                        {formatDate(user.created_at)}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Instance Access */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center justify-between">
                                <div className="flex items-center space-x-2">
                                    <Server className="w-5 h-5" />
                                    <span>N8N Instance Access</span>
                                </div>
                                <Badge variant="outline">
                                    {user.n8n_instances.length} instances
                                </Badge>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {user.n8n_instances.length === 0 ? (
                                <div className="text-center py-6">
                                    <Server className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                                    <p className="text-sm text-gray-600 dark:text-gray-400">
                                        No instance access configured
                                    </p>
                                </div>
                            ) : (
                                <div className="space-y-3">
                                    {user.n8n_instances.map((instance) => (
                                        <div
                                            key={instance.id}
                                            className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg"
                                        >
                                            <div>
                                                <h4 className="font-medium text-gray-900 dark:text-white">
                                                    {instance.name}
                                                </h4>
                                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                                    {instance.url}
                                                </p>
                                            </div>
                                            <div className="flex space-x-2">
                                                {instance.pivot.can_edit && (
                                                    <Badge variant="outline" className="text-xs">
                                                        Edit
                                                    </Badge>
                                                )}
                                                {instance.pivot.can_delete && (
                                                    <Badge variant="outline" className="text-xs">
                                                        Delete
                                                    </Badge>
                                                )}
                                                {!instance.pivot.can_edit && !instance.pivot.can_delete && (
                                                    <Badge variant="outline" className="text-xs">
                                                        View Only
                                                    </Badge>
                                                )}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>

                {/* Role Permissions */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Settings className="w-5 h-5" />
                            <span>Role Permissions</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                                <h4 className="font-medium text-gray-900 dark:text-white mb-2">Dashboard Access</h4>
                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                    {user.role === 'admin' || user.role === 'manager' || user.role === 'viewer'
                                        ? '✅ Can view dashboard'
                                        : '❌ No dashboard access'}
                                </p>
                            </div>

                            <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                                <h4 className="font-medium text-gray-900 dark:text-white mb-2">Instance Management</h4>
                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                    {user.role === 'admin' || user.role === 'manager'
                                        ? '✅ Can manage instances'
                                        : '❌ View only'}
                                </p>
                            </div>

                            <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                                <h4 className="font-medium text-gray-900 dark:text-white mb-2">User Management</h4>
                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                    {user.role === 'admin'
                                        ? '✅ Can manage users'
                                        : '❌ No user management'}
                                </p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
