import React, { useEffect, useState } from 'react';
import { Head } from '@inertiajs/react';
import AppLayout from '@/Layouts/AppLayout';
import { PageProps } from '@/types';
import { generateBreadcrumbs } from '@/utils/breadcrumbs';
import MetricCard from '@/components/MetricCard';
import LineChartCard from '@/components/LineChartCard';
import { LoadingCard } from '@/components/LoadingSpinner';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Activity, CheckCircle2, XCircle, Clock, AlertTriangle } from 'lucide-react';
import axios from 'axios';

interface Workflow {
    id: number;
    name: string;
}

interface AnalyticsData {
    workflow: Workflow;
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
    avgExecutionTime: number;
    monthlyExecutions: { name: string; executions: number }[];
}

export default function Analytics({ workflow }: PageProps<{ workflow: Workflow }>) {
    const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        axios.get(`/api/analytics/workflow/${workflow.id}`)
            .then(response => {
                setAnalyticsData(response.data);
            })
            .catch(err => {
                setError('Failed to load analytics data.');
                console.error(err);
            })
            .finally(() => {
                setLoading(false);
            });
    }, [workflow.id]);

    const breadcrumbs = generateBreadcrumbs(`/workflows/${workflow.id}/analytics`, {
        workflowName: workflow.name
    });

    if (loading) {
        return (
            <AppLayout title={`Analytics: ${workflow.name}`} breadcrumbs={breadcrumbs}>
                <Head title={`Analytics: ${workflow.name}`} />
                <div className="p-8 space-y-6">
                    <div>
                        <h2 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
                            Analytics for {workflow.name}
                        </h2>
                        <p className="mt-2 text-slate-600">
                            Workflow performance metrics and insights
                        </p>
                    </div>
                    <LoadingCard />
                </div>
            </AppLayout>
        );
    }

    if (error) {
        return (
            <AppLayout title={`Analytics: ${workflow.name}`} breadcrumbs={breadcrumbs}>
                <Head title={`Analytics: ${workflow.name}`} />
                <div className="p-8 space-y-6">
                    <div>
                        <h2 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
                            Analytics for {workflow.name}
                        </h2>
                        <p className="mt-2 text-slate-600">
                            Workflow performance metrics and insights
                        </p>
                    </div>
                    <Card className="glass-card p-8 rounded-2xl border-red-200">
                        <div className="flex flex-col items-center justify-center space-y-4">
                            <div className="p-4 rounded-2xl bg-gradient-to-br from-red-500 to-orange-500 shadow-lg">
                                <AlertTriangle className="w-8 h-8 text-white" />
                            </div>
                            <div className="text-center">
                                <h3 className="text-lg font-semibold text-red-600">
                                    Error Loading Analytics
                                </h3>
                                <p className="text-sm text-slate-500 mt-1">
                                    {error}
                                </p>
                            </div>
                        </div>
                    </Card>
                </div>
            </AppLayout>
        );
    }

    if (!analyticsData) {
        return (
            <AppLayout title={`Analytics: ${workflow.name}`} breadcrumbs={breadcrumbs}>
                <Head title={`Analytics: ${workflow.name}`} />
                <div className="p-8 space-y-6">
                    <div>
                        <h2 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
                            Analytics for {workflow.name}
                        </h2>
                        <p className="mt-2 text-slate-600">
                            Workflow performance metrics and insights
                        </p>
                    </div>
                    <Card className="glass-card p-8 rounded-2xl">
                        <div className="flex flex-col items-center justify-center space-y-4">
                            <div className="p-4 rounded-2xl bg-gradient-to-br from-slate-400 to-slate-600 shadow-lg">
                                <Activity className="w-8 h-8 text-white" />
                            </div>
                            <div className="text-center">
                                <h3 className="text-lg font-semibold bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
                                    No Analytics Data
                                </h3>
                                <p className="text-sm text-slate-500 mt-1">
                                    No analytics data available for this workflow yet.
                                </p>
                            </div>
                        </div>
                    </Card>
                </div>
            </AppLayout>
        );
    }

    return (
        <AppLayout title={`Analytics: ${workflow.name}`} breadcrumbs={breadcrumbs}>
            <Head title={`Analytics: ${workflow.name}`} />
            <div className="p-8 space-y-8">
                {/* Header */}
                <div>
                    <h2 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
                        Analytics for {analyticsData.workflow.name}
                    </h2>
                    <p className="mt-2 text-slate-600">
                        Detailed performance metrics and insights for this workflow
                    </p>
                </div>

                {/* Metrics Grid */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                    <MetricCard
                        title="Total Executions"
                        value={analyticsData.totalExecutions.toLocaleString()}
                        description="Total executions for this workflow"
                        icon={<Activity className="h-5 w-5" />}
                        gradient="from-blue-500 to-cyan-500"
                    />
                    <MetricCard
                        title="Successful Executions"
                        value={analyticsData.successfulExecutions.toLocaleString()}
                        description="Successfully completed executions"
                        icon={<CheckCircle2 className="h-5 w-5" />}
                        gradient="from-emerald-500 to-green-500"
                    />
                    <MetricCard
                        title="Failed Executions"
                        value={analyticsData.failedExecutions.toLocaleString()}
                        description="Executions that encountered errors"
                        icon={<XCircle className="h-5 w-5" />}
                        gradient="from-red-500 to-orange-500"
                    />
                    <MetricCard
                        title="Avg. Execution Time"
                        value={`${analyticsData.avgExecutionTime}s`}
                        description="Average workflow completion time"
                        icon={<Clock className="h-5 w-5" />}
                        gradient="from-amber-500 to-orange-500"
                    />
                </div>

                {/* Chart Section */}
                <Card className="glass-card shadow-lg">
                    <CardHeader>
                        <CardTitle className="text-xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
                            Monthly Executions Trend
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <LineChartCard
                            title=""
                            data={analyticsData.monthlyExecutions}
                            dataKey="name"
                            lineKey="executions"
                        />
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
