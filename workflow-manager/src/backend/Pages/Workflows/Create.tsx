import React from 'react';
import { Head } from '@inertiajs/react';
import AppLayout from '@/Layouts/AppLayout';
import { useForm } from '@inertiajs/react';
import { generateBreadcrumbs } from '@/utils/breadcrumbs';
import { Button } from '@/components/ui/button';
import { PageProps } from '@/types';

interface N8nInstance {
    id: number;
    name: string;
}

export default function Create({ instances }: PageProps<{ instances: N8nInstance[] }>) {
    const { data, setData, post, processing, errors } = useForm({
        n8n_instance_id: instances.length > 0 ? instances[0].id : '',
        n8n_workflow_id: '',
        name: '',
        is_active: false,
        tags: '',
        nodes: '',
        connections: '',
    });

    function submit(e: React.FormEvent) {
        e.preventDefault();
        post(route('workflows.store'));
    }

    const breadcrumbs = generateBreadcrumbs('/workflows/create');

    return (
        <AppLayout title="Create Workflow" breadcrumbs={breadcrumbs}>
            <Head title="Create Workflow" />
            <div className="p-8">
                <h2 className="text-3xl font-bold">Add New Workflow</h2>

                <form onSubmit={submit} className="mt-8 max-w-md space-y-4">
                    <div>
                        <label htmlFor="n8n_instance_id" className="block text-sm font-medium text-muted-foreground">N8N Instance</label>
                        <select
                            id="n8n_instance_id"
                            value={data.n8n_instance_id}
                            onChange={(e) => setData('n8n_instance_id', parseInt(e.target.value))}
                            className="mt-1 block w-full border-border bg-background rounded-md shadow-sm"
                        >
                            {instances.map(instance => (
                                <option key={instance.id} value={instance.id}>
                                    {instance.name}
                                </option>
                            ))}
                        </select>
                        {errors.n8n_instance_id && <p className="mt-2 text-sm text-destructive">{errors.n8n_instance_id}</p>}
                    </div>

                    <div>
                        <label htmlFor="name" className="block text-sm font-medium text-muted-foreground">Workflow Name</label>
                        <input
                            id="name"
                            type="text"
                            value={data.name}
                            onChange={(e) => setData('name', e.target.value)}
                            className="mt-1 block w-full border-border bg-background rounded-md shadow-sm"
                        />
                        {errors.name && <p className="mt-2 text-sm text-destructive">{errors.name}</p>}
                    </div>

                    <div>
                        <label htmlFor="n8n_workflow_id" className="block text-sm font-medium text-muted-foreground">N8N Workflow ID</label>
                        <input
                            id="n8n_workflow_id"
                            type="text"
                            value={data.n8n_workflow_id}
                            onChange={(e) => setData('n8n_workflow_id', e.target.value)}
                            className="mt-1 block w-full border-border bg-background rounded-md shadow-sm"
                        />
                        {errors.n8n_workflow_id && <p className="mt-2 text-sm text-destructive">{errors.n8n_workflow_id}</p>}
                    </div>

                    <div>
                        <label htmlFor="is_active" className="block text-sm font-medium text-muted-foreground">Is Active</label>
                        <input
                            id="is_active"
                            type="checkbox"
                            checked={data.is_active}
                            onChange={(e) => setData('is_active', e.target.checked)}
                            className="mt-1 block border-border bg-background rounded-md shadow-sm"
                        />
                        {errors.is_active && <p className="mt-2 text-sm text-destructive">{errors.is_active}</p>}
                    </div>

                    <div>
                        <label htmlFor="tags" className="block text-sm font-medium text-muted-foreground">Tags (comma-separated)</label>
                        <input
                            id="tags"
                            type="text"
                            value={data.tags}
                            onChange={(e) => setData('tags', e.target.value)}
                            className="mt-1 block w-full border-border bg-background rounded-md shadow-sm"
                        />
                        {errors.tags && <p className="mt-2 text-sm text-destructive">{errors.tags}</p>}
                    </div>

                    <div>
                        <label htmlFor="nodes" className="block text-sm font-medium text-muted-foreground">Nodes (JSON)</label>
                        <textarea
                            id="nodes"
                            value={data.nodes}
                            onChange={(e) => setData('nodes', e.target.value)}
                            className="mt-1 block w-full border-border bg-background rounded-md shadow-sm h-32"
                        ></textarea>
                        {errors.nodes && <p className="mt-2 text-sm text-destructive">{errors.nodes}</p>}
                    </div>

                    <div>
                        <label htmlFor="connections" className="block text-sm font-medium text-muted-foreground">Connections (JSON)</label>
                        <textarea
                            id="connections"
                            value={data.connections}
                            onChange={(e) => setData('connections', e.target.value)}
                            className="mt-1 block w-full border-border bg-background rounded-md shadow-sm h-32"
                        ></textarea>
                        {errors.connections && <p className="mt-2 text-sm text-destructive">{errors.connections}</p>}
                    </div>

                    <Button type="submit" disabled={processing}>
                        {processing ? 'Saving...' : 'Save Workflow'}
                    </Button>
                </form>
            </div>
        </AppLayout>
    );
}
