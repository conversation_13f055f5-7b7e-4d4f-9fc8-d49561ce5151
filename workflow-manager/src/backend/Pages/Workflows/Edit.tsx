import React from 'react';
import AppLayout from '@/Layouts/AppLayout';
import { useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { PageProps, Workflow, N8nInstance } from '@/types';

export default function Edit({ workflow, instances }: PageProps<{ workflow: Workflow; instances: N8nInstance[] }>) {
    const { data, setData, put, processing, errors } = useForm({
        n8n_instance_id: workflow.n8n_instance_id,
        n8n_workflow_id: workflow.n8n_workflow_id,
        name: workflow.name,
        is_active: workflow.is_active,
        tags: Array.isArray(workflow.tags) ? workflow.tags.join(', ') : (workflow.tags || ''),
        nodes: JSON.stringify(workflow.nodes, null, 2),
        connections: JSON.stringify(workflow.connections, null, 2),
    });

    function submit(e: React.FormEvent) {
        e.preventDefault();

        // Convert tags string to array and parse JSON
        const formData = {
            ...data,
            tags: data.tags.split(',').map(tag => tag.trim()).filter(tag => tag),
            nodes: JSON.parse(data.nodes),
            connections: JSON.parse(data.connections),
        };

        // Update the form data and submit
        Object.keys(formData).forEach(key => {
            setData(key as any, (formData as any)[key]);
        });

        put(route('workflows.update', workflow.id));
    }

    return (
        <AppLayout>
            <div className="p-8">
                <h2 className="text-3xl font-bold">Edit Workflow</h2>
                <p className="mt-2 text-muted-foreground">
                    Update workflow configuration and settings.
                </p>

                <form onSubmit={submit} className="mt-8 space-y-6">
                    <div>
                        <label htmlFor="n8n_instance_id" className="block text-sm font-medium">
                            N8N Instance
                        </label>
                        <Select
                            value={data.n8n_instance_id.toString()}
                            onValueChange={(value) => setData('n8n_instance_id', parseInt(value))}
                        >
                            <SelectTrigger className="mt-1">
                                <SelectValue placeholder="Select an N8N instance" />
                            </SelectTrigger>
                            <SelectContent>
                                {instances.map((instance) => (
                                    <SelectItem key={instance.id} value={instance.id.toString()}>
                                        {instance.name}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        {errors.n8n_instance_id && <p className="mt-1 text-sm text-red-600">{errors.n8n_instance_id}</p>}
                    </div>

                    <div>
                        <label htmlFor="n8n_workflow_id" className="block text-sm font-medium">
                            N8N Workflow ID
                        </label>
                        <Input
                            id="n8n_workflow_id"
                            type="text"
                            value={data.n8n_workflow_id}
                            onChange={(e) => setData('n8n_workflow_id', e.target.value)}
                            className="mt-1"
                            required
                        />
                        {errors.n8n_workflow_id && <p className="mt-1 text-sm text-red-600">{errors.n8n_workflow_id}</p>}
                    </div>

                    <div>
                        <label htmlFor="name" className="block text-sm font-medium">
                            Workflow Name
                        </label>
                        <Input
                            id="name"
                            type="text"
                            value={data.name}
                            onChange={(e) => setData('name', e.target.value)}
                            className="mt-1"
                            required
                        />
                        {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
                    </div>

                    <div className="flex items-center space-x-2">
                        <Switch
                            id="is_active"
                            checked={data.is_active}
                            onCheckedChange={(checked) => setData('is_active', checked)}
                        />
                        <label htmlFor="is_active" className="text-sm font-medium">
                            Active
                        </label>
                    </div>

                    <div>
                        <label htmlFor="tags" className="block text-sm font-medium">
                            Tags (comma-separated)
                        </label>
                        <Input
                            id="tags"
                            type="text"
                            value={data.tags}
                            onChange={(e) => setData('tags', e.target.value)}
                            className="mt-1"
                            placeholder="automation, webhook, email"
                        />
                        {errors.tags && <p className="mt-1 text-sm text-red-600">{errors.tags}</p>}
                    </div>

                    <div>
                        <label htmlFor="nodes" className="block text-sm font-medium">
                            Nodes (JSON)
                        </label>
                        <textarea
                            id="nodes"
                            value={data.nodes}
                            onChange={(e) => setData('nodes', e.target.value)}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm font-mono text-sm"
                            rows={8}
                            required
                        />
                        {errors.nodes && <p className="mt-1 text-sm text-red-600">{errors.nodes}</p>}
                    </div>

                    <div>
                        <label htmlFor="connections" className="block text-sm font-medium">
                            Connections (JSON)
                        </label>
                        <textarea
                            id="connections"
                            value={data.connections}
                            onChange={(e) => setData('connections', e.target.value)}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm font-mono text-sm"
                            rows={8}
                            required
                        />
                        {errors.connections && <p className="mt-1 text-sm text-red-600">{errors.connections}</p>}
                    </div>

                    <div className="flex justify-end space-x-4">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => window.history.back()}
                        >
                            Cancel
                        </Button>
                        <Button type="submit" disabled={processing}>
                            {processing ? 'Updating...' : 'Update Workflow'}
                        </Button>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
}
