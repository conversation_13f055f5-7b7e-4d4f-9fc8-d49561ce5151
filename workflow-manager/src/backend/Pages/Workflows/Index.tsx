import { useState, useEffect } from 'react';
import { Head } from '@inertiajs/react';
import AppLayout from '@/Layouts/AppLayout';
import { PageProps } from '@/types';
import { Link, router } from '@inertiajs/react';
import { generateBreadcrumbs } from '@/utils/breadcrumbs';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { RefreshCw, Plus, Activity, Edit } from 'lucide-react';
import debounce from 'lodash.debounce';

interface Workflow {
    id: number;
    n8n_instance_id: number;
    n8n_workflow_id: string;
    name: string;
    is_active: boolean;
    tags: string[];
    nodes: any;
    connections: any;
    last_sync_at: string;
    n8n_instance?: {
        id: number;
        name: string;
        health_status: string;
        url: string;
    };
}

interface Filters {
    search: string | null;
    status: 'active' | 'inactive' | null;
    tag: string | null;
    min_executions: number | null;
    max_executions: number | null;
    last_executed_before: string | null;
    last_executed_after: string | null;
}

export default function Index({ workflows, filters }: PageProps<{ workflows: Workflow[], filters: Filters }>) {
    const [search, setSearch] = useState(filters.search || '');
    const [status, setStatus] = useState(filters.status || '');
    const [tag, setTag] = useState(filters.tag || '');
    const [minExecutions, setMinExecutions] = useState(filters.min_executions || '');
    const [maxExecutions, setMaxExecutions] = useState(filters.max_executions || '');
    const [lastExecutedBefore, setLastExecutedBefore] = useState(filters.last_executed_before || '');
    const [lastExecutedAfter, setLastExecutedAfter] = useState(filters.last_executed_after || '');
    const [isSyncing, setIsSyncing] = useState(false);

    const handleToggle = (id: number, is_active: boolean) => {
        router.put(route('workflows.update', id), { is_active: is_active });
    };

    const handleSync = () => {
        setIsSyncing(true);
        router.post(route('workflows.sync'), {}, {
            onFinish: () => setIsSyncing(false),
        });
    };

    useEffect(() => {
        const handler = debounce(() => {
            const query: Record<string, any> = {};
            if (search) query.search = search;
            if (status) query.status = status;
            if (tag) query.tag = tag;
            if (minExecutions) query.min_executions = minExecutions;
            if (maxExecutions) query.max_executions = maxExecutions;
            if (lastExecutedBefore) query.last_executed_before = lastExecutedBefore;
            if (lastExecutedAfter) query.last_executed_after = lastExecutedAfter;

            router.get(route('workflows.index'), query, {
                preserveState: true,
                replace: true,
            });
        }, 300);

        // Only trigger handler if we have actual filter values (not on initial mount)
        const hasFilters = search || status || tag || minExecutions || maxExecutions || lastExecutedBefore || lastExecutedAfter;
        if (hasFilters) {
            handler();
        }

        return () => {
            handler.cancel();
        };
    }, [search, status, tag, minExecutions, maxExecutions, lastExecutedBefore, lastExecutedAfter]);

    const breadcrumbs = generateBreadcrumbs('/workflows');

    return (
        <AppLayout title="Workflows" breadcrumbs={breadcrumbs}>
            <Head title="Workflows" />
            <div className="p-8 space-y-6">
                {/* Header */}
                <div className="flex justify-between items-start">
                    <div>
                        <h2 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
                            Manage Workflows
                        </h2>
                        <p className="mt-2 text-slate-600">
                            Manage and monitor your N8N automation workflows
                        </p>
                    </div>
                    <div className="flex space-x-3">
                        <Button
                            variant="outline"
                            onClick={handleSync}
                            disabled={isSyncing}
                            className="flex items-center space-x-2"
                        >
                            <RefreshCw className={`w-4 h-4 ${isSyncing ? 'animate-spin' : ''}`} />
                            <span>{isSyncing ? 'Syncing...' : 'Sync Workflows'}</span>
                        </Button>
                        <Link href={route('workflows.create')}>
                            <Button className="flex items-center space-x-2">
                                <Plus className="w-4 h-4" />
                                <span>Add Workflow</span>
                            </Button>
                        </Link>
                    </div>
                </div>

                <div className="flex flex-wrap gap-4 mt-4">
                    <Input
                        placeholder="Search workflows..."
                        value={search}
                        onChange={(e) => setSearch(e.target.value)}
                        className="max-w-sm"
                    />
                    <Select value={status || "all"} onValueChange={(value) => setStatus(value === "all" ? "" : value)}>
                        <SelectTrigger className="w-[180px]">
                            <SelectValue placeholder="Filter by Status" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">All Statuses</SelectItem>
                            <SelectItem value="active">Active</SelectItem>
                            <SelectItem value="inactive">Inactive</SelectItem>
                        </SelectContent>
                    </Select>
                    <Input
                        type="number"
                        placeholder="Min Executions"
                        value={minExecutions}
                        onChange={(e) => setMinExecutions(e.target.value)}
                        className="w-[180px]"
                    />
                    <Input
                        type="number"
                        placeholder="Max Executions"
                        value={maxExecutions}
                        onChange={(e) => setMaxExecutions(e.target.value)}
                        className="w-[180px]"
                    />
                    <Input
                        type="date"
                        placeholder="Last Executed Before"
                        value={lastExecutedBefore}
                        onChange={(e) => setLastExecutedBefore(e.target.value)}
                        className="w-[180px]"
                    />
                    <Input
                        type="date"
                        placeholder="Last Executed After"
                        value={lastExecutedAfter}
                        onChange={(e) => setLastExecutedAfter(e.target.value)}
                        className="w-[180px]"
                    />
                </div>

                {/* Workflows List */}
                <div className="mt-8">
                    {workflows.length === 0 ? (
                        <Card className="glass-card p-12 rounded-2xl text-center">
                            <div className="flex flex-col items-center space-y-4">
                                <div className="p-4 rounded-2xl bg-gradient-to-br from-slate-400 to-slate-600 shadow-lg">
                                    <Activity className="w-8 h-8 text-white" />
                                </div>
                                <div>
                                    <h3 className="text-lg font-semibold bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
                                        No Workflows Found
                                    </h3>
                                    <p className="text-sm text-slate-500 mt-1 max-w-md">
                                        {search || status || tag || minExecutions || maxExecutions || lastExecutedBefore || lastExecutedAfter
                                            ? "No workflows match your current filters. Try adjusting your search criteria."
                                            : "Get started by syncing workflows from your N8N instances or creating a new workflow."
                                        }
                                    </p>
                                </div>
                                <div className="flex space-x-3 mt-4">
                                    <Button
                                        variant="outline"
                                        onClick={handleSync}
                                        disabled={isSyncing}
                                        className="flex items-center space-x-2"
                                    >
                                        <RefreshCw className={`w-4 h-4 ${isSyncing ? 'animate-spin' : ''}`} />
                                        <span>{isSyncing ? 'Syncing...' : 'Sync from N8N'}</span>
                                    </Button>
                                    <Link href={route('workflows.create')}>
                                        <Button className="flex items-center space-x-2">
                                            <Plus className="w-4 h-4" />
                                            <span>Create Workflow</span>
                                        </Button>
                                    </Link>
                                </div>
                            </div>
                        </Card>
                    ) : (
                        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                            {workflows.map((workflow) => (
                                <Card key={workflow.id} className="glass-card hover:shadow-xl transition-all duration-300 group">
                                    <CardHeader className="pb-3">
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center space-x-2">
                                                {/* Workflow Active Status */}
                                                <div className={`w-3 h-3 rounded-full ${
                                                    workflow.is_active && workflow.n8n_instance?.health_status === 'online'
                                                        ? 'bg-emerald-500'
                                                        : 'bg-gray-300'
                                                }`} />
                                                {/* Instance Connection Status */}
                                                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                                    workflow.n8n_instance?.health_status === 'online'
                                                        ? 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200'
                                                        : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300'
                                                }`}>
                                                    {workflow.n8n_instance?.health_status === 'online' ? 'Connected' : 'Not Connected'}
                                                </span>
                                            </div>
                                            <Switch
                                                checked={workflow.is_active}
                                                onCheckedChange={() => handleToggle(workflow.id, !workflow.is_active)}
                                                disabled={workflow.n8n_instance?.health_status !== 'online'}
                                            />
                                        </div>
                                        <CardTitle className="text-lg font-semibold text-slate-900 group-hover:text-blue-600 transition-colors">
                                            <Link href={route('workflows.show', workflow.id)}>
                                                {workflow.name}
                                            </Link>
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="space-y-2">
                                            <p className="text-xs text-slate-500 uppercase tracking-wide">
                                                N8N Instance
                                            </p>
                                            <p className="text-sm text-slate-700">
                                                {workflow.n8n_instance?.name || 'Unknown Instance'}
                                            </p>
                                        </div>
                                        <div className="space-y-2">
                                            <p className="text-xs text-slate-500 uppercase tracking-wide">
                                                Workflow ID
                                            </p>
                                            <p className="text-sm font-mono text-slate-700">
                                                {workflow.n8n_workflow_id}
                                            </p>
                                        </div>
                                        <div className="space-y-2">
                                            <p className="text-xs text-slate-500 uppercase tracking-wide">
                                                Last Sync
                                            </p>
                                            <p className="text-sm text-slate-700">
                                                {workflow.last_sync_at ? new Date(workflow.last_sync_at).toLocaleDateString() : 'Never'}
                                            </p>
                                        </div>
                                        <div className="flex space-x-2 pt-2">
                                            <Link href={route('workflows.show', workflow.id)} className="flex-1">
                                                <Button variant="outline" size="sm" className="w-full">
                                                    View Details
                                                </Button>
                                            </Link>
                                            <Link href={route('workflows.edit', workflow.id)}>
                                                <Button variant="outline" size="sm">
                                                    <Edit className="w-4 h-4" />
                                                </Button>
                                            </Link>
                                            <Link href={route('workflows.analytics', workflow.id)}>
                                                <Button variant="outline" size="sm">
                                                    <Activity className="w-4 h-4" />
                                                </Button>
                                            </Link>
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    )}
                </div>
            </div>
        </AppLayout>
    );
}
