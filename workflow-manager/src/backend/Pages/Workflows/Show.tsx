import React, { useCallback } from 'react';
import { Head } from '@inertiajs/react';
import AppLayout from '@/Layouts/AppLayout';
import { PageProps } from '@/types';
import ReactFlow, {
    Controls,
    Background,
    useNodesState,
    useEdgesState,
    addEdge,
    Connection,
    Edge,
    Node,
    BackgroundVariant,
    MiniMap,
    Position
} from 'reactflow';
import { generateBreadcrumbs } from '@/utils/breadcrumbs';

import 'reactflow/dist/style.css';

interface Workflow {
    id: number;
    n8n_instance_id: number;
    n8n_workflow_id: string;
    name: string;
    is_active: boolean;
    tags: string[];
    nodes: any[] | null;
    connections: any[] | any | null; // Can be array or N8N object format
    last_sync_at: string;
    n8n_instance?: {
        id: number;
        name: string;
        health_status: string;
        url: string;
        is_active: boolean;
    };
}

export default function Show({ workflow }: PageProps<{ workflow: Workflow }>) {
    // Safely transform n8n nodes/connections to React Flow format
    const initialNodes: Node[] = Array.isArray(workflow.nodes)
        ? workflow.nodes.map((node: any) => ({
            id: node.id || node.name || `node-${Math.random()}`,
            type: 'default',
            position: {
                x: node.position?.[0] || node.position?.x || Math.random() * 400,
                y: node.position?.[1] || node.position?.y || Math.random() * 400
            },
            data: {
                label: node.name || node.type || 'Unnamed Node',
                nodeType: node.type,
                parameters: node.parameters
            },
            style: {
                background: '#fff',
                border: '2px solid #0ea5e9',
                borderRadius: '8px',
                fontSize: '12px',
                fontWeight: '500',
                width: 150,
                height: 40
            },
            sourcePosition: Position.Right,
            targetPosition: Position.Left
        }))
        : [];

    // Create a mapping of node names to IDs for connection matching
    const nodeNameToId = new Map<string, string>();
    initialNodes.forEach(node => {
        // Map both the node ID and the original name to the final ID
        nodeNameToId.set(node.id, node.id);
        if (node.data.label && node.data.label !== node.id) {
            nodeNameToId.set(node.data.label, node.id);
        }
    });

    // Handle N8N connections format - they can be stored as objects or arrays
    let initialEdges: Edge[] = [];

    if (workflow.connections && typeof workflow.connections === 'object') {
        // N8N stores connections as an object with node names as keys
        // Format: { "NodeName": { "main": [[{ "node": "NextNode", "type": "main", "index": 0 }]] } }
        Object.entries(workflow.connections).forEach(([sourceNode, connections]: [string, any]) => {
            if (connections && connections.main && Array.isArray(connections.main)) {
                connections.main.forEach((connectionGroup: any[], outputIndex: number) => {
                    if (Array.isArray(connectionGroup)) {
                        connectionGroup.forEach((connection: any, connectionIndex: number) => {
                            if (connection && connection.node) {
                                // Try to find the actual node IDs using our mapping
                                const sourceId = nodeNameToId.get(sourceNode) || sourceNode;
                                const targetId = nodeNameToId.get(connection.node) || connection.node;

                                // Only create edge if both nodes exist
                                if (nodeNameToId.has(sourceNode) && nodeNameToId.has(connection.node)) {
                                    initialEdges.push({
                                        id: `e${sourceId}-${targetId}-${outputIndex}-${connectionIndex}`,
                                        source: sourceId,
                                        target: targetId,
                                        type: 'smoothstep',
                                        style: { stroke: '#0ea5e9', strokeWidth: 2 },
                                        animated: workflow.is_active && workflow.n8n_instance?.health_status === 'online'
                                    });
                                }
                            }
                        });
                    }
                });
            }
        });
    } else if (Array.isArray(workflow.connections)) {
        // Handle array format (legacy or custom format)
        initialEdges = workflow.connections.map((connection: any, index: number) => {
            const sourceId = nodeNameToId.get(connection.fromNode || connection.source) || connection.fromNode || connection.source;
            const targetId = nodeNameToId.get(connection.toNode || connection.target) || connection.toNode || connection.target;

            return {
                id: `e${sourceId}-${targetId}-${index}`,
                source: sourceId,
                target: targetId,
                type: 'smoothstep',
                style: { stroke: '#0ea5e9', strokeWidth: 2 },
                animated: workflow.is_active && workflow.n8n_instance?.health_status === 'online'
            };
        }).filter(edge =>
            // Only include edges where both source and target nodes exist
            nodeNameToId.has(edge.source) && nodeNameToId.has(edge.target)
        );
    }

    const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
    const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

    const onConnect = useCallback(
        (params: Connection) => setEdges((eds) => addEdge(params, eds)),
        [setEdges]
    );

    const breadcrumbs = generateBreadcrumbs(`/workflows/${workflow.id}`, {
        workflowName: workflow.name
    });

    return (
        <AppLayout title={`Workflow: ${workflow.name}`} breadcrumbs={breadcrumbs}>
            <Head title={`Workflow: ${workflow.name}`} />
            <div className="p-8">
                <h2 className="text-3xl font-bold">Workflow: {workflow.name}</h2>
                <p className="mt-2 text-muted-foreground">ID: {workflow.n8n_workflow_id}</p>
                <p className="text-sm text-muted-foreground">Status: {workflow.is_active ? 'Active' : 'Inactive'}</p>

                {/* Workflow Statistics */}
                <div className="mt-4 grid grid-cols-4 gap-4 text-sm">
                    <div className="bg-slate-50 dark:bg-slate-800 p-3 rounded-lg">
                        <span className="font-medium">Nodes:</span> {nodes.length}
                    </div>
                    <div className="bg-slate-50 dark:bg-slate-800 p-3 rounded-lg">
                        <span className="font-medium">Connections:</span> {edges.length}
                    </div>
                    <div className="bg-slate-50 dark:bg-slate-800 p-3 rounded-lg">
                        <span className="font-medium">Tags:</span> {workflow.tags?.length || 0}
                    </div>
                    <div className="bg-slate-50 dark:bg-slate-800 p-3 rounded-lg">
                        <span className="font-medium">Status:</span>
                        <span className={`ml-1 ${
                            workflow.is_active && workflow.n8n_instance?.health_status === 'online'
                                ? 'text-green-600'
                                : workflow.is_active && workflow.n8n_instance?.health_status !== 'online'
                                ? 'text-yellow-600'
                                : 'text-slate-500'
                        }`}>
                            {workflow.is_active && workflow.n8n_instance?.health_status === 'online'
                                ? 'Active & Connected'
                                : workflow.is_active && workflow.n8n_instance?.health_status !== 'online'
                                ? 'Active but Disconnected'
                                : 'Inactive'}
                        </span>
                    </div>
                </div>

                {/* React Flow Visualization */}
                <div className="mt-8">
                    <h3 className="text-lg font-semibold mb-4">Workflow Visualization</h3>
                    <div
                        style={{ width: '100%', height: '600px' }}
                        className="border rounded-lg bg-slate-50 dark:bg-slate-900"
                    >
                        {nodes.length > 0 ? (
                            <ReactFlow
                                nodes={nodes}
                                edges={edges}
                                onNodesChange={onNodesChange}
                                onEdgesChange={onEdgesChange}
                                onConnect={onConnect}
                                fitView
                                fitViewOptions={{ padding: 0.2 }}
                                attributionPosition="bottom-left"
                                proOptions={{ hideAttribution: true }}
                                defaultViewport={{ x: 0, y: 0, zoom: 1 }}
                                minZoom={0.1}
                                maxZoom={2}
                            >
                                <Controls
                                    position="top-left"
                                    showInteractive={false}
                                />
                                <Background
                                    variant={BackgroundVariant.Dots}
                                    gap={20}
                                    size={1}
                                    color="#94a3b8"
                                />
                                <MiniMap
                                    nodeColor="#0ea5e9"
                                    nodeStrokeWidth={3}
                                    position="bottom-right"
                                    style={{
                                        backgroundColor: '#f8fafc',
                                        border: '1px solid #e2e8f0'
                                    }}
                                />
                            </ReactFlow>
                        ) : (
                            <div className="flex items-center justify-center h-full text-slate-500">
                                <div className="text-center">
                                    <div className="w-16 h-16 mx-auto mb-4 bg-slate-200 dark:bg-slate-700 rounded-full flex items-center justify-center">
                                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                        </svg>
                                    </div>
                                    <p className="text-lg font-medium">No workflow data available</p>
                                    <p className="text-sm mt-1">This workflow doesn't have any nodes or connections to display.</p>
                                    <p className="text-xs mt-2 text-slate-400">
                                        The workflow may need to be synced from N8N or may not contain any nodes yet.
                                    </p>
                                </div>
                            </div>
                        )}
                    </div>
                </div>

                {/* Debug Information */}
                <details className="mt-8">
                    <summary className="cursor-pointer text-sm font-medium text-slate-600">
                        Debug Information
                    </summary>
                    <div className="mt-2 p-4 bg-slate-100 dark:bg-slate-800 rounded-lg text-xs">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                            <div>
                                <strong>Workflow Status:</strong>
                                <pre className="mt-1 overflow-x-auto">{JSON.stringify({
                                    is_active: workflow.is_active,
                                    n8n_instance: workflow.n8n_instance,
                                    last_sync_at: workflow.last_sync_at
                                }, null, 2)}</pre>
                            </div>
                            <div>
                                <strong>Processed Nodes ({nodes.length}):</strong>
                                <pre className="mt-1 overflow-x-auto">{JSON.stringify(nodes.map(n => ({
                                    id: n.id,
                                    label: n.data.label,
                                    position: n.position
                                })), null, 2)}</pre>
                            </div>
                            <div>
                                <strong>Processed Edges ({edges.length}):</strong>
                                <pre className="mt-1 overflow-x-auto">{JSON.stringify(edges.map(e => ({
                                    id: e.id,
                                    source: e.source,
                                    target: e.target,
                                    type: e.type,
                                    animated: e.animated
                                })), null, 2)}</pre>
                            </div>
                            <div>
                                <strong>Node Name Mapping:</strong>
                                <pre className="mt-1 overflow-x-auto">{JSON.stringify(
                                    Array.from(nodeNameToId.entries()).reduce((obj, [key, value]) => {
                                        obj[key] = value;
                                        return obj;
                                    }, {} as Record<string, string>), null, 2
                                )}</pre>
                            </div>
                            <div>
                                <strong>Raw Nodes:</strong>
                                <pre className="mt-1 overflow-x-auto">{JSON.stringify(workflow.nodes, null, 2)}</pre>
                            </div>
                            <div className="lg:col-span-2">
                                <strong>Raw Connections:</strong>
                                <pre className="mt-1 overflow-x-auto">{JSON.stringify(workflow.connections, null, 2)}</pre>
                            </div>
                        </div>
                    </div>
                </details>
            </div>
        </AppLayout>
    );
}
