import axios from 'axios';

// Configure Axios globally
window.axios = axios;
window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

// Set up CSRF token for <PERSON>vel
const token = document.head.querySelector('meta[name="csrf-token"]');
if (token) {
    window.axios.defaults.headers.common['X-CSRF-TOKEN'] = token.getAttribute('content');
}

// Set up Sanctum authentication
window.axios.defaults.withCredentials = true;

// Initialize Sanctum CSRF cookie
let csrfInitialized = false;

// Add request interceptor to ensure CSRF cookie is initialized
window.axios.interceptors.request.use(async (config) => {
    if (!csrfInitialized && config.url?.startsWith('/api/')) {
        try {
            await axios.get('/sanctum/csrf-cookie');
            csrfInitialized = true;
        } catch (error) {
            console.warn('Failed to initialize CSRF cookie:', error);
        }
    }
    return config;
});
