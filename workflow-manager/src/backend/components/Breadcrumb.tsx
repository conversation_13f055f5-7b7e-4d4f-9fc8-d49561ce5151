import React from 'react';
import { Link } from '@inertiajs/react';
import { ChevronRight, Home } from 'lucide-react';

export interface BreadcrumbItem {
    label: string;
    href?: string;
    icon?: React.ComponentType<any>;
}

interface BreadcrumbProps {
    items: BreadcrumbItem[];
    className?: string;
}

export default function Breadcrumb({ items, className = '' }: BreadcrumbProps) {
    return (
        <nav className={`flex items-center space-x-1 text-sm text-slate-600 dark:text-slate-400 ${className}`}>
            {/* Home link */}
            <Link 
                href={route('dashboard')} 
                className="flex items-center hover:text-slate-900 dark:hover:text-slate-200 transition-colors"
            >
                <Home className="w-4 h-4" />
            </Link>
            
            {items.length > 0 && (
                <ChevronRight className="w-4 h-4 text-slate-400 dark:text-slate-500" />
            )}
            
            {items.map((item, index) => {
                const isLast = index === items.length - 1;
                const Icon = item.icon;
                
                return (
                    <React.Fragment key={index}>
                        {item.href && !isLast ? (
                            <Link 
                                href={item.href}
                                className="flex items-center space-x-1 hover:text-slate-900 dark:hover:text-slate-200 transition-colors"
                            >
                                {Icon && <Icon className="w-4 h-4" />}
                                <span>{item.label}</span>
                            </Link>
                        ) : (
                            <span className={`flex items-center space-x-1 ${isLast ? 'text-slate-900 dark:text-slate-100 font-medium' : ''}`}>
                                {Icon && <Icon className="w-4 h-4" />}
                                <span>{item.label}</span>
                            </span>
                        )}
                        
                        {!isLast && (
                            <ChevronRight className="w-4 h-4 text-slate-400 dark:text-slate-500" />
                        )}
                    </React.Fragment>
                );
            })}
        </nav>
    );
}
