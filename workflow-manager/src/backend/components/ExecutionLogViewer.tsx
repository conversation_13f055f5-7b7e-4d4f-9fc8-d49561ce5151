import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import {
    FileText,
    Clock,
    CheckCircle2,
    XCircle,
    AlertTriangle,
    Play,
    Pause,
    RefreshCw,
    ChevronDown,
    ChevronRight,
    Eye,
    Download
} from 'lucide-react';
import axios from 'axios';

interface ExecutionStep {
    id: string;
    name: string;
    type: string;
    status: 'success' | 'failed' | 'running' | 'waiting';
    startTime?: string;
    endTime?: string;
    duration?: number;
    error?: string;
    data?: any;
    outputData?: any;
}

interface ExecutionLog {
    id: string;
    workflowId: string;
    workflowName: string;
    status: 'success' | 'failed' | 'running' | 'waiting';
    startedAt: string;
    finishedAt?: string;
    duration?: number;
    mode: 'manual' | 'trigger' | 'webhook';
    steps: ExecutionStep[];
    error?: string;
    metadata?: {
        instanceId: number;
        instanceName: string;
        userId?: number;
        triggerData?: any;
    };
}

interface ExecutionLogViewerProps {
    executionId: string;
    onClose?: () => void;
    autoRefresh?: boolean;
    refreshInterval?: number;
}

export default function ExecutionLogViewer({
    executionId,
    onClose,
    autoRefresh = false,
    refreshInterval = 3000
}: ExecutionLogViewerProps) {
    const [execution, setExecution] = useState<ExecutionLog | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [expandedSteps, setExpandedSteps] = useState<Set<string>>(new Set());
    const [activeTab, setActiveTab] = useState('overview');

    const fetchExecutionLog = async () => {
        try {
            const response = await axios.get(`/api/executions/${executionId}/log`);
            setExecution(response.data);
            setError(null);
        } catch (err) {
            console.error('Failed to fetch execution log:', err);
            setError('Failed to load execution log');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchExecutionLog();

        if (autoRefresh && execution?.status === 'running') {
            const interval = setInterval(fetchExecutionLog, refreshInterval);
            return () => clearInterval(interval);
        }
    }, [executionId, autoRefresh, refreshInterval, execution?.status]);

    const toggleStepExpansion = (stepId: string) => {
        const newExpanded = new Set(expandedSteps);
        if (newExpanded.has(stepId)) {
            newExpanded.delete(stepId);
        } else {
            newExpanded.add(stepId);
        }
        setExpandedSteps(newExpanded);
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'success':
                return <CheckCircle2 className="w-4 h-4 text-green-500" />;
            case 'failed':
                return <XCircle className="w-4 h-4 text-red-500" />;
            case 'running':
                return <Play className="w-4 h-4 text-blue-500 animate-pulse" />;
            case 'waiting':
                return <Clock className="w-4 h-4 text-amber-500" />;
            default:
                return <AlertTriangle className="w-4 h-4 text-gray-500" />;
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'success':
                return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
            case 'failed':
                return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
            case 'running':
                return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
            case 'waiting':
                return 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300';
            default:
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
        }
    };

    const formatDuration = (ms: number) => {
        if (ms < 1000) return `${ms}ms`;
        if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
        return `${(ms / 60000).toFixed(1)}m`;
    };

    const formatDateTime = (dateString: string) => {
        return new Date(dateString).toLocaleString();
    };

    const downloadLog = () => {
        if (!execution) return;

        const logData = JSON.stringify(execution, null, 2);
        const blob = new Blob([logData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `execution-${execution.id}-log.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    };

    if (loading) {
        return (
            <Card className="glass-card">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <RefreshCw className="w-5 h-5 animate-spin" />
                        Loading Execution Log
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="flex items-center justify-center py-8">
                        <div className="text-sm text-slate-500 dark:text-slate-400">
                            Loading execution details...
                        </div>
                    </div>
                </CardContent>
            </Card>
        );
    }

    if (error || !execution) {
        return (
            <Card className="glass-card">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <XCircle className="w-5 h-5 text-red-500" />
                        Error Loading Log
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="text-center py-8">
                        <div className="text-sm text-red-500 mb-4">
                            {error || 'Execution not found'}
                        </div>
                        <Button onClick={fetchExecutionLog} variant="outline" size="sm">
                            <RefreshCw className="w-4 h-4 mr-2" />
                            Retry
                        </Button>
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card className="glass-card">
            <CardHeader>
                <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                        <FileText className="w-5 h-5" />
                        Execution Log
                        {getStatusIcon(execution.status)}
                    </CardTitle>
                    <div className="flex items-center gap-2">
                        <Button onClick={downloadLog} variant="outline" size="sm">
                            <Download className="w-4 h-4 mr-2" />
                            Download
                        </Button>
                        {onClose && (
                            <Button onClick={onClose} variant="outline" size="sm">
                                Close
                            </Button>
                        )}
                    </div>
                </div>
                <div className="flex items-center gap-4 text-sm text-slate-500 dark:text-slate-400">
                    <span>ID: {execution.id}</span>
                    <span>•</span>
                    <span>{execution.workflowName}</span>
                    <span>•</span>
                    <span>{formatDateTime(execution.startedAt)}</span>
                    {execution.duration && (
                        <>
                            <span>•</span>
                            <span>{formatDuration(execution.duration)}</span>
                        </>
                    )}
                </div>
            </CardHeader>
            <CardContent>
                <Tabs value={activeTab} onValueChange={setActiveTab}>
                    <TabsList className="grid w-full grid-cols-3">
                        <TabsTrigger value="overview">Overview</TabsTrigger>
                        <TabsTrigger value="steps">Steps</TabsTrigger>
                        <TabsTrigger value="data">Data</TabsTrigger>
                    </TabsList>

                    <TabsContent value="overview" className="space-y-4">
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div className="p-3 rounded-lg bg-slate-50 dark:bg-slate-800/50">
                                <div className="text-xs text-slate-500 dark:text-slate-400">Status</div>
                                <Badge className={`mt-1 ${getStatusColor(execution.status)}`}>
                                    {execution.status}
                                </Badge>
                            </div>
                            <div className="p-3 rounded-lg bg-slate-50 dark:bg-slate-800/50">
                                <div className="text-xs text-slate-500 dark:text-slate-400">Mode</div>
                                <div className="font-medium capitalize">{execution.mode}</div>
                            </div>
                            <div className="p-3 rounded-lg bg-slate-50 dark:bg-slate-800/50">
                                <div className="text-xs text-slate-500 dark:text-slate-400">Steps</div>
                                <div className="font-medium">{execution.steps.length}</div>
                            </div>
                            <div className="p-3 rounded-lg bg-slate-50 dark:bg-slate-800/50">
                                <div className="text-xs text-slate-500 dark:text-slate-400">Instance</div>
                                <div className="font-medium">{execution.metadata?.instanceName}</div>
                            </div>
                        </div>

                        {execution.error && (
                            <div className="p-4 rounded-lg bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800">
                                <div className="flex items-center gap-2 mb-2">
                                    <XCircle className="w-4 h-4 text-red-500" />
                                    <span className="font-medium text-red-700 dark:text-red-300">Error</span>
                                </div>
                                <pre className="text-sm text-red-600 dark:text-red-400 whitespace-pre-wrap">
                                    {execution.error}
                                </pre>
                            </div>
                        )}
                    </TabsContent>

                    <TabsContent value="steps">
                        <ScrollArea className="h-96">
                            <div className="space-y-2">
                                {execution.steps.map((step, index) => (
                                    <div key={step.id} className="border border-slate-200 dark:border-slate-700 rounded-lg">
                                        <div
                                            className="flex items-center justify-between p-3 cursor-pointer hover:bg-slate-50 dark:hover:bg-slate-800/50"
                                            onClick={() => toggleStepExpansion(step.id)}
                                        >
                                            <div className="flex items-center gap-3">
                                                {expandedSteps.has(step.id) ?
                                                    <ChevronDown className="w-4 h-4" /> :
                                                    <ChevronRight className="w-4 h-4" />
                                                }
                                                <span className="text-xs bg-slate-100 dark:bg-slate-800 px-2 py-1 rounded">
                                                    {index + 1}
                                                </span>
                                                {getStatusIcon(step.status)}
                                                <div>
                                                    <div className="font-medium">{step.name}</div>
                                                    <div className="text-xs text-slate-500 dark:text-slate-400">
                                                        {step.type}
                                                        {step.duration && ` • ${formatDuration(step.duration)}`}
                                                    </div>
                                                </div>
                                            </div>
                                            <Badge className={`${getStatusColor(step.status)}`}>
                                                {step.status}
                                            </Badge>
                                        </div>

                                        {expandedSteps.has(step.id) && (
                                            <div className="border-t border-slate-200 dark:border-slate-700 p-3 bg-slate-50 dark:bg-slate-800/30">
                                                {step.error && (
                                                    <div className="mb-3 p-2 rounded bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800">
                                                        <div className="text-xs font-medium text-red-700 dark:text-red-300 mb-1">Error:</div>
                                                        <pre className="text-xs text-red-600 dark:text-red-400 whitespace-pre-wrap">
                                                            {step.error}
                                                        </pre>
                                                    </div>
                                                )}

                                                {step.outputData && (
                                                    <div className="mb-3">
                                                        <div className="text-xs font-medium text-slate-700 dark:text-slate-300 mb-1">Output:</div>
                                                        <pre className="text-xs bg-slate-100 dark:bg-slate-800 p-2 rounded overflow-x-auto">
                                                            {JSON.stringify(step.outputData, null, 2)}
                                                        </pre>
                                                    </div>
                                                )}

                                                <div className="flex items-center gap-4 text-xs text-slate-500 dark:text-slate-400">
                                                    {step.startTime && <span>Started: {formatDateTime(step.startTime)}</span>}
                                                    {step.endTime && <span>Ended: {formatDateTime(step.endTime)}</span>}
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </ScrollArea>
                    </TabsContent>

                    <TabsContent value="data">
                        <ScrollArea className="h-96">
                            <div className="space-y-4">
                                {execution.metadata?.triggerData && (
                                    <div>
                                        <div className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                                            Trigger Data
                                        </div>
                                        <pre className="text-xs bg-slate-100 dark:bg-slate-800 p-3 rounded overflow-x-auto">
                                            {JSON.stringify(execution.metadata.triggerData, null, 2)}
                                        </pre>
                                    </div>
                                )}

                                <div>
                                    <div className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                                        Full Execution Data
                                    </div>
                                    <pre className="text-xs bg-slate-100 dark:bg-slate-800 p-3 rounded overflow-x-auto">
                                        {JSON.stringify(execution, null, 2)}
                                    </pre>
                                </div>
                            </div>
                        </ScrollArea>
                    </TabsContent>
                </Tabs>
            </CardContent>
        </Card>
    );
}
