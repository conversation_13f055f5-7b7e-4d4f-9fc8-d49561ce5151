import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { useTheme } from '@/components/ThemeProvider';

interface LineChartCardProps {
    title: string;
    data: any[];
    dataKey: string;
    lineKey: string;
}

export default function LineChartCard({ title, data, dataKey, lineKey }: LineChartCardProps) {
    const { theme } = useTheme();
    const isDark = theme === 'dark';

    return (
        <Card className="col-span-4">
            <CardHeader>
                <CardTitle>{title}</CardTitle>
            </CardHeader>
            <CardContent className="pl-2">
                <ResponsiveContainer width="100%" height={350}>
                    <LineChart data={data}>
                        <CartesianGrid
                            strokeDasharray="3 3"
                            stroke={isDark ? 'hsl(217.2 32.6% 17.5%)' : 'hsl(214.3 31.8% 91.4%)'}
                        />
                        <XAxis
                            dataKey={dataKey}
                            tick={{ fill: isDark ? 'hsl(215 20.2% 65.1%)' : 'hsl(215.4 16.3% 46.9%)' }}
                            axisLine={{ stroke: isDark ? 'hsl(217.2 32.6% 17.5%)' : 'hsl(214.3 31.8% 91.4%)' }}
                        />
                        <YAxis
                            tick={{ fill: isDark ? 'hsl(215 20.2% 65.1%)' : 'hsl(215.4 16.3% 46.9%)' }}
                            axisLine={{ stroke: isDark ? 'hsl(217.2 32.6% 17.5%)' : 'hsl(214.3 31.8% 91.4%)' }}
                        />
                        <Tooltip
                            contentStyle={{
                                backgroundColor: isDark ? 'hsl(222.2 84% 4.9%)' : 'hsl(0 0% 100%)',
                                border: `1px solid ${isDark ? 'hsl(217.2 32.6% 17.5%)' : 'hsl(214.3 31.8% 91.4%)'}`,
                                borderRadius: '8px',
                                color: isDark ? 'hsl(210 40% 98%)' : 'hsl(222.2 84% 4.9%)'
                            }}
                        />
                        <Line type="monotone" dataKey={lineKey} stroke="hsl(var(--primary))" activeDot={{ r: 8 }} />
                    </LineChart>
                </ResponsiveContainer>
            </CardContent>
        </Card>
    );
}
