import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Activity, Clock, CheckCircle2, XCircle, Zap, RefreshCw } from 'lucide-react';
import axios from 'axios';

interface RunningExecution {
    id: string;
    workflowId: string;
    workflowName: string;
    instanceId: number;
    instanceName: string;
    startedAt: string;
    status: 'running' | 'waiting' | 'success' | 'failed';
    mode: 'manual' | 'trigger' | 'webhook';
    duration?: number;
}

interface LiveExecutionTrackerProps {
    refreshInterval?: number; // in milliseconds, default 5000 (5 seconds)
    maxExecutions?: number; // maximum number of executions to show, default 10
}

export default function LiveExecutionTracker({ 
    refreshInterval = 5000, 
    maxExecutions = 10 
}: LiveExecutionTrackerProps) {
    const [executions, setExecutions] = useState<RunningExecution[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

    const fetchRunningExecutions = async () => {
        try {
            const response = await axios.get('/api/executions/running');
            setExecutions(response.data.slice(0, maxExecutions));
            setError(null);
            setLastUpdate(new Date());
        } catch (err) {
            console.error('Failed to fetch running executions:', err);
            setError('Failed to load running executions');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        // Initial fetch
        fetchRunningExecutions();

        // Set up polling
        const interval = setInterval(fetchRunningExecutions, refreshInterval);

        return () => clearInterval(interval);
    }, [refreshInterval, maxExecutions]);

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'running':
                return <Zap className="w-4 h-4 text-blue-500 animate-pulse" />;
            case 'waiting':
                return <Clock className="w-4 h-4 text-amber-500" />;
            case 'success':
                return <CheckCircle2 className="w-4 h-4 text-green-500" />;
            case 'failed':
                return <XCircle className="w-4 h-4 text-red-500" />;
            default:
                return <Activity className="w-4 h-4 text-gray-500" />;
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'running':
                return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
            case 'waiting':
                return 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300';
            case 'success':
                return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
            case 'failed':
                return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
            default:
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
        }
    };

    const formatDuration = (startedAt: string) => {
        const start = new Date(startedAt);
        const now = new Date();
        const diffMs = now.getTime() - start.getTime();
        const diffSeconds = Math.floor(diffMs / 1000);
        const diffMinutes = Math.floor(diffSeconds / 60);
        const diffHours = Math.floor(diffMinutes / 60);

        if (diffHours > 0) {
            return `${diffHours}h ${diffMinutes % 60}m`;
        } else if (diffMinutes > 0) {
            return `${diffMinutes}m ${diffSeconds % 60}s`;
        } else {
            return `${diffSeconds}s`;
        }
    };

    const formatTime = (dateString: string) => {
        return new Date(dateString).toLocaleTimeString();
    };

    if (loading) {
        return (
            <Card className="glass-card">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <RefreshCw className="w-5 h-5 animate-spin" />
                        Live Executions
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="flex items-center justify-center py-8">
                        <div className="text-sm text-slate-500 dark:text-slate-400">
                            Loading running executions...
                        </div>
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card className="glass-card">
            <CardHeader>
                <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                        <Activity className="w-5 h-5" />
                        Live Executions
                        {executions.length > 0 && (
                            <Badge variant="secondary" className="ml-2">
                                {executions.length}
                            </Badge>
                        )}
                    </CardTitle>
                    {lastUpdate && (
                        <div className="text-xs text-slate-500 dark:text-slate-400">
                            Updated {lastUpdate.toLocaleTimeString()}
                        </div>
                    )}
                </div>
            </CardHeader>
            <CardContent>
                {error ? (
                    <div className="flex items-center justify-center py-8">
                        <div className="text-sm text-red-500">{error}</div>
                    </div>
                ) : executions.length === 0 ? (
                    <div className="flex items-center justify-center py-8">
                        <div className="text-center">
                            <CheckCircle2 className="w-8 h-8 text-green-500 mx-auto mb-2" />
                            <div className="text-sm text-slate-500 dark:text-slate-400">
                                No running executions
                            </div>
                        </div>
                    </div>
                ) : (
                    <div className="space-y-3">
                        {executions.map((execution) => (
                            <div
                                key={execution.id}
                                className="flex items-center justify-between p-3 rounded-lg bg-slate-50 dark:bg-slate-800/50 border border-slate-200 dark:border-slate-700"
                            >
                                <div className="flex items-center gap-3 flex-1">
                                    {getStatusIcon(execution.status)}
                                    <div className="flex-1 min-w-0">
                                        <div className="font-medium text-sm truncate">
                                            {execution.workflowName}
                                        </div>
                                        <div className="text-xs text-slate-500 dark:text-slate-400">
                                            {execution.instanceName} • Started {formatTime(execution.startedAt)}
                                        </div>
                                    </div>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Badge 
                                        variant="outline" 
                                        className={`text-xs ${getStatusColor(execution.status)}`}
                                    >
                                        {execution.status}
                                    </Badge>
                                    <div className="text-xs text-slate-500 dark:text-slate-400 min-w-0">
                                        {formatDuration(execution.startedAt)}
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </CardContent>
        </Card>
    );
}
