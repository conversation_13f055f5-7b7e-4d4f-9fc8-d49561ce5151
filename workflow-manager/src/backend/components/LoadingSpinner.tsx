import React from 'react';

interface LoadingSpinnerProps {
    size?: 'sm' | 'md' | 'lg';
    className?: string;
}

export default function LoadingSpinner({ size = 'md', className = '' }: LoadingSpinnerProps) {
    const sizeClasses = {
        sm: 'w-4 h-4',
        md: 'w-8 h-8',
        lg: 'w-12 h-12'
    };

    return (
        <div className={`${sizeClasses[size]} ${className}`}>
            <div className="relative">
                {/* Outer ring */}
                <div className="absolute inset-0 rounded-full border-4 border-slate-200"></div>
                
                {/* Spinning gradient ring */}
                <div className="absolute inset-0 rounded-full border-4 border-transparent border-t-blue-500 border-r-teal-500 animate-spin"></div>

                {/* Inner glow */}
                <div className="absolute inset-2 rounded-full bg-gradient-to-br from-blue-500/20 to-teal-600/20 animate-pulse"></div>
            </div>
        </div>
    );
}

export function LoadingCard() {
    return (
        <div className="glass-card p-8 rounded-2xl">
            <div className="flex flex-col items-center justify-center space-y-4">
                <LoadingSpinner size="lg" />
                <div className="text-center">
                    <h3 className="text-lg font-semibold bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
                        Loading Dashboard
                    </h3>
                    <p className="text-sm text-slate-500 mt-1">
                        Fetching your workflow analytics...
                    </p>
                </div>
            </div>
        </div>
    );
}

export function LoadingPage() {
    return (
        <div className="min-h-screen flex items-center justify-center p-8">
            <LoadingCard />
        </div>
    );
}
