import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';
import UseTemplateSheet from '@/components/UseTemplateSheet';
import { Template } from '@/types';

interface Props {
    template: Template;
    size?: 'sm' | 'default' | 'lg';
    className?: string;
}

export default function UseTemplateButton({ template, size = 'sm', className }: Props) {
    const [templateData, setTemplateData] = useState<{ template: Template; instances: any[] } | null>(null);
    const [loading, setLoading] = useState(false);

    const handleClick = async () => {
        if (templateData) return; // Already loaded

        setLoading(true);
        try {
            const response = await fetch(route('templates.use-data', template.id));
            const data = await response.json();
            setTemplateData(data);
        } catch (error) {
            console.error('Failed to fetch template data:', error);
        } finally {
            setLoading(false);
        }
    };

    if (templateData) {
        return (
            <UseTemplateSheet
                template={templateData.template}
                instances={templateData.instances}
            >
                <Button
                    size={size}
                    className={className || "bg-gradient-to-r from-blue-600 to-teal-600 hover:from-blue-700 hover:to-teal-700"}
                >
                    Use Template
                    <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
            </UseTemplateSheet>
        );
    }

    return (
        <Button
            size={size}
            className={className || "bg-gradient-to-r from-blue-600 to-teal-600 hover:from-blue-700 hover:to-teal-700"}
            onClick={handleClick}
            disabled={loading}
        >
            {loading ? 'Loading...' : 'Use Template'}
            <ArrowRight className="w-4 h-4 ml-2" />
        </Button>
    );
}
