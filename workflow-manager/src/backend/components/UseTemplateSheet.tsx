import React, { useState } from 'react';
import { useForm, router } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { ArrowRight, FileText, Server, Zap, AlertCircle, Settings, Plus } from 'lucide-react';
import { Template, N8nInstance } from '@/types';

interface Props {
    template: Template;
    instances: N8nInstance[];
    children: React.ReactNode;
}

interface FormData {
    instance_id: string;
    workflow_name: string;
    is_active: boolean;
}

export default function UseTemplateSheet({ template, instances, children }: Props) {
    const [open, setOpen] = useState(false);
    const [selectedInstance, setSelectedInstance] = useState<N8nInstance | null>(null);

    const { data, setData, processing, errors, reset } = useForm<FormData>({
        instance_id: '',
        workflow_name: template.name + ' (from template)',
        is_active: false,
    });

    const handleInstanceSelect = (instanceId: string) => {
        setData('instance_id', instanceId);
        const instance = instances.find(i => i.id.toString() === instanceId);
        setSelectedInstance(instance || null);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (!data.instance_id) return;

        // Use the template use route with the selected instance and configuration
        router.post(route('templates.use', template.id), {
            instance_id: data.instance_id,
            workflow_name: data.workflow_name,
            is_active: data.is_active,
        }, {
            onSuccess: () => {
                setOpen(false);
                reset();
            },
        });
    };

    const handleOpenChange = (newOpen: boolean) => {
        setOpen(newOpen);
        if (!newOpen) {
            reset();
            setSelectedInstance(null);
        }
    };

    return (
        <Sheet open={open} onOpenChange={handleOpenChange}>
            <SheetTrigger asChild>
                {children}
            </SheetTrigger>
            <SheetContent className="sm:max-w-[600px]">
                <SheetHeader>
                    <SheetTitle className="flex items-center gap-2">
                        <FileText className="w-5 h-5" />
                        Use Template: {template.name}
                    </SheetTitle>
                    <SheetDescription>
                        Configure and create a new workflow from this template
                    </SheetDescription>
                </SheetHeader>

                <div className="mt-6 space-y-6">
                    {/* Template Info */}
                    <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                        <div className="flex items-start gap-3">
                            <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
                            <div className="flex-1">
                                <h4 className="font-medium text-blue-900 dark:text-blue-100">
                                    {template.name}
                                </h4>
                                <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                                    {template.description}
                                </p>
                                {template.tags && template.tags.length > 0 && (
                                    <div className="flex flex-wrap gap-1 mt-2">
                                        {template.tags.map((tag, index) => (
                                            <Badge key={index} variant="secondary" className="text-xs">
                                                {tag}
                                            </Badge>
                                        ))}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Configuration Form */}
                    <form onSubmit={handleSubmit} className="space-y-4">
                        {/* Workflow Name */}
                        <div className="space-y-2">
                            <Label htmlFor="workflow_name">Workflow Name</Label>
                            <Input
                                id="workflow_name"
                                value={data.workflow_name}
                                onChange={(e) => setData('workflow_name', e.target.value)}
                                placeholder="Enter workflow name"
                                required
                            />
                            {errors.workflow_name && (
                                <p className="text-sm text-red-600 dark:text-red-400">
                                    {errors.workflow_name}
                                </p>
                            )}
                        </div>

                        {/* N8N Instance Selection */}
                        <div className="space-y-2">
                            <Label>N8N Instance</Label>
                            <Select onValueChange={handleInstanceSelect} required>
                                <SelectTrigger>
                                    <SelectValue placeholder="Select an N8N instance" />
                                </SelectTrigger>
                                <SelectContent>
                                    {instances.map((instance) => (
                                        <SelectItem key={instance.id} value={instance.id.toString()}>
                                            <div className="flex items-center gap-2">
                                                <Server className="w-4 h-4" />
                                                <span>{instance.name}</span>
                                                <Badge
                                                    variant={instance.health_status === 'online' ? 'default' : 'destructive'}
                                                    className="ml-auto text-xs"
                                                >
                                                    {instance.health_status}
                                                </Badge>
                                            </div>
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            {errors.instance_id && (
                                <p className="text-sm text-red-600 dark:text-red-400">
                                    {errors.instance_id}
                                </p>
                            )}
                        </div>

                        {/* Selected Instance Info */}
                        {selectedInstance && (
                            <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg">
                                <div className="flex items-center gap-2">
                                    <Zap className="w-4 h-4 text-green-600 dark:text-green-400" />
                                    <div className="flex-1">
                                        <h4 className="font-medium text-green-900 dark:text-green-100 text-sm">
                                            {selectedInstance.name}
                                        </h4>
                                        <p className="text-xs text-green-700 dark:text-green-300">
                                            {selectedInstance.url}
                                        </p>
                                    </div>
                                    <Badge
                                        variant={selectedInstance.health_status === 'online' ? 'default' : 'destructive'}
                                        className="text-xs"
                                    >
                                        {selectedInstance.health_status}
                                    </Badge>
                                </div>
                            </div>
                        )}

                        {/* Workflow Settings */}
                        <div className="space-y-2">
                            <div className="flex items-center space-x-2">
                                <input
                                    type="checkbox"
                                    id="is_active"
                                    checked={data.is_active}
                                    onChange={(e) => setData('is_active', e.target.checked)}
                                    className="rounded border-gray-300"
                                />
                                <Label htmlFor="is_active" className="text-sm">
                                    Activate workflow immediately
                                </Label>
                            </div>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                                If unchecked, the workflow will be created but remain inactive until you manually activate it.
                            </p>
                        </div>

                        {/* Info Box */}
                        <div className="bg-amber-50 dark:bg-amber-900/20 p-3 rounded-lg">
                            <div className="flex items-start gap-2">
                                <AlertCircle className="w-4 h-4 text-amber-600 dark:text-amber-400 mt-0.5" />
                                <div>
                                    <h4 className="font-medium text-amber-900 dark:text-amber-100 text-sm">
                                        What happens next?
                                    </h4>
                                    <p className="text-xs text-amber-700 dark:text-amber-300 mt-1">
                                        A new workflow will be created in your selected N8N instance. You can then customize, test, and manage it from the Workflows page.
                                    </p>
                                </div>
                            </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex items-center justify-between pt-4">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => setOpen(false)}
                            >
                                Cancel
                            </Button>
                            <Button
                                type="submit"
                                disabled={processing || !data.instance_id}
                                className="bg-gradient-to-r from-blue-600 to-teal-600 hover:from-blue-700 hover:to-teal-700"
                            >
                                {processing ? 'Creating...' : 'Create Workflow'}
                                <ArrowRight className="w-4 h-4 ml-2" />
                            </Button>
                        </div>
                    </form>
                </div>
            </SheetContent>
        </Sheet>
    );
}
