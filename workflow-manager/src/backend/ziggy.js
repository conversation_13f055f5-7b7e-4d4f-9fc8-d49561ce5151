const Ziggy = {"url":"http:\/\/localhost","port":null,"defaults":{},"routes":{"sanctum.csrf-cookie":{"uri":"sanctum\/csrf-cookie","methods":["GET","HEAD"]},"login":{"uri":"login","methods":["GET","HEAD"]},"register":{"uri":"register","methods":["GET","HEAD"]},"logout":{"uri":"logout","methods":["POST"]},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"instances.index":{"uri":"instances","methods":["GET","HEAD"]},"instances.create":{"uri":"instances\/create","methods":["GET","HEAD"]},"instances.store":{"uri":"instances","methods":["POST"]},"instances.show":{"uri":"instances\/{n8nInstance}","methods":["GET","HEAD"],"parameters":["n8nInstance"],"bindings":{"n8nInstance":"id"}},"instances.edit":{"uri":"instances\/{n8nInstance}\/edit","methods":["GET","HEAD"],"parameters":["n8nInstance"],"bindings":{"n8nInstance":"id"}},"instances.update":{"uri":"instances\/{n8nInstance}","methods":["PUT"],"parameters":["n8nInstance"],"bindings":{"n8nInstance":"id"}},"instances.destroy":{"uri":"instances\/{n8nInstance}","methods":["DELETE"],"parameters":["n8nInstance"],"bindings":{"n8nInstance":"id"}},"instances.check-health":{"uri":"instances\/{n8nInstance}\/check-health","methods":["POST"],"parameters":["n8nInstance"],"bindings":{"n8nInstance":"id"}},"instances.analytics":{"uri":"instances\/{n8nInstance}\/analytics","methods":["GET","HEAD"],"parameters":["n8nInstance"]},"workflows.index":{"uri":"workflows","methods":["GET","HEAD"]},"workflows.create":{"uri":"workflows\/create","methods":["GET","HEAD"]},"workflows.store":{"uri":"workflows","methods":["POST"]},"workflows.show":{"uri":"workflows\/{workflow}","methods":["GET","HEAD"],"parameters":["workflow"],"bindings":{"workflow":"id"}},"workflows.edit":{"uri":"workflows\/{workflow}\/edit","methods":["GET","HEAD"],"parameters":["workflow"],"bindings":{"workflow":"id"}},"workflows.update":{"uri":"workflows\/{workflow}","methods":["PUT"],"parameters":["workflow"],"bindings":{"workflow":"id"}},"workflows.destroy":{"uri":"workflows\/{workflow}","methods":["DELETE"],"parameters":["workflow"],"bindings":{"workflow":"id"}},"workflows.sync":{"uri":"workflows\/sync","methods":["POST"]},"workflows.executions":{"uri":"workflows\/{workflow}\/executions","methods":["GET","HEAD"],"parameters":["workflow"]},"workflows.analytics":{"uri":"workflows\/{workflow}\/analytics","methods":["GET","HEAD"],"parameters":["workflow"]},"ai.workflows.index":{"uri":"ai\/workflows","methods":["GET","HEAD"]},"ai.templates.category":{"uri":"ai\/templates\/category\/{category}","methods":["GET","HEAD"],"parameters":["category"],"bindings":{"category":"id"}},"ai.templates.show":{"uri":"ai\/templates\/{template}","methods":["GET","HEAD"],"parameters":["template"],"bindings":{"template":"id"}},"ai.generate":{"uri":"ai\/generate","methods":["POST"]},"ai.history.status":{"uri":"ai\/history\/{history}\/status","methods":["GET","HEAD"],"parameters":["history"],"bindings":{"history":"id"}},"ai.history.deploy":{"uri":"ai\/history\/{history}\/deploy","methods":["POST"],"parameters":["history"],"bindings":{"history":"id"}},"ai.history":{"uri":"ai\/history","methods":["GET","HEAD"]},"ai.history.browse":{"uri":"ai\/history\/browse","methods":["GET","HEAD"]},"ai.history.rate":{"uri":"ai\/history\/{history}\/rate","methods":["POST"],"parameters":["history"],"bindings":{"history":"id"}},"templates.index":{"uri":"templates","methods":["GET","HEAD"]},"templates.create":{"uri":"templates\/create","methods":["GET","HEAD"]},"templates.store":{"uri":"templates","methods":["POST"]},"templates.show":{"uri":"templates\/{template}","methods":["GET","HEAD"],"parameters":["template"],"bindings":{"template":"id"}},"templates.edit":{"uri":"templates\/{template}\/edit","methods":["GET","HEAD"],"parameters":["template"],"bindings":{"template":"id"}},"templates.update":{"uri":"templates\/{template}","methods":["PUT"],"parameters":["template"],"bindings":{"template":"id"}},"templates.destroy":{"uri":"templates\/{template}","methods":["DELETE"],"parameters":["template"],"bindings":{"template":"id"}},"alert-rules.index":{"uri":"alerts\/rules","methods":["GET","HEAD"]},"alert-rules.create":{"uri":"alerts\/rules\/create","methods":["GET","HEAD"]},"alert-rules.store":{"uri":"alerts\/rules","methods":["POST"]},"alert-rules.show":{"uri":"alerts\/rules\/{alertRule}","methods":["GET","HEAD"],"parameters":["alertRule"],"bindings":{"alertRule":"id"}},"alert-rules.edit":{"uri":"alerts\/rules\/{alertRule}\/edit","methods":["GET","HEAD"],"parameters":["alertRule"],"bindings":{"alertRule":"id"}},"alert-rules.update":{"uri":"alerts\/rules\/{alertRule}","methods":["PUT"],"parameters":["alertRule"],"bindings":{"alertRule":"id"}},"alert-rules.destroy":{"uri":"alerts\/rules\/{alertRule}","methods":["DELETE"],"parameters":["alertRule"],"bindings":{"alertRule":"id"}},"alert-rules.toggle":{"uri":"alerts\/rules\/{alertRule}\/toggle","methods":["POST"],"parameters":["alertRule"],"bindings":{"alertRule":"id"}},"alert-rules.test":{"uri":"alerts\/rules\/{alertRule}\/test","methods":["POST"],"parameters":["alertRule"],"bindings":{"alertRule":"id"}},"alert-channels.index":{"uri":"alerts\/channels","methods":["GET","HEAD"]},"alert-channels.create":{"uri":"alerts\/channels\/create","methods":["GET","HEAD"]},"alert-channels.store":{"uri":"alerts\/channels","methods":["POST"]},"alert-channels.show":{"uri":"alerts\/channels\/{alertChannel}","methods":["GET","HEAD"],"parameters":["alertChannel"],"bindings":{"alertChannel":"id"}},"alert-channels.edit":{"uri":"alerts\/channels\/{alertChannel}\/edit","methods":["GET","HEAD"],"parameters":["alertChannel"],"bindings":{"alertChannel":"id"}},"alert-channels.update":{"uri":"alerts\/channels\/{alertChannel}","methods":["PUT"],"parameters":["alertChannel"],"bindings":{"alertChannel":"id"}},"alert-channels.destroy":{"uri":"alerts\/channels\/{alertChannel}","methods":["DELETE"],"parameters":["alertChannel"],"bindings":{"alertChannel":"id"}},"alert-channels.test":{"uri":"alerts\/channels\/{alertChannel}\/test","methods":["POST"],"parameters":["alertChannel"],"bindings":{"alertChannel":"id"}},"alert-channels.toggle":{"uri":"alerts\/channels\/{alertChannel}\/toggle","methods":["POST"],"parameters":["alertChannel"],"bindings":{"alertChannel":"id"}},"alert-notifications.index":{"uri":"alerts\/notifications","methods":["GET","HEAD"]},"alert-notifications.show":{"uri":"alerts\/notifications\/{alertNotification}","methods":["GET","HEAD"],"parameters":["alertNotification"],"bindings":{"alertNotification":"id"}},"alert-notifications.acknowledge":{"uri":"alerts\/notifications\/{alertNotification}\/acknowledge","methods":["POST"],"parameters":["alertNotification"],"bindings":{"alertNotification":"id"}},"alert-notifications.bulk-acknowledge":{"uri":"alerts\/notifications\/bulk-acknowledge","methods":["POST"]},"alert-notifications.destroy":{"uri":"alerts\/notifications\/{alertNotification}","methods":["DELETE"],"parameters":["alertNotification"],"bindings":{"alertNotification":"id"}},"alert-notifications.bulk-destroy":{"uri":"alerts\/notifications\/bulk-destroy","methods":["POST"]},"users.index":{"uri":"users","methods":["GET","HEAD"]},"users.create":{"uri":"users\/create","methods":["GET","HEAD"]},"users.store":{"uri":"users","methods":["POST"]},"users.show":{"uri":"users\/{user}","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"id"}},"users.edit":{"uri":"users\/{user}\/edit","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"id"}},"users.update":{"uri":"users\/{user}","methods":["PUT","PATCH"],"parameters":["user"],"bindings":{"user":"id"}},"users.destroy":{"uri":"users\/{user}","methods":["DELETE"],"parameters":["user"],"bindings":{"user":"id"}},"users.instance-access":{"uri":"users\/{user}\/instance-access","methods":["POST"],"parameters":["user"],"bindings":{"user":"id"}},"admin.ai-providers.index":{"uri":"admin\/ai-providers","methods":["GET","HEAD"]},"admin.ai-providers.update":{"uri":"admin\/ai-providers\/{provider}","methods":["PUT"],"parameters":["provider"],"bindings":{"provider":"id"}},"admin.ai-providers.test":{"uri":"admin\/ai-providers\/{provider}\/test","methods":["POST"],"parameters":["provider"],"bindings":{"provider":"id"}},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
