import { Card, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";

interface MetricCardProps {
    title: string;
    value: string | number;
    description?: string;
    icon?: React.ReactNode;
    trend?: {
        value: number;
        is_positive: boolean;
    };
    gradient?: string;
    className?: string;
}

export default function MetricCard({
    title,
    value,
    description,
    icon,
    trend,
    gradient = "from-blue-500 to-teal-600",
    className
}: MetricCardProps) {
    return (
        <Card className={`relative overflow-hidden bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-slate-200/60 dark:border-slate-700/60 shadow-lg hover:shadow-xl transition-all duration-300 group ${className || ''}`}>
            {/* Gradient Background */}
            <div className={`absolute inset-0 bg-gradient-to-br ${gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-300`} />

            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-semibold text-slate-600 dark:text-slate-300 uppercase tracking-wide">
                    {title}
                </CardTitle>
                <div className={`p-2 rounded-xl bg-gradient-to-br ${gradient} shadow-lg`}>
                    <div className="text-white">
                        {icon}
                    </div>
                </div>
            </CardHeader>
            <CardContent className="space-y-2">
                <div className="flex items-baseline space-x-2">
                    <div className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-slate-100 dark:to-slate-300 bg-clip-text text-transparent">
                        {value}
                    </div>
                    {trend && (
                        <div className={`flex items-center text-sm font-medium ${
                            trend.is_positive
                                ? 'text-emerald-600'
                                : 'text-red-600'
                        }`}>
                            <span className="mr-1">
                                {trend.is_positive ? '↗' : '↘'}
                            </span>
                            {Math.abs(trend.value)}%
                        </div>
                    )}
                </div>
                {description && (
                    <p className="text-sm text-slate-500 dark:text-slate-400 leading-relaxed">
                        {description}
                    </p>
                )}
            </CardContent>
        </Card>
    );
}
