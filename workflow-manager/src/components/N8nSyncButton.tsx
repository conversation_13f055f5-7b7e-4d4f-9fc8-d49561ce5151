import { useState } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { RefreshCw, Download, AlertCircle, CheckCircle } from 'lucide-react';

interface N8nSyncButtonProps {
  instanceId: string;
  onSyncComplete?: () => void;
  type: 'workflows' | 'executions';
  className?: string;
}

interface SyncResult {
  success: boolean;
  count: number;
  error?: string;
}

export const N8nSyncButton: React.FC<N8nSyncButtonProps> = ({
  instanceId,
  onSyncComplete,
  type,
  className = ''
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [lastSync, setLastSync] = useState<SyncResult | null>(null);

  const handleSync = async () => {
    setIsLoading(true);
    setLastSync(null);

    try {
      let result;
      if (type === 'workflows') {
        result = await invoke('sync_workflows_from_n8n', {
          instance_id: instanceId
        });
      } else {
        result = await invoke('sync_executions_from_n8n', {
          instance_id: instanceId,
          workflow_id: null,
          limit: 100
        });
      }

      const count = Array.isArray(result) ? result.length : 0;
      setLastSync({ success: true, count });
      
      if (onSyncComplete) {
        onSyncComplete();
      }
    } catch (error) {
      console.error(`Failed to sync ${type}:`, error);
      setLastSync({ 
        success: false, 
        count: 0, 
        error: error instanceof Error ? error.message : String(error)
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getButtonText = () => {
    if (isLoading) {
      return `Syncing ${type}...`;
    }
    return `Sync ${type}`;
  };

  const getStatusIcon = () => {
    if (isLoading) {
      return <RefreshCw className="w-4 h-4 animate-spin" />;
    }
    if (lastSync?.success) {
      return <CheckCircle className="w-4 h-4 text-green-500" />;
    }
    if (lastSync?.success === false) {
      return <AlertCircle className="w-4 h-4 text-red-500" />;
    }
    return <Download className="w-4 h-4" />;
  };

  const getStatusMessage = () => {
    if (!lastSync) return null;
    
    if (lastSync.success) {
      return (
        <span className="text-sm text-green-600">
          Synced {lastSync.count} {type}
        </span>
      );
    } else {
      return (
        <span className="text-sm text-red-600">
          Sync failed: {lastSync.error}
        </span>
      );
    }
  };

  return (
    <div className="flex flex-col gap-2">
      <button
        onClick={handleSync}
        disabled={isLoading}
        className={`
          flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all
          ${isLoading 
            ? 'bg-gray-100 text-gray-500 cursor-not-allowed' 
            : 'bg-gradient-to-r from-blue-500 to-teal-600 text-white hover:from-blue-600 hover:to-teal-700 active:scale-95'
          }
          ${className}
        `}
      >
        {getStatusIcon()}
        {getButtonText()}
      </button>
      
      {getStatusMessage()}
    </div>
  );
};

export default N8nSyncButton;
