import { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';
import { Server, Workflow, Activity, Clock, AlertTriangle, CheckCircle2 } from 'lucide-react';


interface N8nInstance {
  id: string;
  name: string;
  url: string;
  status: string;
  is_default: boolean;
}

interface SyncStats {
  workflows: number;
  executions: number;
  lastSync: string | null;
}

interface AppSettings {
  auto_sync: boolean;
  sync_interval: number;
  desktop_notifications: boolean;
}

export const N8nSyncDashboard: React.FC = () => {
  const [instances, setInstances] = useState<N8nInstance[]>([]);
  const [syncStats, setSyncStats] = useState<Record<string, SyncStats>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [syncingInstances, setSyncingInstances] = useState<Set<string>>(new Set());
  const [settings, setSettings] = useState<AppSettings | null>(null);

  useEffect(() => {
    loadInstances();
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const appSettings = await invoke<AppSettings>('get_app_settings');
      setSettings(appSettings);
    } catch (err) {
      console.error('Failed to load settings:', err);
    }
  };

  const loadInstances = async () => {
    try {
      setLoading(true);
      const data = await invoke<N8nInstance[]>('list_n8n_instances', {
        limit: null,
        offset: null
      });
      setInstances(data);
      
      // Load sync stats for each instance
      const stats: Record<string, SyncStats> = {};
      for (const instance of data) {
        stats[instance.id] = await loadSyncStats(instance.id);
      }
      setSyncStats(stats);
    } catch (err) {
      console.error('Failed to load N8N instances:', err);
      setError(err instanceof Error ? err.message : String(err));
    } finally {
      setLoading(false);
    }
  };

  const loadSyncStats = async (instanceId: string): Promise<SyncStats> => {
    try {
      // Get workflow count
      const workflows = await invoke<any[]>('list_workflows', {
        instance_id: instanceId,
        limit: null,
        offset: null
      });

      // Get execution count
      const executions = await invoke<any[]>('list_executions', {
        instance_id: instanceId,
        limit: null,
        offset: null
      });

      return {
        workflows: workflows.length,
        executions: executions.length,
        lastSync: null // TODO: Track last sync time
      };
    } catch (error) {
      console.error(`Failed to load sync stats for instance ${instanceId}:`, error);
      return {
        workflows: 0,
        executions: 0,
        lastSync: null
      };
    }
  };

  // Check if sync is currently happening for an instance
  const isInstanceSyncing = (instanceId: string): boolean => {
    return syncingInstances.has(instanceId);
  };

  // Get real sync status from backend
  useEffect(() => {
    const fetchSyncStatus = async () => {
      try {
        const syncingIds = await invoke<string[]>('get_syncing_instances');
        setSyncingInstances(new Set(syncingIds));
      } catch (err) {
        console.error('Failed to get syncing instances:', err);
      }
    };

    // Fetch sync status immediately and then every 2 seconds
    fetchSyncStatus();
    const interval = setInterval(fetchSyncStatus, 2000);

    // Listen for sync completed events to refresh data immediately
    const setupSyncListener = async () => {
      const unlisten = await listen('sync_completed', (event) => {
        console.log('Sync completed event received in N8nSyncDashboard:', event.payload);
        // Refresh instances and sync stats when sync completes
        loadInstances();
      });
      return unlisten;
    };

    let unlistenPromise = setupSyncListener();

    return () => {
      clearInterval(interval);
      unlistenPromise.then(unlisten => unlisten());
    };
  }, []);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
      case 'connected':
        return 'text-green-500';
      case 'inactive':
      case 'disconnected':
        return 'text-red-500';
      default:
        return 'text-yellow-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
      case 'connected':
        return <CheckCircle2 className="w-4 h-4" />;
      case 'inactive':
      case 'disconnected':
        return <AlertTriangle className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-2 text-gray-600">Loading N8N instances...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center gap-2 text-red-700">
          <AlertTriangle className="w-5 h-5" />
          <span className="font-medium">Error loading N8N instances</span>
        </div>
        <p className="text-red-600 mt-1">{error}</p>
        <button
          onClick={loadInstances}
          className="mt-3 px-4 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  if (instances.length === 0) {
    return (
      <div className="text-center p-8">
        <Server className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No N8N Instances</h3>
        <p className="text-gray-600 mb-4">
          Configure N8N instances to start syncing workflows and executions.
        </p>
        <button
          onClick={() => window.location.href = '/instances'}
          className="px-4 py-2 bg-gradient-to-r from-blue-500 to-teal-600 text-white rounded-lg hover:from-blue-600 hover:to-teal-700 transition-all"
        >
          Add N8N Instance
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">N8N Data Sync</h2>
        <button
          onClick={loadInstances}
          className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
        >
          Refresh
        </button>
      </div>

      <div className="grid gap-6">
        {instances.map((instance) => {
          const stats = syncStats[instance.id] || { workflows: 0, executions: 0, lastSync: null };
          
          return (
            <div key={instance.id} className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Server className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{instance.name}</h3>
                    <p className="text-sm text-gray-600">{instance.url}</p>
                    {instance.is_default && (
                      <span className="inline-block mt-1 px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
                        Default
                      </span>
                    )}
                  </div>
                </div>
                
                <div className={`flex items-center gap-1 ${getStatusColor(instance.status)}`}>
                  {getStatusIcon(instance.status)}
                  <span className="text-sm font-medium capitalize">{instance.status}</span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Workflow className="w-4 h-4 text-gray-600" />
                    <span className="text-sm font-medium text-gray-700">Workflows</span>
                  </div>
                  <p className="text-2xl font-bold text-gray-900">{stats.workflows}</p>
                </div>
                
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Activity className="w-4 h-4 text-gray-600" />
                    <span className="text-sm font-medium text-gray-700">Executions</span>
                  </div>
                  <p className="text-2xl font-bold text-gray-900">{stats.executions}</p>
                </div>
              </div>

              {/* Show sync status only when actually syncing */}
              {isInstanceSyncing(instance.id) ? (
                <div className="flex items-center gap-2 text-sm text-blue-600">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                  Syncing
                </div>
              ) : settings?.auto_sync ? (
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                  Sync enabled
                </div>
              ) : (
                <div className="flex items-center gap-2 text-sm text-gray-400">
                  <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                  Sync disabled
                </div>
              )}

              {stats.lastSync && (
                <p className="text-xs text-gray-500 mt-3">
                  Last synced: {new Date(stats.lastSync).toLocaleString()}
                </p>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default N8nSyncDashboard;
