import { useState } from 'react';
import { Link, Outlet, useLocation } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useTheme } from '@/components/ThemeProvider';
import Breadcrumb from '@/components/Breadcrumb';
import {
    Menu,
    LayoutDashboard,
    Server,
    Workflow,
    Settings,
    ChevronLeft,
    ChevronRight,
    ChevronDown,
    ChevronUp,
    Zap,
    Moon,
    Sun,
    Sparkles,
    Rocket
} from 'lucide-react';

export default function AppLayout() {
    const [isSidebarOpen, setIsSidebarOpen] = useState(true);
    const { theme, setTheme } = useTheme();
    const location = useLocation();

    // Get page title based on current route
    const getPageTitle = () => {
        const path = location.pathname;
        if (path === '/') return 'Dashboard';
        if (path === '/workflows') return 'Workflows';
        if (path === '/workflows/create') return 'Create Workflow';
        if (path.startsWith('/workflows/') && path.endsWith('/edit')) return 'Edit Workflow';
        if (path.startsWith('/workflows/')) return 'Workflow Details';
        if (path === '/instances') return 'N8N Instances';
        if (path.includes('/instances/') && path.endsWith('/deployments')) return 'Instance Deployments';
        if (path === '/settings') return 'Settings';
        return 'Workflow Manager';
    };

    const title = getPageTitle();
    const breadcrumbs: any[] = [];

    const toggleSidebar = () => {
        setIsSidebarOpen(!isSidebarOpen);
    };

    const toggleDarkMode = () => {
        setTheme(theme === 'dark' ? 'light' : 'dark');
    };

    const [expandedGroups, setExpandedGroups] = useState<string[]>(['ai']);

    const toggleGroup = (groupKey: string) => {
        setExpandedGroups(prev =>
            prev.includes(groupKey)
                ? prev.filter(key => key !== groupKey)
                : [...prev, groupKey]
        );
    };

    const navigationItems = [
        { name: 'Dashboard', href: '/', icon: LayoutDashboard },
        { name: 'Instances', href: '/instances', icon: Server },
        { name: 'Workflows', href: '/workflows', icon: Workflow },
    ];

    // AI Group with submenus
    const aiGroup = {
        name: 'AI Automation',
        key: 'ai',
        icon: Sparkles,
        items: [
            { name: 'Create Template', href: '/workflows/create', icon: Sparkles },
            { name: 'Deploy Template', href: '/workflows/deploy', icon: Rocket },
        ]
    };

    const adminItems = [
        { name: 'Settings', href: '/settings', icon: Settings },
    ];

    return (
        <div className="flex h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
            {/* Sidebar */}
            <aside
                className={`flex-shrink-0 bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-r border-slate-200/60 dark:border-slate-700/60 transition-all duration-300 ease-in-out shadow-xl
                ${isSidebarOpen ? 'w-72' : 'w-0 overflow-hidden md:w-20 md:overflow-visible'}`}
            >
                {/* Logo Section */}
                <div className="p-6 border-b border-slate-200/60 dark:border-slate-700/60">
                    <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-blue-500 to-teal-600 rounded-xl flex items-center justify-center">
                            <Zap className="w-6 h-6 text-white" />
                        </div>
                        {isSidebarOpen && (
                            <div>
                                <h1 className="text-xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-slate-100 dark:to-slate-300 bg-clip-text text-transparent">
                                    Workflow Manager
                                </h1>
                                <p className="text-xs text-slate-500 dark:text-slate-400">Workflow Management</p>
                            </div>
                        )}
                    </div>
                </div>

                {/* Navigation */}
                <nav className="p-4 space-y-2">
                    {/* Regular Navigation Items */}
                    {navigationItems.map((item) => {
                        const Icon = item.icon;
                        const isActive = location.pathname === item.href;

                        return (
                            <Link
                                key={item.name}
                                to={item.href}
                                className={`flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200 group
                                    ${isActive
                                        ? 'bg-gradient-to-r from-blue-500 to-teal-600 text-white shadow-lg shadow-blue-500/25'
                                        : 'text-slate-600 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800 hover:text-slate-900 dark:hover:text-slate-100'
                                    }`}
                            >
                                <Icon className={`w-5 h-5 ${isActive ? 'text-white' : 'text-slate-500 dark:text-slate-400 group-hover:text-slate-700 dark:group-hover:text-slate-200'}`} />
                                {isSidebarOpen && (
                                    <span className="font-medium">{item.name}</span>
                                )}
                            </Link>
                        );
                    })}

                    {/* AI Group */}
                    <div className="space-y-1">
                        <button
                            onClick={() => toggleGroup(aiGroup.key)}
                            className="w-full flex items-center justify-between px-4 py-3 rounded-xl transition-all duration-200 group text-slate-600 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800 hover:text-slate-900 dark:hover:text-slate-100"
                        >
                            <div className="flex items-center space-x-3">
                                <aiGroup.icon className="w-5 h-5 text-slate-500 dark:text-slate-400 group-hover:text-slate-700 dark:group-hover:text-slate-200" />
                                {isSidebarOpen && (
                                    <span className="font-medium">{aiGroup.name}</span>
                                )}
                            </div>
                            {isSidebarOpen && (
                                expandedGroups.includes(aiGroup.key) ?
                                    <ChevronUp className="w-4 h-4 text-slate-400 dark:text-slate-500" /> :
                                    <ChevronDown className="w-4 h-4 text-slate-400 dark:text-slate-500" />
                            )}
                        </button>

                        {expandedGroups.includes(aiGroup.key) && isSidebarOpen && (
                            <div className="ml-4 space-y-1">
                                {aiGroup.items.map((subItem) => {
                                    const SubIcon = subItem.icon;
                                    const isSubActive = location.pathname === subItem.href;

                                    return (
                                        <Link
                                            key={subItem.name}
                                            to={subItem.href}
                                            className={`flex items-center space-x-3 px-4 py-2 rounded-lg transition-all duration-200 group
                                                ${isSubActive
                                                    ? 'bg-gradient-to-r from-blue-500 to-teal-500 text-white shadow-lg shadow-blue-500/25'
                                                    : 'text-slate-600 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-800 hover:text-slate-900 dark:hover:text-slate-100'
                                                }`}
                                        >
                                            <SubIcon className={`w-4 h-4 ${isSubActive ? 'text-white' : 'text-slate-400 dark:text-slate-500 group-hover:text-slate-600 dark:group-hover:text-slate-300'}`} />
                                            <span className="text-sm font-medium">{subItem.name}</span>
                                        </Link>
                                    );
                                })}
                            </div>
                        )}
                    </div>

                    {/* Admin Items */}
                    {adminItems.map((item) => {
                        const Icon = item.icon;
                        const isActive = location.pathname === item.href;

                        return (
                            <Link
                                key={item.name}
                                to={item.href}
                                className={`flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200 group
                                    ${isActive
                                        ? 'bg-gradient-to-r from-blue-500 to-teal-600 text-white shadow-lg shadow-blue-500/25'
                                        : 'text-slate-600 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800 hover:text-slate-900 dark:hover:text-slate-100'
                                    }`}
                            >
                                <Icon className={`w-5 h-5 ${isActive ? 'text-white' : 'text-slate-500 dark:text-slate-400 group-hover:text-slate-700 dark:group-hover:text-slate-200'}`} />
                                {isSidebarOpen && (
                                    <span className="font-medium">{item.name}</span>
                                )}
                            </Link>
                        );
                    })}
                </nav>

                {/* Sidebar Toggle Button */}
                <div className="absolute bottom-6 left-4">
                    <Button
                        variant="ghost"
                        size="icon"
                        onClick={toggleSidebar}
                        className="w-10 h-10 rounded-xl bg-slate-100 dark:bg-slate-800 hover:bg-slate-200 dark:hover:bg-slate-700 border border-slate-200 dark:border-slate-700"
                    >
                        {isSidebarOpen ? (
                            <ChevronLeft className="w-4 h-4" />
                        ) : (
                            <ChevronRight className="w-4 h-4" />
                        )}
                    </Button>
                </div>
            </aside>

            {/* Main Content Area */}
            <main className="flex-1 flex flex-col overflow-hidden">
                {/* Header */}
                <header className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-b border-slate-200/60 dark:border-slate-700/60 px-6 py-4 shadow-sm">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={toggleSidebar}
                                className="md:hidden w-10 h-10 rounded-xl hover:bg-slate-100 dark:hover:bg-slate-800"
                            >
                                <Menu className="h-5 w-5" />
                            </Button>
                            <div>
                                {breadcrumbs && breadcrumbs.length > 0 && (
                                    <Breadcrumb items={breadcrumbs} className="mb-2" />
                                )}
                                <h2 className="text-2xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-slate-100 dark:to-slate-300 bg-clip-text text-transparent">
                                    {title || 'Dashboard'}
                                </h2>
                                <p className="text-sm text-slate-500 dark:text-slate-400">
                                    Manage your N8N workflows and automations
                                </p>
                            </div>
                        </div>

                        {/* Header Actions */}
                        <div className="flex items-center space-x-3">
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={toggleDarkMode}
                                className="w-10 h-10 rounded-xl hover:bg-slate-100 dark:hover:bg-slate-800"
                            >
                                {theme === 'dark' ? (
                                    <Sun className="h-5 w-5" />
                                ) : (
                                    <Moon className="h-5 w-5" />
                                )}
                            </Button>
                            {/* User menu will be implemented later */}

                        </div>
                    </div>
                </header>

                {/* Page Content */}
                <div className="flex-1 overflow-y-auto bg-transparent p-6">
                    <Outlet />
                </div>
            </main>
        </div>
    );
}
