import { StrictMode } from "react";
import ReactDOM from "react-dom/client";
import App from "./App";

console.log("🚀 Main.tsx loading...");
console.log("🔍 Root element:", document.getElementById("root"));

const rootElement = document.getElementById("root");
if (!rootElement) {
  console.error("❌ Root element not found!");
  document.body.innerHTML = '<div style="padding: 20px; font-family: Arial;">❌ Root element not found! Check index.html</div>';
} else {
  console.log("✅ Root element found, mounting React app...");
  ReactDOM.createRoot(rootElement).render(
    <StrictMode>
      <App />
    </StrictMode>,
  );
  console.log("✅ React app mounted successfully!");
}
