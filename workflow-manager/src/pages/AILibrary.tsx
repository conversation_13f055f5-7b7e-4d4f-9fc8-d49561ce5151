import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
    Search, 
    Sparkles, 
    Brain, 
    Zap, 
    Download,
    Star,
    Clock,
    Users,
    Filter
} from 'lucide-react';
import { LoadingSpinner } from '@/components/LoadingSpinner';

interface AITemplate {
    id: string;
    name: string;
    description: string;
    category: 'automation' | 'data_processing' | 'ai_integration' | 'workflow_optimization';
    aiProvider: 'openai' | 'claude' | 'local' | 'multiple';
    complexity: 'beginner' | 'intermediate' | 'advanced';
    rating: number;
    downloads: number;
    lastUpdated: string;
    tags: string[];
    author: string;
    isOfficial: boolean;
}

const mockAITemplates: AITemplate[] = [
    {
        id: '1',
        name: 'Smart Email Classifier',
        description: 'Automatically classify and route emails using AI sentiment analysis and content categorization.',
        category: 'ai_integration',
        aiProvider: 'openai',
        complexity: 'intermediate',
        rating: 4.8,
        downloads: 1247,
        lastUpdated: '2024-01-10',
        tags: ['email', 'classification', 'nlp', 'automation'],
        author: 'N8N Team',
        isOfficial: true,
    },
    {
        id: '2',
        name: 'AI Content Generator',
        description: 'Generate blog posts, social media content, and marketing copy using advanced AI models.',
        category: 'automation',
        aiProvider: 'multiple',
        complexity: 'beginner',
        rating: 4.6,
        downloads: 892,
        lastUpdated: '2024-01-08',
        tags: ['content', 'generation', 'marketing', 'social-media'],
        author: 'Community',
        isOfficial: false,
    },
    {
        id: '3',
        name: 'Intelligent Data Processor',
        description: 'Process and analyze large datasets with AI-powered insights and automated reporting.',
        category: 'data_processing',
        aiProvider: 'claude',
        complexity: 'advanced',
        rating: 4.9,
        downloads: 567,
        lastUpdated: '2024-01-12',
        tags: ['data', 'analysis', 'reporting', 'insights'],
        author: 'Data Science Team',
        isOfficial: true,
    },
    {
        id: '4',
        name: 'Workflow Optimizer',
        description: 'Analyze and optimize your existing workflows using AI recommendations.',
        category: 'workflow_optimization',
        aiProvider: 'local',
        complexity: 'intermediate',
        rating: 4.7,
        downloads: 423,
        lastUpdated: '2024-01-05',
        tags: ['optimization', 'analysis', 'performance', 'recommendations'],
        author: 'Performance Team',
        isOfficial: true,
    },
];

export default function AILibrary() {
    const [templates, setTemplates] = useState<AITemplate[]>([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedCategory, setSelectedCategory] = useState<string>('all');
    const [selectedProvider, setSelectedProvider] = useState<string>('all');

    useEffect(() => {
        // Simulate loading
        setTimeout(() => {
            setTemplates(mockAITemplates);
            setLoading(false);
        }, 1000);
    }, []);

    const filteredTemplates = templates.filter(template => {
        const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            template.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
        
        const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
        const matchesProvider = selectedProvider === 'all' || template.aiProvider === selectedProvider;
        
        return matchesSearch && matchesCategory && matchesProvider;
    });

    const getCategoryIcon = (category: string) => {
        switch (category) {
            case 'automation': return <Zap className="w-4 h-4" />;
            case 'data_processing': return <Brain className="w-4 h-4" />;
            case 'ai_integration': return <Sparkles className="w-4 h-4" />;
            case 'workflow_optimization': return <Filter className="w-4 h-4" />;
            default: return <Sparkles className="w-4 h-4" />;
        }
    };

    const getProviderBadgeColor = (provider: string) => {
        switch (provider) {
            case 'openai': return 'bg-green-100 text-green-800';
            case 'claude': return 'bg-purple-100 text-purple-800';
            case 'local': return 'bg-blue-100 text-blue-800';
            case 'multiple': return 'bg-orange-100 text-orange-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <LoadingSpinner size="lg" />
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
                        <Sparkles className="w-8 h-8 text-purple-600" />
                        AI Library
                    </h1>
                    <p className="text-muted-foreground">
                        Discover AI-powered workflow templates and automations
                    </p>
                </div>
                
                <Badge variant="secondary" className="flex items-center gap-1">
                    <Brain className="w-3 h-3" />
                    {templates.length} AI Templates
                </Badge>
            </div>

            {/* Search and Filters */}
            <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                    <Input
                        placeholder="Search AI templates..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                    />
                </div>
                
                <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="px-3 py-2 border rounded-md bg-background"
                >
                    <option value="all">All Categories</option>
                    <option value="automation">Automation</option>
                    <option value="data_processing">Data Processing</option>
                    <option value="ai_integration">AI Integration</option>
                    <option value="workflow_optimization">Workflow Optimization</option>
                </select>
                
                <select
                    value={selectedProvider}
                    onChange={(e) => setSelectedProvider(e.target.value)}
                    className="px-3 py-2 border rounded-md bg-background"
                >
                    <option value="all">All Providers</option>
                    <option value="openai">OpenAI</option>
                    <option value="claude">Claude</option>
                    <option value="local">Local Models</option>
                    <option value="multiple">Multiple</option>
                </select>
            </div>

            {/* Templates Grid */}
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {filteredTemplates.map((template) => (
                    <Card key={template.id} className="hover:shadow-lg transition-shadow">
                        <CardHeader className="pb-3">
                            <div className="flex items-start justify-between">
                                <div className="flex items-center gap-2">
                                    {getCategoryIcon(template.category)}
                                    <CardTitle className="text-lg">{template.name}</CardTitle>
                                </div>
                                {template.isOfficial && (
                                    <Badge variant="default" className="text-xs">Official</Badge>
                                )}
                            </div>
                            <p className="text-sm text-muted-foreground line-clamp-2">
                                {template.description}
                            </p>
                        </CardHeader>
                        
                        <CardContent className="space-y-4">
                            {/* Provider and Complexity */}
                            <div className="flex items-center gap-2">
                                <Badge className={`text-xs ${getProviderBadgeColor(template.aiProvider)}`}>
                                    {template.aiProvider.toUpperCase()}
                                </Badge>
                                <Badge variant="outline" className="text-xs">
                                    {template.complexity}
                                </Badge>
                            </div>
                            
                            {/* Stats */}
                            <div className="flex items-center justify-between text-sm text-muted-foreground">
                                <div className="flex items-center gap-1">
                                    <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                                    <span>{template.rating}</span>
                                </div>
                                <div className="flex items-center gap-1">
                                    <Download className="w-3 h-3" />
                                    <span>{template.downloads}</span>
                                </div>
                                <div className="flex items-center gap-1">
                                    <Clock className="w-3 h-3" />
                                    <span>{template.lastUpdated}</span>
                                </div>
                            </div>
                            
                            {/* Tags */}
                            <div className="flex flex-wrap gap-1">
                                {template.tags.slice(0, 3).map((tag) => (
                                    <Badge key={tag} variant="secondary" className="text-xs">
                                        {tag}
                                    </Badge>
                                ))}
                                {template.tags.length > 3 && (
                                    <Badge variant="secondary" className="text-xs">
                                        +{template.tags.length - 3}
                                    </Badge>
                                )}
                            </div>
                            
                            {/* Author */}
                            <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                <Users className="w-3 h-3" />
                                <span>by {template.author}</span>
                            </div>
                            
                            {/* Actions */}
                            <div className="flex gap-2 pt-2">
                                <Button size="sm" className="flex-1">
                                    <Download className="w-3 h-3 mr-1" />
                                    Use Template
                                </Button>
                                <Button size="sm" variant="outline">
                                    Preview
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                ))}
            </div>

            {filteredTemplates.length === 0 && (
                <div className="text-center py-12">
                    <Sparkles className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No AI templates found</h3>
                    <p className="text-muted-foreground">
                        Try adjusting your search criteria or browse all categories.
                    </p>
                </div>
            )}
        </div>
    );
}
