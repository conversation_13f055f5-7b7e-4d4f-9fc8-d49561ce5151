import { useEffect, useState } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';
import MetricCard from '@/components/MetricCard';
import LineChartCard from '@/components/LineChartCard';
import LiveExecutionTracker from '@/components/LiveExecutionTracker';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Activity, CheckCircle2, TrendingUp, Zap, AlertTriangle, RefreshCw } from 'lucide-react';
import { LoadingPage } from '@/components/LoadingSpinner';

interface DashboardData {
    total_executions: number;
    successful_executions: number;
    failed_executions: number;
    avg_execution_time: number;
    monthly_executions: { name: string; executions: number }[];
    recent_executions: any[];
    top_workflows: any[];
    trends?: {
        total_executions: { value: number; is_positive: boolean };
        successful_executions: { value: number; is_positive: boolean };
        failed_executions: { value: number; is_positive: boolean };
        avg_execution_time: { value: number; is_positive: boolean };
    };
    data_source?: string;
    last_sync_message?: string;
    data_age?: string;
    instance_count?: number;
    message?: string;
}

export default function Dashboard() {
    const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [refreshing, setRefreshing] = useState(false);

    useEffect(() => {
        const fetchDashboardData = async (isRefresh = false) => {
            try {
                if (isRefresh) {
                    setRefreshing(true);
                } else {
                    setLoading(true);
                }
                const data = await invoke<DashboardData>('get_dashboard_data');
                setDashboardData(data);
                setError(null);
            } catch (err) {
                setError('Failed to load dashboard data.');
                console.error(err);
            } finally {
                setLoading(false);
                setRefreshing(false);
            }
        };

        fetchDashboardData();

        // Set up periodic refresh
        const interval = setInterval(() => fetchDashboardData(), 30000); // Refresh every 30 seconds

        // Listen for sync completed events to refresh dashboard immediately
        const setupSyncListener = async () => {
            const unlisten = await listen('sync_completed', () => {
                // Refresh dashboard data immediately when sync completes (as a refresh, not full loading)
                fetchDashboardData(true);
            });
            return unlisten;
        };

        let unlistenPromise = setupSyncListener();

        return () => {
            clearInterval(interval);
            unlistenPromise.then(unlisten => unlisten());
        };
    }, []);

    if (loading) {
        return <LoadingPage />;
    }

    if (error || !dashboardData) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <Card className="w-96">
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2 text-red-600">
                            <AlertTriangle className="w-5 h-5" />
                            Error Loading Dashboard
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <p className="text-muted-foreground">{error}</p>
                    </CardContent>
                </Card>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                    {refreshing && (
                        <div className="flex items-center gap-2 text-sm text-blue-600">
                            <RefreshCw className="w-4 h-4 animate-spin" />
                            Updating...
                        </div>
                    )}
                    {dashboardData.data_source && (
                        <Badge variant={
                            dashboardData.data_source === 'real_database_data' ? 'default' :
                            dashboardData.data_source === 'instances_configured_no_data' ? 'outline' :
                            'secondary'
                        }>
                            {dashboardData.data_source === 'real_database_data' ? 'Live Data' :
                             dashboardData.data_source === 'instances_configured_no_data' ? 'Instances Configured' :
                             'No Data'}
                        </Badge>
                    )}
                </div>
            </div>

            {/* Status Message */}
            {dashboardData.message && (
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                    <div className="flex items-center gap-2">
                        <Activity className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                        <div className="text-sm text-blue-800 dark:text-blue-200">
                            {dashboardData.message}
                        </div>
                    </div>
                    {dashboardData.last_sync_message && (
                        <div className="text-xs text-blue-600 dark:text-blue-300 mt-1">
                            {dashboardData.last_sync_message}
                        </div>
                    )}
                </div>
            )}

            {/* Metrics Grid */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <MetricCard
                    title="Total Executions"
                    value={dashboardData.total_executions}
                    icon={<Activity />}
                    trend={dashboardData.trends?.total_executions}
                />
                <MetricCard
                    title="Successful"
                    value={dashboardData.successful_executions}
                    icon={<CheckCircle2 />}
                    trend={dashboardData.trends?.successful_executions}
                    className="text-green-600"
                />
                <MetricCard
                    title="Failed"
                    value={dashboardData.failed_executions}
                    icon={<AlertTriangle />}
                    trend={dashboardData.trends?.failed_executions}
                    className="text-red-600"
                />
                <MetricCard
                    title="Avg. Execution Time"
                    value={`${dashboardData.avg_execution_time}ms`}
                    icon={<TrendingUp />}
                    trend={dashboardData.trends?.avg_execution_time}
                />
            </div>

            {/* Charts and Live Data - Only show if we have data */}
            {dashboardData.monthly_executions.length > 0 ? (
                <div className="grid gap-6 md:grid-cols-2">
                    <LineChartCard
                        title="Monthly Executions"
                        data={dashboardData.monthly_executions}
                        dataKey="name"
                        lineKey="executions"
                    />
                    <LiveExecutionTracker />
                </div>
            ) : (
                <div className="grid gap-6 md:grid-cols-2">
                    <Card>
                        <CardHeader>
                            <CardTitle>Monthly Executions</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="flex items-center justify-center py-8">
                                <div className="text-center">
                                    <Activity className="w-8 h-8 text-slate-400 mx-auto mb-2" />
                                    <div className="text-sm text-slate-500 dark:text-slate-400">
                                        No execution data available yet
                                    </div>
                                    <div className="text-xs text-slate-400 dark:text-slate-500 mt-1">
                                        Connect to N8N instances to see execution trends
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    <LiveExecutionTracker />
                </div>
            )}

            {/* Recent Activity */}
            <Card>
                <CardHeader>
                    <CardTitle>Recent Executions</CardTitle>
                </CardHeader>
                <CardContent>
                    {dashboardData.recent_executions.length > 0 ? (
                        <div className="space-y-2">
                            {dashboardData.recent_executions.slice(0, 10).map((execution, index) => (
                                <div key={index} className="flex items-center justify-between p-2 rounded border">
                                    <span className="font-medium">{execution.workflow_name || 'Unknown Workflow'}</span>
                                    <Badge variant={execution.status === 'success' ? 'default' : 'destructive'}>
                                        {execution.status}
                                    </Badge>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="flex items-center justify-center py-8">
                            <div className="text-center">
                                <Activity className="w-8 h-8 text-slate-400 mx-auto mb-2" />
                                <div className="text-sm text-slate-500 dark:text-slate-400">
                                    No execution data available yet
                                </div>
                                <div className="text-xs text-slate-400 dark:text-slate-500 mt-1">
                                    {dashboardData.message || "Connect to N8N instances to see execution history"}
                                </div>
                            </div>
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Zap className="w-5 h-5" />
                        Quick Actions
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="text-sm text-muted-foreground">
                        <p>• Sync data from your N8N instances in the <strong>Instances</strong> page</p>
                        <p>• Manage workflows and templates from their respective pages</p>
                        <p>• Monitor execution status in real-time above</p>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
