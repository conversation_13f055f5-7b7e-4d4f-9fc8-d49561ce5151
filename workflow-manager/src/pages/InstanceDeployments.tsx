import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate, Link } from 'react-router-dom';
import { invoke } from '@tauri-apps/api/core';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { ArrowLeft, Search, Play, Pause, Eye, Edit, Trash2, Activity, Clock, CheckCircle, XCircle } from 'lucide-react';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import toast from 'react-hot-toast';

interface N8nInstance {
    id: string;
    name: string;
    url: string;
    status: string;
    version?: string;
    description?: string;
    is_default: boolean;
    created_at: string;
    updated_at: string;
    last_ping?: string;
}

interface DeployedWorkflow {
    id: string;
    n8n_instance_id: string;
    n8n_workflow_id: string;
    name: string;
    is_active: boolean;
    tags: string[];
    nodes: any;
    connections: any;
    last_sync_at: string;
    n8n_instance?: N8nInstance;
}

interface WorkflowStats {
    total_executions: number;
    successful_executions: number;
    failed_executions: number;
    last_execution?: string;
    last_execution_status?: string;
}

export default function InstanceDeployments() {
    const { instanceId } = useParams<{ instanceId: string }>();
    const navigate = useNavigate();
    
    const [instance, setInstance] = useState<N8nInstance | null>(null);
    const [workflows, setWorkflows] = useState<DeployedWorkflow[]>([]);
    const [filteredWorkflows, setFilteredWorkflows] = useState<DeployedWorkflow[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [searchQuery, setSearchQuery] = useState('');
    const [workflowStats, setWorkflowStats] = useState<{ [key: string]: WorkflowStats }>({});

    useEffect(() => {
        if (instanceId) {
            fetchInstanceAndWorkflows();
        }
    }, [instanceId]);

    useEffect(() => {
        // Filter workflows based on search query
        if (searchQuery.trim() === '') {
            setFilteredWorkflows(workflows);
        } else {
            const filtered = workflows.filter(workflow =>
                workflow.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                workflow.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
            );
            setFilteredWorkflows(filtered);
        }
    }, [searchQuery, workflows]);

    const fetchInstanceAndWorkflows = async () => {
        if (!instanceId) return;

        try {
            setLoading(true);
            setError(null);

            // Fetch instance details
            const instanceData = await invoke<N8nInstance>('get_n8n_instance', { id: instanceId });
            if (!instanceData) {
                setError('Instance not found');
                return;
            }
            setInstance(instanceData);

            // Fetch deployed workflows for this instance
            const workflowsData = await invoke<DeployedWorkflow[]>('list_workflows', { 
                instance_id: instanceId,
                limit: null,
                offset: null
            });
            setWorkflows(workflowsData);

            // Fetch stats for each workflow (optional - can be implemented later)
            // const statsPromises = workflowsData.map(async (workflow) => {
            //     try {
            //         const stats = await invoke<WorkflowStats>('get_workflow_stats', { 
            //             workflow_id: workflow.id 
            //         });
            //         return { [workflow.id]: stats };
            //     } catch (err) {
            //         return { [workflow.id]: null };
            //     }
            // });
            // const statsResults = await Promise.all(statsPromises);
            // const combinedStats = statsResults.reduce((acc, curr) => ({ ...acc, ...curr }), {});
            // setWorkflowStats(combinedStats);

        } catch (err) {
            console.error('Failed to fetch instance deployments:', err);
            setError('Failed to load instance deployments');
            toast.error('Failed to load deployments');
        } finally {
            setLoading(false);
        }
    };

    const handleToggleWorkflow = async (workflowId: string, isActive: boolean) => {
        try {
            // This would call a backend command to toggle workflow status
            // await invoke('toggle_workflow_status', { id: workflowId, is_active: !isActive });
            toast.success(`Workflow ${!isActive ? 'activated' : 'paused'}`);
            fetchInstanceAndWorkflows(); // Refresh data
        } catch (err) {
            console.error('Failed to toggle workflow:', err);
            toast.error('Failed to toggle workflow status');
        }
    };

    const handleDeleteWorkflow = async (workflowId: string) => {
        if (!confirm('Are you sure you want to delete this workflow?')) return;

        try {
            await invoke('delete_workflow', { id: workflowId });
            toast.success('Workflow deleted successfully');
            fetchInstanceAndWorkflows(); // Refresh data
        } catch (err) {
            console.error('Failed to delete workflow:', err);
            toast.error('Failed to delete workflow');
        }
    };

    const getStatusBadge = (isActive: boolean) => {
        return isActive ? (
            <Badge variant="default" className="bg-green-100 text-green-800">
                <CheckCircle className="w-3 h-3 mr-1" />
                Active
            </Badge>
        ) : (
            <Badge variant="secondary">
                <XCircle className="w-3 h-3 mr-1" />
                Inactive
            </Badge>
        );
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-96">
                <LoadingSpinner size="lg" />
            </div>
        );
    }

    if (error || !instance) {
        return (
            <div className="space-y-6">
                <div className="flex items-center gap-4">
                    <Button variant="outline" size="sm" onClick={() => navigate('/instances')}>
                        <ArrowLeft className="w-4 h-4 mr-2" />
                        Back to Instances
                    </Button>
                </div>
                <Card className="border-red-200 bg-red-50">
                    <CardContent className="pt-6">
                        <p className="text-red-600">{error || 'Instance not found'}</p>
                    </CardContent>
                </Card>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center gap-4">
                <Button variant="outline" size="sm" onClick={() => navigate('/instances')}>
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Instances
                </Button>
                <div>
                    <p className="text-muted-foreground">
                        Deployed workflows on <strong>{instance.name}</strong>
                    </p>
                </div>
            </div>

            {/* Instance Info */}
            <Card>
                <CardHeader>
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                            <CardTitle className="text-xl">{instance.name}</CardTitle>
                            <Badge variant={instance.status === 'active' ? 'default' : 'secondary'}>
                                {instance.status}
                            </Badge>
                            {instance.is_default && (
                                <Badge variant="outline">Default</Badge>
                            )}
                        </div>
                        <div className="text-sm text-muted-foreground">
                            {workflows.length} deployed workflow{workflows.length !== 1 ? 's' : ''}
                        </div>
                    </div>
                    <p className="text-sm text-muted-foreground">{instance.url}</p>
                </CardHeader>
            </Card>

            {/* Search */}
            <div className="flex items-center gap-4">
                <div className="relative flex-1 max-w-md">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                    <Input
                        placeholder="Search workflows..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10"
                    />
                </div>
            </div>

            {/* Workflows Grid */}
            {filteredWorkflows.length === 0 ? (
                <Card>
                    <CardContent className="pt-6 text-center">
                        <Activity className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                        <p className="text-muted-foreground mb-2">
                            {searchQuery ? 'No workflows match your search' : 'No workflows deployed on this instance'}
                        </p>
                        {!searchQuery && (
                            <p className="text-sm text-muted-foreground mb-4">
                                Deploy templates to this instance from the Workflows page.
                            </p>
                        )}
                        <Link to="/workflows">
                            <Button variant="outline">
                                Go to Workflows
                            </Button>
                        </Link>
                    </CardContent>
                </Card>
            ) : (
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {filteredWorkflows.map((workflow) => (
                        <Card key={workflow.id} className="hover:shadow-lg transition-shadow">
                            <CardHeader className="pb-3">
                                <div className="flex items-center justify-between">
                                    <CardTitle className="text-lg truncate">{workflow.name}</CardTitle>
                                    {getStatusBadge(workflow.is_active)}
                                </div>
                                {workflow.tags.length > 0 && (
                                    <div className="flex flex-wrap gap-1">
                                        {workflow.tags.slice(0, 3).map((tag, index) => (
                                            <Badge key={index} variant="outline" className="text-xs">
                                                {tag}
                                            </Badge>
                                        ))}
                                        {workflow.tags.length > 3 && (
                                            <Badge variant="outline" className="text-xs">
                                                +{workflow.tags.length - 3}
                                            </Badge>
                                        )}
                                    </div>
                                )}
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-3">
                                    {/* Workflow Info */}
                                    <div className="text-sm text-muted-foreground">
                                        <div className="flex items-center gap-2">
                                            <Clock className="w-3 h-3" />
                                            Last sync: {new Date(workflow.last_sync_at).toLocaleDateString()}
                                        </div>
                                    </div>

                                    {/* Stats (if available) */}
                                    {workflowStats[workflow.id] && (
                                        <div className="grid grid-cols-2 gap-2 text-sm">
                                            <div>
                                                <span className="text-muted-foreground">Executions:</span>
                                                <span className="ml-1 font-medium">
                                                    {workflowStats[workflow.id].total_executions}
                                                </span>
                                            </div>
                                            <div>
                                                <span className="text-muted-foreground">Success:</span>
                                                <span className="ml-1 font-medium text-green-600">
                                                    {workflowStats[workflow.id].successful_executions}
                                                </span>
                                            </div>
                                        </div>
                                    )}

                                    {/* Actions */}
                                    <div className="flex items-center gap-2 flex-wrap">
                                        <Button
                                            size="sm"
                                            variant="outline"
                                            onClick={() => handleToggleWorkflow(workflow.id, workflow.is_active)}
                                        >
                                            {workflow.is_active ? (
                                                <>
                                                    <Pause className="w-3 h-3 mr-1" />
                                                    Pause
                                                </>
                                            ) : (
                                                <>
                                                    <Play className="w-3 h-3 mr-1" />
                                                    Start
                                                </>
                                            )}
                                        </Button>

                                        <Link to={`/workflows/${workflow.id}`}>
                                            <Button size="sm" variant="outline">
                                                <Eye className="w-3 h-3 mr-1" />
                                                View
                                            </Button>
                                        </Link>

                                        <Link to={`/workflows/${workflow.id}/edit`}>
                                            <Button size="sm" variant="outline">
                                                <Edit className="w-3 h-3" />
                                            </Button>
                                        </Link>

                                        <Button
                                            size="sm"
                                            variant="outline"
                                            onClick={() => handleDeleteWorkflow(workflow.id)}
                                            className="text-red-600 hover:text-red-700"
                                        >
                                            <Trash2 className="w-3 h-3" />
                                        </Button>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>
            )}
        </div>
    );
}
