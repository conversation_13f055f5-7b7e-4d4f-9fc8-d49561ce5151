import { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

import { Plus, Server, Edit, Trash2, TestTube, Eye, EyeOff, Rocket } from 'lucide-react';
import { LoadingSpinner } from '@/components/LoadingSpinner';

import toast from 'react-hot-toast';

interface N8nInstance {
    id: string;
    name: string;
    url: string;
    api_key?: string;
    status: string;
    version?: string;
    description?: string;
    is_default: boolean;
    created_at: string;
    updated_at: string;
    last_ping?: string;
    stats?: {
        total_workflows: number;
        active_workflows: number;
        total_executions: number;
        last_execution: string;
    };
}

interface AppSettings {
    auto_sync: boolean;
    sync_interval: number;
    desktop_notifications: boolean;
}

interface CreateInstanceData {
    name: string;
    url: string;
    api_key: string;
    is_default: boolean;
}

export default function Instances() {
    const navigate = useNavigate();
    const [instances, setInstances] = useState<N8nInstance[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [showCreateDialog, setShowCreateDialog] = useState(false);
    const [createData, setCreateData] = useState<CreateInstanceData>({
        name: '',
        url: '',
        api_key: '',
        is_default: false
    });
    const [creating, setCreating] = useState(false);
    const [showPassword, setShowPassword] = useState<{ [key: string]: boolean }>({});
    const [editingInstance, setEditingInstance] = useState<N8nInstance | null>(null);
    const [showEditDialog, setShowEditDialog] = useState(false);
    const [syncingInstances, setSyncingInstances] = useState<Set<string>>(new Set());
    const [settings, setSettings] = useState<AppSettings | null>(null);
    const [showDeleteDialog, setShowDeleteDialog] = useState(false);
    const [instanceToDelete, setInstanceToDelete] = useState<N8nInstance | null>(null);
    const [deleting, setDeleting] = useState(false);

    useEffect(() => {
        fetchInstances();
        loadSettings();
    }, []);

    const loadSettings = async () => {
        try {
            const appSettings = await invoke<AppSettings>('get_app_settings');
            setSettings(appSettings);
        } catch (err) {
            console.error('Failed to load settings:', err);
        }
    };

    // Get real sync status from backend
    useEffect(() => {
        const fetchSyncStatus = async () => {
            try {
                const syncingIds = await invoke<string[]>('get_syncing_instances');
                setSyncingInstances(new Set(syncingIds));
            } catch (err) {
                console.error('Failed to get syncing instances:', err);
            }
        };

        // Fetch sync status immediately and then every 2 seconds
        fetchSyncStatus();
        const interval = setInterval(fetchSyncStatus, 2000);

        return () => clearInterval(interval);
    }, []);

    const fetchInstances = async () => {
        try {
            setLoading(true);
            const data = await invoke<N8nInstance[]>('list_n8n_instances', {
                limit: null,
                offset: null
            });
            setInstances(data);
            setError(null);

            // Auto-select as default if there's only one instance and no default is set
            if (data.length === 1 && !data[0].is_default) {
                try {
                    await invoke('set_default_n8n_instance', { id: data[0].id });
                    // Refresh to get updated default status
                    const updatedData = await invoke<N8nInstance[]>('list_n8n_instances', {
                        limit: null,
                        offset: null
                    });
                    setInstances(updatedData);
                    toast.success(`"${data[0].name}" automatically set as default instance`);
                } catch (err) {
                    console.error('Failed to auto-set default instance:', err);
                }
            }
        } catch (err) {
            setError('Failed to load N8N instances.');
            console.error('Error fetching instances:', err);
        } finally {
            setLoading(false);
        }
    };

    const handleCreateInstance = async () => {
        if (!createData.name || !createData.url || !createData.api_key) {
            toast.error('Please fill in all required fields');
            return;
        }

        try {
            setCreating(true);
            await invoke('create_n8n_instance', {
                create_data: {
                    name: createData.name,
                    url: createData.url,
                    api_key: createData.api_key,
                    is_default: createData.is_default,
                    description: null
                }
            });
            toast.success('N8N instance created successfully');
            setShowCreateDialog(false);
            setCreateData({ name: '', url: '', api_key: '', is_default: false });
            fetchInstances();
        } catch (err) {
            toast.error('Failed to create N8N instance');
            console.error('Error creating instance:', err);
        } finally {
            setCreating(false);
        }
    };

    const handleTestConnection = async (instanceId: string) => {
        try {
            const result = await invoke<boolean>('ping_n8n_instance', { id: instanceId });
            if (result) {
                toast.success('Connection test successful');
            } else {
                toast.error('Connection test failed');
            }
            fetchInstances(); // Refresh to update health status
        } catch (err) {
            toast.error('Connection test failed');
            console.error('Error testing connection:', err);
        }
    };

    const handleDeleteInstance = (instance: N8nInstance) => {
        setInstanceToDelete(instance);
        setShowDeleteDialog(true);
    };

    const confirmDeleteInstance = async () => {
        if (!instanceToDelete) return;

        setDeleting(true);
        try {
            await invoke('delete_n8n_instance', { id: instanceToDelete.id });
            toast.success('N8N instance deleted successfully');
            fetchInstances();
            setShowDeleteDialog(false);
            setInstanceToDelete(null);
        } catch (err) {
            toast.error('Failed to delete N8N instance');
            console.error(err);
        } finally {
            setDeleting(false);
        }
    };

    const handleSetDefault = async (instanceId: string) => {
        try {
            await invoke('set_default_n8n_instance', { id: instanceId });
            toast.success('Default instance updated');
            fetchInstances();
        } catch (err) {
            toast.error('Failed to set default instance');
            console.error(err);
        }
    };

    const handleEditInstance = (instance: N8nInstance) => {
        setEditingInstance(instance);
        setCreateData({
            name: instance.name,
            url: instance.url,
            api_key: '', // Don't pre-fill for security
            is_default: instance.is_default
        });
        setShowEditDialog(true);
    };

    const handleUpdateInstance = async () => {
        if (!editingInstance || !createData.name || !createData.url) {
            toast.error('Please fill in all required fields');
            return;
        }

        try {
            setCreating(true);

            // Build update data - only include fields that should be updated
            const updateData: any = {};

            // Always update name and URL if they're provided
            if (createData.name.trim()) {
                updateData.name = createData.name.trim();
            }
            if (createData.url.trim()) {
                updateData.url = createData.url.trim();
            }

            // Only update API key if a new one is provided (not empty)
            if (createData.api_key && createData.api_key.trim()) {
                updateData.api_key = createData.api_key.trim();
            }

            // Always update is_default
            updateData.is_default = createData.is_default;

            await invoke('update_n8n_instance', {
                id: editingInstance.id,
                update_data: updateData
            });

            toast.success('N8N instance updated successfully');
            setShowEditDialog(false);
            setEditingInstance(null);
            setCreateData({ name: '', url: '', api_key: '', is_default: false });
            fetchInstances();
        } catch (err) {
            toast.error('Failed to update N8N instance');
            console.error('Update error:', err);
        } finally {
            setCreating(false);
        }
    };

    const getHealthStatusBadge = (status: string) => {
        switch (status.toLowerCase()) {
            case 'active':
                return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>;
            case 'error':
                return <Badge variant="destructive">Error</Badge>;
            case 'inactive':
            default:
                return <Badge variant="secondary">Inactive</Badge>;
        }
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-96">
                <LoadingSpinner size="lg" />
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">

                <Dialog open={showCreateDialog} onOpenChange={(open) => {
                    setShowCreateDialog(open);
                    // Auto-check "Set as default" if this will be the first instance
                    if (open && instances.length === 0) {
                        setCreateData(prev => ({ ...prev, is_default: true }));
                    }
                }}>
                    <DialogTrigger asChild>
                        <Button>
                            <Plus className="w-4 h-4 mr-2" />
                            Add Instance
                        </Button>
                    </DialogTrigger>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Add N8N Instance</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                            <div>
                                <Label htmlFor="name">Instance Name</Label>
                                <Input
                                    id="name"
                                    value={createData.name}
                                    onChange={(e) => setCreateData(prev => ({ ...prev, name: e.target.value }))}
                                    placeholder="My N8N Instance"
                                />
                            </div>
                            <div>
                                <Label htmlFor="url">N8N URL</Label>
                                <Input
                                    id="url"
                                    value={createData.url}
                                    onChange={(e) => setCreateData(prev => ({ ...prev, url: e.target.value }))}
                                    placeholder="https://n8n.example.com"
                                />
                            </div>
                            <div>
                                <Label htmlFor="api_key">API Key</Label>
                                <div className="relative">
                                    <Input
                                        id="api_key"
                                        type={showPassword.create ? 'text' : 'password'}
                                        value={createData.api_key}
                                        onChange={(e) => setCreateData(prev => ({ ...prev, api_key: e.target.value }))}
                                        placeholder="n8n_api_key_..."
                                        className="pr-10"
                                    />
                                    <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        className="absolute right-0 top-0 h-full px-3"
                                        onClick={() => setShowPassword(prev => ({ ...prev, create: !prev.create }))}
                                    >
                                        {showPassword.create ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                                    </Button>
                                </div>
                                <p className="text-xs text-muted-foreground mt-1">
                                    This API key will be securely stored and encrypted
                                </p>
                            </div>
                            <div className="flex items-center space-x-2">
                                <input
                                    type="checkbox"
                                    id="is_default"
                                    checked={createData.is_default}
                                    onChange={(e) => setCreateData(prev => ({ ...prev, is_default: e.target.checked }))}
                                />
                                <Label htmlFor="is_default">Set as default instance</Label>
                            </div>
                            <div className="flex justify-end space-x-2">
                                <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                                    Cancel
                                </Button>
                                <Button onClick={handleCreateInstance} disabled={creating}>
                                    {creating ? <LoadingSpinner size="sm" /> : 'Create Instance'}
                                </Button>
                            </div>
                        </div>
                    </DialogContent>
                </Dialog>

                {/* Edit Instance Dialog */}
                <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Edit N8N Instance</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                            <div>
                                <Label htmlFor="edit_name">Instance Name</Label>
                                <Input
                                    id="edit_name"
                                    value={createData.name}
                                    onChange={(e) => setCreateData(prev => ({ ...prev, name: e.target.value }))}
                                    placeholder="My N8N Instance"
                                />
                            </div>
                            <div>
                                <Label htmlFor="edit_url">N8N URL</Label>
                                <Input
                                    id="edit_url"
                                    value={createData.url}
                                    onChange={(e) => setCreateData(prev => ({ ...prev, url: e.target.value }))}
                                    placeholder="https://n8n.example.com"
                                />
                            </div>
                            <div>
                                <Label htmlFor="edit_api_key">API Key (leave empty to keep current)</Label>
                                <div className="relative">
                                    <Input
                                        id="edit_api_key"
                                        type={showPassword.edit ? 'text' : 'password'}
                                        value={createData.api_key}
                                        onChange={(e) => setCreateData(prev => ({ ...prev, api_key: e.target.value }))}
                                        placeholder="Enter new API key or leave empty"
                                        className="pr-10"
                                    />
                                    <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        className="absolute right-0 top-0 h-full px-3"
                                        onClick={() => setShowPassword(prev => ({ ...prev, edit: !prev.edit }))}
                                    >
                                        {showPassword.edit ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                                    </Button>
                                </div>
                                <p className="text-xs text-muted-foreground mt-1">
                                    Leave empty to keep the current API key unchanged
                                </p>
                            </div>
                            <div className="flex items-center space-x-2">
                                <input
                                    type="checkbox"
                                    id="edit_is_default"
                                    checked={createData.is_default}
                                    onChange={(e) => setCreateData(prev => ({ ...prev, is_default: e.target.checked }))}
                                />
                                <Label htmlFor="edit_is_default">Set as default instance</Label>
                            </div>
                            <div className="flex justify-end space-x-2">
                                <Button variant="outline" onClick={() => {
                                    setShowEditDialog(false);
                                    setEditingInstance(null);
                                    setCreateData({ name: '', url: '', api_key: '', is_default: false });
                                }}>
                                    Cancel
                                </Button>
                                <Button onClick={handleUpdateInstance} disabled={creating}>
                                    {creating ? <LoadingSpinner size="sm" /> : 'Update Instance'}
                                </Button>
                            </div>
                        </div>
                    </DialogContent>
                </Dialog>
            </div>

            {/* Error State */}
            {error && (
                <Card className="border-red-200 bg-red-50">
                    <CardContent className="pt-6">
                        <p className="text-red-600">{error}</p>
                    </CardContent>
                </Card>
            )}

            {/* Instances Grid */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {instances.map((instance) => (
                    <Card key={instance.id} className="hover:shadow-lg transition-shadow">
                        <CardHeader className="pb-3">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <Server className="w-5 h-5" />
                                    <CardTitle className="text-lg">{instance.name}</CardTitle>
                                </div>
                                <div className="flex items-center gap-2">
                                    {instance.is_default && (
                                        <Badge variant="outline" className="text-xs">Default</Badge>
                                    )}
                                    {getHealthStatusBadge(instance.status)}
                                </div>
                            </div>
                            <p className="text-sm text-muted-foreground">{instance.url}</p>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {/* Stats */}
                                {instance.stats && (
                                    <div className="grid grid-cols-2 gap-2 text-sm">
                                        <div>
                                            <span className="text-muted-foreground">Workflows:</span>
                                            <span className="ml-1 font-medium">{instance.stats.total_workflows}</span>
                                        </div>
                                        <div>
                                            <span className="text-muted-foreground">Active:</span>
                                            <span className="ml-1 font-medium">{instance.stats.active_workflows}</span>
                                        </div>
                                        <div className="col-span-2">
                                            <span className="text-muted-foreground">Executions:</span>
                                            <span className="ml-1 font-medium">{instance.stats.total_executions}</span>
                                        </div>
                                    </div>
                                )}

                                {/* Sync Status - only show when actually syncing or when sync is enabled */}
                                {instance.status.toLowerCase() === 'active' && (
                                    <div className="space-y-2">
                                        <div className="text-xs font-medium text-muted-foreground mb-1">Sync Status</div>
                                        {syncingInstances.has(instance.id) ? (
                                            <div className="flex items-center gap-2 text-sm text-blue-600">
                                                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                                                Syncing
                                            </div>
                                        ) : settings?.auto_sync ? (
                                            <div className="flex items-center gap-2 text-sm text-gray-500">
                                                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                                                Ready
                                            </div>
                                        ) : (
                                            <div className="flex items-center gap-2 text-sm text-gray-400">
                                                <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                                                Disabled
                                            </div>
                                        )}
                                    </div>
                                )}

                                {/* Actions */}
                                <div className="flex items-center gap-2 flex-wrap">
                                    <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => handleTestConnection(instance.id)}
                                    >
                                        <TestTube className="w-3 h-3 mr-1" />
                                        Test
                                    </Button>

                                    <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => navigate(`/instances/${instance.id}/deployments`)}
                                    >
                                        <Rocket className="w-3 h-3 mr-1" />
                                        Deployments
                                    </Button>

                                    {!instance.is_default && (
                                        <Button
                                            size="sm"
                                            variant="outline"
                                            onClick={() => handleSetDefault(instance.id)}
                                            className="text-xs"
                                        >
                                            Default
                                        </Button>
                                    )}

                                    <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => handleEditInstance(instance)}
                                    >
                                        <Edit className="w-3 h-3" />
                                    </Button>

                                    <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => handleDeleteInstance(instance)}
                                        className="text-red-600 hover:text-red-700"
                                    >
                                        <Trash2 className="w-3 h-3" />
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                ))}
            </div>

            {/* Empty State */}
            {instances.length === 0 && !loading && !error && (
                <Card>
                    <CardContent className="pt-6 text-center">
                        <Server className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                        <p className="text-muted-foreground mb-4">No N8N instances configured.</p>
                        <p className="text-sm text-muted-foreground mb-4">
                            Add your first N8N instance to start managing workflows.
                        </p>
                        <Dialog open={showCreateDialog} onOpenChange={(open) => {
                            setShowCreateDialog(open);
                            // Auto-check "Set as default" if this will be the first instance
                            if (open && instances.length === 0) {
                                setCreateData(prev => ({ ...prev, is_default: true }));
                            }
                        }}>
                            <DialogTrigger asChild>
                                <Button>
                                    <Plus className="w-4 h-4 mr-2" />
                                    Add Your First Instance
                                </Button>
                            </DialogTrigger>
                        </Dialog>
                    </CardContent>
                </Card>
            )}

            {/* Delete Confirmation Dialog */}
            <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Delete N8N Instance</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                        <p>Are you sure you want to delete the N8N instance <strong>"{instanceToDelete?.name}"</strong>?</p>
                        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                            <p className="text-red-800 text-sm font-medium mb-2">⚠️ This action cannot be undone</p>
                            <p className="text-red-700 text-sm">
                                This will permanently delete:
                            </p>
                            <ul className="text-red-700 text-sm mt-2 ml-4 list-disc">
                                <li>The N8N instance connection</li>
                                <li>All deployed workflows on this instance</li>
                                <li>All workflow execution history from this instance</li>
                                <li>Stored API credentials for this instance</li>
                            </ul>

                        </div>
                    </div>
                    <div className="flex justify-end gap-2 mt-6">
                        <Button
                            variant="outline"
                            onClick={() => setShowDeleteDialog(false)}
                            disabled={deleting}
                        >
                            Cancel
                        </Button>
                        <Button
                            variant="destructive"
                            onClick={confirmDeleteInstance}
                            disabled={deleting}
                        >
                            {deleting ? (
                                <>
                                    <LoadingSpinner size="sm" />
                                    Deleting...
                                </>
                            ) : (
                                'Delete Instance'
                            )}
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    );
}
