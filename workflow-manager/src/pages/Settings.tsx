import { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import {
    Settings as SettingsIcon,
    Key,
    Brain,
    Palette,
    Bell,
    Shield,
    Plus,
    Edit,
    Trash2,
    Eye,
    EyeOff
} from 'lucide-react';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { useTheme } from '@/components/ThemeProvider';
import toast from 'react-hot-toast';

interface CredentialInfo {
    id: string;
    credential_type: string;
    name: string;
    description?: string;
    created_at: string;
    last_used?: string;
}

interface CreateCredentialData {
    credential_type: string;
    name: string;
    description: string;
    value: string;
}

export default function Settings() {
    const { theme, setTheme } = useTheme();
    const [credentials, setCredentials] = useState<CredentialInfo[]>([]);
    const [loading, setLoading] = useState(true);
    const [showCreateCredDialog, setShowCreateCredDialog] = useState(false);
    const [createCredData, setCreateCredData] = useState<CreateCredentialData>({
        credential_type: 'OpenAiApiKey',
        name: '',
        description: '',
        value: ''
    });
    const [creating, setCreating] = useState(false);
    const [showPassword, setShowPassword] = useState<{ [key: string]: boolean }>({});

    // Edit credential state
    const [showEditCredDialog, setShowEditCredDialog] = useState(false);
    const [editingCredential, setEditingCredential] = useState<CredentialInfo | null>(null);
    const [editCredData, setEditCredData] = useState({
        value: ''
    });
    const [updating, setUpdating] = useState(false);

    // Delete confirmation dialog state
    const [showDeleteConfirmDialog, setShowDeleteConfirmDialog] = useState(false);
    const [deletingCredential, setDeletingCredential] = useState<CredentialInfo | null>(null);
    const [deleting, setDeleting] = useState(false);

    // Application Settings
    const [settings, setSettings] = useState({
        autoSync: true,
        notifications: true,
        syncInterval: 30,
        maxExecutionHistory: 1000,
        enableAnalytics: false,
        autoBackup: true,
        backupInterval: 24
    });
    const [settingsLoading, setSettingsLoading] = useState(false);

    useEffect(() => {
        fetchCredentials();
        loadSettings();
    }, []);

    const loadSettings = async () => {
        try {
            setSettingsLoading(true);
            const appSettings = await invoke('get_app_settings');
            setSettings({
                autoSync: appSettings.auto_sync,
                notifications: appSettings.notifications,
                syncInterval: appSettings.sync_interval,
                maxExecutionHistory: appSettings.max_execution_history,
                enableAnalytics: appSettings.enable_analytics,
                autoBackup: appSettings.auto_backup,
                backupInterval: appSettings.backup_interval
            });
        } catch (error) {
            console.error('Failed to load settings:', error);
            toast.error('Failed to load settings');
        } finally {
            setSettingsLoading(false);
        }
    };

    const saveSettings = async (newSettings: typeof settings) => {
        try {
            setSettingsLoading(true);
            await invoke('update_app_settings', {
                updateData: {
                    auto_sync: newSettings.autoSync,
                    notifications: newSettings.notifications,
                    sync_interval: newSettings.syncInterval,
                    max_execution_history: newSettings.maxExecutionHistory,
                    enable_analytics: newSettings.enableAnalytics,
                    auto_backup: newSettings.autoBackup,
                    backup_interval: newSettings.backupInterval
                }
            });
            setSettings(newSettings);
            toast.success('Settings saved successfully');
        } catch (error) {
            console.error('Failed to save settings:', error);
            toast.error('Failed to save settings');
        } finally {
            setSettingsLoading(false);
        }
    };

    const fetchCredentials = async () => {
        try {
            setLoading(true);
            const data = await invoke<CredentialInfo[]>('list_credentials');
            // Filter out N8N API keys - those are managed in the Instances page
            const aiCredentials = data.filter(cred => cred.credential_type !== 'N8nApiKey');
            setCredentials(aiCredentials);
        } catch (err) {
            console.error('Failed to load credentials:', err);
        } finally {
            setLoading(false);
        }
    };

    const handleCreateCredential = async () => {
        if (!createCredData.name || !createCredData.value) {
            toast.error('Please fill in all required fields');
            return;
        }

        try {
            setCreating(true);
            await invoke('create_credential', {
                request: {
                    credential_type: createCredData.credential_type,
                    name: createCredData.name,
                    description: createCredData.description || null,
                    value: createCredData.value,
                    instance_name: null
                }
            });
            toast.success('Credential created successfully');
            setShowCreateCredDialog(false);
            setCreateCredData({
                credential_type: 'OpenAiApiKey',
                name: '',
                description: '',
                value: ''
            });
            fetchCredentials();
        } catch (err) {
            toast.error('Failed to create credential');
            console.error(err);
        } finally {
            setCreating(false);
        }
    };

    const handleDeleteCredential = (credential: CredentialInfo) => {
        setDeletingCredential(credential);
        setShowDeleteConfirmDialog(true);
    };

    const confirmDeleteCredential = async () => {
        if (!deletingCredential) return;

        setDeleting(true);

        try {
            await invoke('delete_credential', { id: deletingCredential.id });
            toast.success('Credential deleted successfully');
            fetchCredentials();
        } catch (err) {
            console.error('Failed to delete credential:', err);
            toast.error('Failed to delete credential');
        } finally {
            setDeleting(false);
            setShowDeleteConfirmDialog(false);
            setDeletingCredential(null);
        }
    };

    const cancelDeleteCredential = () => {
        setShowDeleteConfirmDialog(false);
        setDeletingCredential(null);
    };

    const handleTestCredential = async (credentialId: string) => {
        try {
            const result = await invoke<boolean>('validate_credential', { id: credentialId });
            if (result) {
                toast.success('Credential test successful');
            } else {
                toast.error('Credential test failed');
            }
        } catch (err) {
            toast.error('Credential test failed');
            console.error(err);
        }
    };

    const handleEditCredential = (credential: CredentialInfo) => {
        setEditingCredential(credential);
        setEditCredData({ value: '' }); // Don't pre-fill the API key for security
        setShowEditCredDialog(true);
    };

    const handleUpdateCredential = async () => {
        if (!editingCredential || !editCredData.value) {
            toast.error('Please enter a new API key');
            return;
        }

        try {
            setUpdating(true);
            await invoke('update_credential', {
                id: editingCredential.id,
                request: {
                    value: editCredData.value
                }
            });
            toast.success('Credential updated successfully');
            setShowEditCredDialog(false);
            setEditingCredential(null);
            setEditCredData({ value: '' });
            fetchCredentials();
        } catch (err) {
            toast.error('Failed to update credential');
            console.error(err);
        } finally {
            setUpdating(false);
        }
    };

    const getCredentialTypeIcon = (type: string) => {
        switch (type) {
            case 'OpenAiApiKey':
            case 'ClaudeApiKey':
            case 'DeepSeekApiKey':
            case 'AimlApiKey':
            case 'LocalModelConfig':
                return <Brain className="w-4 h-4" />;
            default:
                return <Key className="w-4 h-4" />;
        }
    };

    const getCredentialTypeName = (type: string) => {
        switch (type) {
            case 'OpenAiApiKey':
                return 'OpenAI API Key';
            case 'ClaudeApiKey':
                return 'Claude API Key';
            case 'DeepSeekApiKey':
                return 'DeepSeek API Key';
            case 'AimlApiKey':
                return 'AIML API Key';
            case 'LocalModelConfig':
                return 'Local Model Config';
            case 'N8nApiKey':
                return 'N8N API Key'; // Should not appear in Settings, but just in case
            default:
                return type.replace(/([A-Z])/g, ' $1').trim();
        }
    };

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="mb-6"></div>

            <Tabs defaultValue="credentials" className="space-y-6">
                <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="credentials" className="flex items-center gap-2">
                        <Brain className="w-4 h-4" />
                        AI Providers
                    </TabsTrigger>
                    <TabsTrigger value="ai" className="flex items-center gap-2">
                        <Brain className="w-4 h-4" />
                        AI Settings
                    </TabsTrigger>
                    <TabsTrigger value="appearance" className="flex items-center gap-2">
                        <Palette className="w-4 h-4" />
                        Appearance
                    </TabsTrigger>
                    <TabsTrigger value="general" className="flex items-center gap-2">
                        <SettingsIcon className="w-4 h-4" />
                        General
                    </TabsTrigger>
                </TabsList>

                {/* AI API Keys Tab */}
                <TabsContent value="credentials" className="space-y-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <h2 className="text-xl font-semibold">AI Provider API Keys</h2>
                            <p className="text-muted-foreground">
                                Configure API keys for AI providers like OpenAI, Claude, and others to enable AI-powered workflow generation
                            </p>
                        </div>

                        <Dialog open={showCreateCredDialog} onOpenChange={setShowCreateCredDialog}>
                            <DialogTrigger asChild>
                                <Button>
                                    <Plus className="w-4 h-4 mr-2" />
                                    Add AI Provider
                                </Button>
                            </DialogTrigger>
                            <DialogContent className="sm:max-w-md">
                                <DialogHeader>
                                    <DialogTitle>Add AI Provider API Key</DialogTitle>
                                    <p className="text-sm text-muted-foreground">
                                        Add an API key for an AI provider to enable AI-powered features
                                    </p>
                                </DialogHeader>
                                <div className="space-y-4">
                                    <div>
                                        <Label>AI Provider</Label>
                                        <Select
                                            value={createCredData.credential_type}
                                            onValueChange={(value) => setCreateCredData(prev => ({ ...prev, credential_type: value }))}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select AI provider" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="OpenAiApiKey">
                                                    <div className="flex items-center gap-2">
                                                        <Brain className="w-4 h-4" />
                                                        OpenAI (GPT-4, GPT-3.5)
                                                    </div>
                                                </SelectItem>
                                                <SelectItem value="ClaudeApiKey">
                                                    <div className="flex items-center gap-2">
                                                        <Brain className="w-4 h-4" />
                                                        Anthropic Claude
                                                    </div>
                                                </SelectItem>
                                                <SelectItem value="DeepSeekApiKey">
                                                    <div className="flex items-center gap-2">
                                                        <Brain className="w-4 h-4" />
                                                        DeepSeek
                                                    </div>
                                                </SelectItem>
                                                <SelectItem value="AimlApiKey">
                                                    <div className="flex items-center gap-2">
                                                        <Brain className="w-4 h-4" />
                                                        AI/ML API
                                                    </div>
                                                </SelectItem>
                                                <SelectItem value="LocalModelConfig">
                                                    <div className="flex items-center gap-2">
                                                        <Brain className="w-4 h-4" />
                                                        Local Model
                                                    </div>
                                                </SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div>
                                        <Label>Display Name</Label>
                                        <Input
                                            value={createCredData.name}
                                            onChange={(e) => setCreateCredData(prev => ({ ...prev, name: e.target.value }))}
                                            placeholder="e.g., My OpenAI Key, Production Claude"
                                        />
                                    </div>
                                    <div>
                                        <Label>Description (Optional)</Label>
                                        <Input
                                            value={createCredData.description}
                                            onChange={(e) => setCreateCredData(prev => ({ ...prev, description: e.target.value }))}
                                            placeholder="e.g., For workflow generation, Testing purposes"
                                        />
                                    </div>
                                    <div>
                                        <Label>API Key</Label>
                                        <div className="relative">
                                            <Input
                                                type={showPassword.create ? 'text' : 'password'}
                                                value={createCredData.value}
                                                onChange={(e) => setCreateCredData(prev => ({ ...prev, value: e.target.value }))}
                                                placeholder="sk-... or your provider's API key"
                                            />
                                            <Button
                                                type="button"
                                                variant="ghost"
                                                size="sm"
                                                className="absolute right-0 top-0 h-full px-3"
                                                onClick={() => setShowPassword(prev => ({ ...prev, create: !prev.create }))}
                                            >
                                                {showPassword.create ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                                            </Button>
                                        </div>
                                        <p className="text-xs text-muted-foreground mt-1">
                                            Your API key is encrypted and stored securely
                                        </p>
                                    </div>
                                    <div className="flex justify-end space-x-2">
                                        <Button variant="outline" onClick={() => setShowCreateCredDialog(false)}>
                                            Cancel
                                        </Button>
                                        <Button onClick={handleCreateCredential} disabled={creating}>
                                            {creating ? <LoadingSpinner size="sm" /> : 'Add API Key'}
                                        </Button>
                                    </div>
                                </div>
                            </DialogContent>
                        </Dialog>

                        {/* Edit Credential Dialog */}
                        <Dialog open={showEditCredDialog} onOpenChange={setShowEditCredDialog}>
                            <DialogContent className="sm:max-w-md">
                                <DialogHeader>
                                    <DialogTitle>Edit API Key</DialogTitle>
                                    <p className="text-sm text-muted-foreground">
                                        Update the API key for {editingCredential?.name}
                                    </p>
                                </DialogHeader>
                                <div className="space-y-4">
                                    <div>
                                        <Label>Provider</Label>
                                        <div className="flex items-center gap-2 p-2 bg-muted rounded-md">
                                            {editingCredential && getCredentialTypeIcon(editingCredential.credential_type)}
                                            <span className="text-sm">
                                                {editingCredential && getCredentialTypeName(editingCredential.credential_type)}
                                            </span>
                                        </div>
                                    </div>
                                    <div>
                                        <Label>Display Name</Label>
                                        <div className="p-2 bg-muted rounded-md text-sm">
                                            {editingCredential?.name}
                                        </div>
                                    </div>
                                    <div>
                                        <Label>New API Key</Label>
                                        <div className="relative">
                                            <Input
                                                type={showPassword.edit ? 'text' : 'password'}
                                                value={editCredData.value}
                                                onChange={(e) => setEditCredData(prev => ({ ...prev, value: e.target.value }))}
                                                placeholder="Enter new API key"
                                            />
                                            <Button
                                                type="button"
                                                variant="ghost"
                                                size="sm"
                                                className="absolute right-0 top-0 h-full px-3"
                                                onClick={() => setShowPassword(prev => ({ ...prev, edit: !prev.edit }))}
                                            >
                                                {showPassword.edit ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                                            </Button>
                                        </div>
                                        <p className="text-xs text-muted-foreground mt-1">
                                            Your API key is encrypted and stored securely
                                        </p>
                                    </div>
                                    <div className="flex justify-end space-x-2">
                                        <Button variant="outline" onClick={() => setShowEditCredDialog(false)}>
                                            Cancel
                                        </Button>
                                        <Button onClick={handleUpdateCredential} disabled={updating}>
                                            {updating ? <LoadingSpinner size="sm" /> : 'Update API Key'}
                                        </Button>
                                    </div>
                                </div>
                            </DialogContent>
                        </Dialog>
                    </div>



                    {/* Credentials List */}
                    <div className="grid gap-4">
                        {loading ? (
                            <div className="flex items-center justify-center py-8">
                                <LoadingSpinner size="lg" />
                            </div>
                        ) : credentials.length === 0 ? (
                            <Card>
                                <CardContent className="pt-6 text-center">
                                    <Brain className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                                    <h3 className="text-lg font-medium mb-2">No AI Providers Configured</h3>
                                    <p className="text-muted-foreground mb-4">
                                        Add API keys for AI providers to enable AI-powered workflow generation and assistance.
                                    </p>
                                    <div className="text-sm text-muted-foreground mb-4 space-y-1">
                                        <p>Supported providers:</p>
                                        <p>• OpenAI (GPT-4, GPT-3.5)</p>
                                        <p>• Anthropic Claude</p>
                                        <p>• DeepSeek</p>
                                        <p>• AI/ML API</p>
                                        <p>• Local Models</p>
                                    </div>
                                    <Dialog open={showCreateCredDialog} onOpenChange={setShowCreateCredDialog}>
                                        <DialogTrigger asChild>
                                            <Button>
                                                <Plus className="w-4 h-4 mr-2" />
                                                Add Your First AI Provider
                                            </Button>
                                        </DialogTrigger>
                                    </Dialog>
                                </CardContent>
                            </Card>
                        ) : (
                            credentials.map((credential) => (
                                <Card key={credential.id}>
                                    <CardHeader className="pb-3">
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-3">
                                                {getCredentialTypeIcon(credential.credential_type)}
                                                <div>
                                                    <CardTitle className="text-lg">{credential.name}</CardTitle>
                                                    <p className="text-sm text-muted-foreground">
                                                        {getCredentialTypeName(credential.credential_type)}
                                                    </p>
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <Badge variant="default">
                                                    Active
                                                </Badge>
                                                {credential.last_used && (
                                                    <Badge variant="outline">
                                                        Recently Used
                                                    </Badge>
                                                )}
                                            </div>
                                        </div>
                                        {credential.description && (
                                            <p className="text-sm text-muted-foreground">{credential.description}</p>
                                        )}
                                    </CardHeader>
                                    <CardContent>
                                        <div className="flex items-center gap-2">
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                onClick={() => handleTestCredential(credential.id)}
                                            >
                                                Test Connection
                                            </Button>
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                onClick={() => handleEditCredential(credential)}
                                            >
                                                <Edit className="w-3 h-3 mr-1" />
                                                Edit
                                            </Button>
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                onClick={() => handleDeleteCredential(credential)}
                                                className="text-red-600 hover:text-red-700"
                                            >
                                                <Trash2 className="w-3 h-3 mr-1" />
                                                Remove
                                            </Button>
                                        </div>
                                    </CardContent>
                                </Card>
                            ))
                        )}
                    </div>
                </TabsContent>

                {/* AI Settings Tab */}
                <TabsContent value="ai" className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>AI Configuration</CardTitle>
                            <p className="text-muted-foreground">
                                Configure AI providers and model preferences
                            </p>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <Label>Default AI Provider</Label>
                                    <p className="text-sm text-muted-foreground">Choose your preferred AI provider for workflow generation</p>
                                </div>
                                <Select defaultValue="openai">
                                    <SelectTrigger className="w-48">
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="openai">OpenAI</SelectItem>
                                        <SelectItem value="claude">Claude</SelectItem>
                                        <SelectItem value="deepseek">DeepSeek</SelectItem>
                                        <SelectItem value="aiml">AIML</SelectItem>
                                        <SelectItem value="local">Local Model</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>

                            <div className="flex items-center justify-between">
                                <div>
                                    <Label>Enable AI Workflow Generation</Label>
                                    <p className="text-sm text-muted-foreground">Allow AI to generate workflows from natural language descriptions</p>
                                </div>
                                <Switch defaultChecked />
                            </div>

                            <div className="flex items-center justify-between">
                                <div>
                                    <Label>AI Suggestions</Label>
                                    <p className="text-sm text-muted-foreground">Show AI-powered suggestions for workflow optimization</p>
                                </div>
                                <Switch defaultChecked />
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>

                {/* Appearance Tab */}
                <TabsContent value="appearance" className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Appearance</CardTitle>
                            <p className="text-muted-foreground">
                                Customize the look and feel of the application
                            </p>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <Label>Theme</Label>
                                    <p className="text-sm text-muted-foreground">Choose your preferred color scheme</p>
                                </div>
                                <Select value={theme} onValueChange={setTheme}>
                                    <SelectTrigger className="w-48">
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="light">Light</SelectItem>
                                        <SelectItem value="dark">Dark</SelectItem>
                                        <SelectItem value="system">System</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>

                {/* General Tab */}
                <TabsContent value="general" className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>General Settings</CardTitle>
                            <p className="text-muted-foreground">
                                Configure general application behavior
                            </p>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <Label>Auto Sync</Label>
                                    <p className="text-sm text-muted-foreground">Automatically sync with N8N instances</p>
                                </div>
                                <Switch
                                    checked={settings.autoSync}
                                    onCheckedChange={(checked) => {
                                        const newSettings = { ...settings, autoSync: checked };
                                        saveSettings(newSettings);
                                    }}
                                    disabled={settingsLoading}
                                />
                            </div>

                            <div className="flex items-center justify-between">
                                <div>
                                    <Label>Desktop Notifications</Label>
                                    <p className="text-sm text-muted-foreground">Show notifications for workflow events</p>
                                </div>
                                <Switch
                                    checked={settings.notifications}
                                    onCheckedChange={(checked) => {
                                        const newSettings = { ...settings, notifications: checked };
                                        saveSettings(newSettings);
                                    }}
                                    disabled={settingsLoading}
                                />
                            </div>

                            <div className="flex items-center justify-between">
                                <div>
                                    <Label>Sync Interval (seconds)</Label>
                                    <p className="text-sm text-muted-foreground">How often to sync with N8N instances</p>
                                </div>
                                <Input
                                    type="number"
                                    value={settings.syncInterval}
                                    onChange={(e) => {
                                        const value = parseInt(e.target.value);
                                        if (value >= 10 && value <= 300) {
                                            const newSettings = { ...settings, syncInterval: value };
                                            saveSettings(newSettings);
                                        }
                                    }}
                                    onBlur={(e) => {
                                        const value = parseInt(e.target.value);
                                        if (value < 10 || value > 300) {
                                            const newSettings = { ...settings, syncInterval: Math.max(10, Math.min(300, value || 30)) };
                                            saveSettings(newSettings);
                                        }
                                    }}
                                    className="w-24"
                                    min="10"
                                    max="300"
                                    disabled={settingsLoading}
                                />
                            </div>

                            <div className="flex items-center justify-between">
                                <div>
                                    <Label>Auto Backup</Label>
                                    <p className="text-sm text-muted-foreground">Automatically backup application data</p>
                                </div>
                                <Switch
                                    checked={settings.autoBackup}
                                    onCheckedChange={(checked) => setSettings(prev => ({ ...prev, autoBackup: checked }))}
                                />
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>
            </Tabs>

            {/* Delete Confirmation Dialog */}
            {showDeleteConfirmDialog && deletingCredential && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
                        <h3 className="text-lg font-semibold mb-4">Confirm Deletion</h3>
                        <p className="text-gray-600 dark:text-gray-300 mb-6">
                            Are you sure you want to delete the credential <strong>{deletingCredential.name}</strong> ({deletingCredential.credential_type})?
                            This action cannot be undone.
                        </p>
                        <div className="flex justify-end gap-3">
                            <Button
                                variant="outline"
                                onClick={cancelDeleteCredential}
                                disabled={deleting}
                            >
                                Cancel
                            </Button>
                            <Button
                                variant="destructive"
                                onClick={confirmDeleteCredential}
                                disabled={deleting}
                            >
                                {deleting ? 'Deleting...' : 'Delete'}
                            </Button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}
