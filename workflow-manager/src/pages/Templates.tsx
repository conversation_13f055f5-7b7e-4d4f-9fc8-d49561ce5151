import { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Plus, Download, Eye, Star, Search, Filter, FileText, Zap } from 'lucide-react';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import toast from 'react-hot-toast';

interface Template {
    id: string;
    name: string;
    description: string | null;
    category: string;
    tags: string | null; // JSON array as string
    nodes: string; // JSON as string
    connections: string; // JSON as string
    settings: string | null; // JSON as string
    static_data: string | null; // JSON as string
    variables: string | null; // JSON object for template variables
    is_public: boolean;
    usage_count: number;
    created_at: string;
    updated_at: string;
    created_by: string | null; // AI model or user identifier
}

interface CreateTemplateData {
    name: string;
    description: string;
    category: string;
    tags: string[];
    nodes: any;
    connections: any;
    settings?: any;
    static_data?: any;
    variables?: any;
    is_public?: boolean;
    created_by?: string;
}

export default function Templates() {
    const navigate = useNavigate();
    const [templates, setTemplates] = useState<Template[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedCategory, setSelectedCategory] = useState<string>('all');
    const [showCreateDialog, setShowCreateDialog] = useState(false);

    const [createData, setCreateData] = useState<CreateTemplateData>({
        name: '',
        description: '',
        category: 'automation',
        tags: [],
        nodes: {},
        connections: {}
    });
    const [creating, setCreating] = useState(false);
    const [seeding, setSeeding] = useState(false);

    useEffect(() => {
        fetchTemplates();
    }, [selectedCategory]);

    const fetchTemplates = async () => {
        try {
            setLoading(true);
            const data = await invoke<Template[]>('list_templates', {
                category: selectedCategory === 'all' ? null : selectedCategory,
                limit: 50,
                offset: 0
            });
            setTemplates(data);
            setError(null);
        } catch (err) {
            setError('Failed to load templates.');
        } finally {
            setLoading(false);
        }
    };

    const handleCreateTemplate = async () => {
        if (!createData.name || !createData.description) {
            toast.error('Please fill in all required fields');
            return;
        }

        try {
            setCreating(true);
            await invoke('create_template', { create_data: createData });
            toast.success('Template created successfully');
            setShowCreateDialog(false);
            setCreateData({
                name: '',
                description: '',
                category: 'automation',
                tags: [],
                nodes: {},
                connections: {}
            });
            fetchTemplates();
        } catch (err) {
            toast.error('Failed to create template');
            console.error(err);
        } finally {
            setCreating(false);
        }
    };

    const handleUseTemplate = async (templateId: string) => {
        try {
            // First check if there are any N8N instances available
            const instances = await invoke<any[]>('list_n8n_instances');

            if (instances.length === 0) {
                toast.error('No N8N instances configured. Please add an N8N instance first before using templates.');
                return;
            }

            const template = await invoke<Template>('use_template', { template_id: templateId });
            toast.success(`Template "${template.name}" applied successfully! Redirecting to workflow creation...`);

            // Navigate to workflow creation page
            // You could pass template data as state if needed
            setTimeout(() => {
                navigate('/workflows/create');
            }, 1500);
        } catch (err) {
            toast.error('Failed to use template');
            console.error(err);
        }
    };

    const handlePreviewTemplate = (templateId: string) => {
        navigate(`/templates/${templateId}/preview`);
    };

    const handleSeedSampleTemplates = async () => {
        try {
            setSeeding(true);
            await invoke<string>('seed_sample_templates');
            toast.success('Sample templates created successfully!');
            fetchTemplates();
        } catch (err) {
            toast.error('Failed to create sample templates');
            console.error('Seed error:', err);
        } finally {
            setSeeding(false);
        }
    };

    const getCategoryIcon = (category: string) => {
        switch (category) {
            case 'automation':
                return <Zap className="w-4 h-4" />;
            case 'data_processing':
                return <FileText className="w-4 h-4" />;
            case 'integration':
                return <Download className="w-4 h-4" />;
            case 'notification':
                return <Star className="w-4 h-4" />;
            case 'ai_workflow':
                return <Star className="w-4 h-4" />;
            default:
                return <FileText className="w-4 h-4" />;
        }
    };

    const getCategoryColor = (category: string) => {
        switch (category) {
            case 'automation':
                return 'bg-blue-100 text-blue-800';
            case 'data_processing':
                return 'bg-green-100 text-green-800';
            case 'integration':
                return 'bg-purple-100 text-purple-800';
            case 'notification':
                return 'bg-orange-100 text-orange-800';
            case 'ai_workflow':
                return 'bg-gradient-to-r from-blue-100 to-teal-100 text-blue-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const filteredTemplates = templates.filter(template => {
        const tags = template.tags ? JSON.parse(template.tags) : [];
        return template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            (template.description && template.description.toLowerCase().includes(searchQuery.toLowerCase())) ||
            tags.some((tag: string) => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    });

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-96">
                <LoadingSpinner size="lg" />
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 className="text-2xl font-bold tracking-tight">Templates</h1>
                    <p className="text-muted-foreground">
                        Discover and create workflow templates to streamline your automation
                    </p>
                </div>

                <div className="flex flex-col sm:flex-row gap-2">
                    <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
                        <DialogTrigger asChild>
                            <Button>
                                <Plus className="w-4 h-4 mr-2" />
                                Create Template
                            </Button>
                        </DialogTrigger>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Create New Template</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                            <div>
                                <label className="text-sm font-medium">Template Name</label>
                                <Input
                                    value={createData.name}
                                    onChange={(e) => setCreateData(prev => ({ ...prev, name: e.target.value }))}
                                    placeholder="Email Notification Workflow"
                                />
                            </div>
                            <div>
                                <label className="text-sm font-medium">Description</label>
                                <Input
                                    value={createData.description}
                                    onChange={(e) => setCreateData(prev => ({ ...prev, description: e.target.value }))}
                                    placeholder="Sends email notifications when conditions are met"
                                />
                            </div>
                            <div>
                                <label className="text-sm font-medium">Category</label>
                                <Select
                                    value={createData.category}
                                    onValueChange={(value) => setCreateData(prev => ({ ...prev, category: value }))}
                                >
                                    <SelectTrigger>
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="automation">Automation</SelectItem>
                                        <SelectItem value="data_processing">Data Processing</SelectItem>
                                        <SelectItem value="integration">Integration</SelectItem>
                                        <SelectItem value="notification">Notification</SelectItem>
                                        <SelectItem value="ai_workflow">AI Workflow</SelectItem>
                                        <SelectItem value="other">Other</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="flex justify-end space-x-2">
                                <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                                    Cancel
                                </Button>
                                <Button onClick={handleCreateTemplate} disabled={creating}>
                                    {creating ? <LoadingSpinner size="sm" /> : 'Create Template'}
                                </Button>
                            </div>
                        </div>
                    </DialogContent>
                </Dialog>

                    <Button
                        variant="outline"
                        onClick={handleSeedSampleTemplates}
                        disabled={seeding}
                    >
                        {seeding ? <LoadingSpinner size="sm" /> : 'Add Sample Templates'}
                    </Button>
                </div>
            </div>

            {/* Search and Filters */}
            <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1 max-w-md">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                    <Input
                        placeholder="Search templates..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10"
                    />
                </div>

                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                    <SelectTrigger className="w-full sm:w-48">
                        <Filter className="w-4 h-4 mr-2" />
                        <SelectValue placeholder="All Categories" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="all">All Categories</SelectItem>
                        <SelectItem value="automation">Automation</SelectItem>
                        <SelectItem value="data_processing">Data Processing</SelectItem>
                        <SelectItem value="integration">Integration</SelectItem>
                        <SelectItem value="notification">Notification</SelectItem>
                        <SelectItem value="ai_workflow">AI Workflow</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                </Select>
            </div>

            {/* Error State */}
            {error && (
                <Card className="border-red-200 bg-red-50">
                    <CardContent className="pt-6">
                        <p className="text-red-600">{error}</p>
                    </CardContent>
                </Card>
            )}

            {/* Templates Grid */}
            <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                {filteredTemplates.map((template) => (
                    <Card key={template.id} className="group hover:shadow-lg transition-all duration-200 hover:-translate-y-1">
                        <CardHeader className="pb-4">
                            <div className="flex items-start justify-between mb-3">
                                <div className="flex items-center gap-2">
                                    {getCategoryIcon(template.category)}
                                    <Badge className={`${getCategoryColor(template.category)} text-xs`}>
                                        {template.category.replace('_', ' ')}
                                    </Badge>
                                </div>
                                {template.is_public && (
                                    <Badge variant="default" className="text-xs">Public</Badge>
                                )}
                            </div>
                            <div>
                                <CardTitle className="text-lg mb-2 group-hover:text-primary transition-colors">
                                    {template.name}
                                </CardTitle>
                                <p className="text-sm text-muted-foreground line-clamp-2">
                                    {template.description || 'No description available'}
                                </p>
                            </div>
                        </CardHeader>
                        <CardContent className="pt-0">
                            <div className="space-y-4">
                                {/* Stats */}
                                <div className="flex items-center justify-between text-sm text-muted-foreground">
                                    <div className="flex items-center gap-1">
                                        <Download className="w-3 h-3" />
                                        <span>{template.usage_count} uses</span>
                                    </div>
                                    <div className="text-xs">
                                        {new Date(template.created_at).toLocaleDateString()}
                                    </div>
                                </div>

                                {/* Tags */}
                                {(() => {
                                    const tags = template.tags ? JSON.parse(template.tags) : [];
                                    return tags.length > 0 && (
                                        <div className="flex flex-wrap gap-1">
                                            {tags.slice(0, 2).map((tag: string, index: number) => (
                                                <Badge key={index} variant="secondary" className="text-xs">
                                                    {tag}
                                                </Badge>
                                            ))}
                                            {tags.length > 2 && (
                                                <Badge variant="secondary" className="text-xs">
                                                    +{tags.length - 2}
                                                </Badge>
                                            )}
                                        </div>
                                    );
                                })()}

                                {/* Actions */}
                                <div className="flex items-center gap-2 pt-2">
                                    <Button
                                        size="sm"
                                        onClick={() => handleUseTemplate(template.id)}
                                        className="flex-1"
                                    >
                                        <Download className="w-3 h-3 mr-1" />
                                        Use
                                    </Button>

                                    <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => handlePreviewTemplate(template.id)}
                                        className="px-3"
                                    >
                                        <Eye className="w-3 h-3" />
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                ))}
            </div>

            {/* Empty State */}
            {filteredTemplates.length === 0 && !loading && !error && (
                <div className="text-center py-12">
                    <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-6">
                        <FileText className="w-12 h-12 text-muted-foreground" />
                    </div>
                    <h3 className="text-lg font-semibold mb-2">
                        {searchQuery || selectedCategory !== 'all'
                            ? 'No templates found'
                            : 'No templates yet'}
                    </h3>
                    <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                        {searchQuery || selectedCategory !== 'all'
                            ? 'Try adjusting your search criteria or browse all categories.'
                            : 'Create your first template to share workflows with others, or add some sample templates to get started.'}
                    </p>
                    <div className="flex flex-col sm:flex-row gap-3 justify-center">
                        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
                            <DialogTrigger asChild>
                                <Button>
                                    <Plus className="w-4 h-4 mr-2" />
                                    Create Template
                                </Button>
                            </DialogTrigger>
                        </Dialog>
                        {(!searchQuery && selectedCategory === 'all') && (
                            <Button
                                variant="outline"
                                onClick={handleSeedSampleTemplates}
                                disabled={seeding}
                            >
                                {seeding ? <LoadingSpinner size="sm" /> : 'Add Sample Templates'}
                            </Button>
                        )}
                    </div>
                </div>
            )}


        </div>
    );
}
