import { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { invoke } from '@tauri-apps/api/core';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Download, FileText, Eye, Calendar, Users } from 'lucide-react';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import toast from 'react-hot-toast';
import ReactFlow, {
    Controls,
    Background,
    useNodesState,
    useEdgesState,
    addEdge,
    Connection,
    Edge,
    Node,
    BackgroundVariant,
    MiniMap,
    Position
} from 'reactflow';

import 'reactflow/dist/style.css';

interface Template {
    id: string;
    name: string;
    description?: string;
    category: string;
    tags?: string;
    nodes: string;
    connections: string;
    settings?: string;
    static_data?: string;
    is_public: boolean;
    usage_count: number;
    created_by?: string;
    created_at: string;
    updated_at: string;
}

export default function TemplatePreview() {
    const { id } = useParams<{ id: string }>();
    const navigate = useNavigate();
    const [template, setTemplate] = useState<Template | null>(null);
    const [loading, setLoading] = useState(true);

    // ReactFlow state for template preview
    const [nodes, setNodes, onNodesChange] = useNodesState([]);
    const [edges, setEdges, onEdgesChange] = useEdgesState([]);

    const onConnect = useCallback(
        (params: Connection) => setEdges((eds) => addEdge(params, eds)),
        [setEdges]
    );

    // Transform template data to ReactFlow format
    const transformTemplateToReactFlow = useCallback((template: Template) => {
        let initialNodes: Node[] = [];
        let initialEdges: Edge[] = [];

        try {
            // Parse nodes from template
            const templateNodes = JSON.parse(template.nodes || '[]');
            const nodeNameToId = new Map<string, string>();

            // Transform nodes
            if (Array.isArray(templateNodes)) {
                initialNodes = templateNodes.map((node: any) => {
                    const nodeId = node.id || node.name || `node-${Math.random()}`;
                    nodeNameToId.set(node.name || nodeId, nodeId);
                    
                    return {
                        id: nodeId,
                        type: 'default',
                        position: {
                            x: node.position?.[0] || node.position?.x || Math.random() * 400,
                            y: node.position?.[1] || node.position?.y || Math.random() * 400
                        },
                        data: {
                            label: node.name || node.type || 'Unnamed Node',
                            nodeType: node.type,
                            parameters: node.parameters
                        },
                        style: {
                            background: '#fff',
                            border: '2px solid #0ea5e9',
                            borderRadius: '8px',
                            fontSize: '12px',
                            fontWeight: '500',
                            width: 150,
                            height: 40
                        },
                    };
                });
            }

            // Parse and transform connections
            const templateConnections = JSON.parse(template.connections || '{}');
            
            if (typeof templateConnections === 'object' && templateConnections !== null) {
                // Handle N8N-style connections
                Object.entries(templateConnections).forEach(([sourceNodeName, outputs]: [string, any]) => {
                    if (outputs && typeof outputs === 'object') {
                        Object.entries(outputs).forEach(([, connections]: [string, any]) => {
                            if (Array.isArray(connections)) {
                                connections.forEach((connectionGroup: any, outputIndex: number) => {
                                    if (Array.isArray(connectionGroup)) {
                                        connectionGroup.forEach((connection: any, connectionIndex: number) => {
                                            const sourceId = nodeNameToId.get(sourceNodeName) || sourceNodeName;
                                            const targetId = nodeNameToId.get(connection.node) || connection.node;
                                            
                                            if (sourceId && targetId && nodeNameToId.has(sourceNodeName) && nodeNameToId.has(connection.node)) {
                                                initialEdges.push({
                                                    id: `e${sourceId}-${targetId}-${outputIndex}-${connectionIndex}`,
                                                    source: sourceId,
                                                    target: targetId,
                                                    type: 'smoothstep',
                                                    style: { stroke: '#0ea5e9', strokeWidth: 2 },
                                                    animated: false
                                                });
                                            }
                                        });
                                    }
                                });
                            }
                        });
                    }
                });
            }
        } catch (error) {
            console.error('Error transforming template data:', error);
        }

        return { nodes: initialNodes, edges: initialEdges };
    }, []);

    // Handle using template
    const handleUseTemplate = async () => {
        try {
            // Check if there are any N8N instances
            const instances = await invoke<any[]>('list_n8n_instances');
            
            if (!instances || instances.length === 0) {
                toast.error('No N8N instances configured. Please add an N8N instance first before using templates.');
                return;
            }

            // Increment usage count
            await invoke('increment_template_usage', { id: template!.id });
            
            toast.success('Template usage recorded! Redirecting to workflow creation...');
            
            // Navigate to workflow creation with template data
            navigate('/workflows/create', { 
                state: { 
                    templateId: template!.id,
                    templateName: template!.name 
                } 
            });
        } catch (err) {
            console.error('Error using template:', err);
            toast.error('Failed to use template');
        }
    };

    useEffect(() => {
        const fetchTemplate = async () => {
            if (!id) return;
            
            try {
                const data = await invoke<Template>('get_template', { id });
                setTemplate(data);
                
                // Transform template data to ReactFlow format
                const { nodes: flowNodes, edges: flowEdges } = transformTemplateToReactFlow(data);
                setNodes(flowNodes);
                setEdges(flowEdges);
            } catch (err) {
                console.error('Failed to load template:', err);
                toast.error('Failed to load template');
                navigate('/templates');
            } finally {
                setLoading(false);
            }
        };

        fetchTemplate();
    }, [id, navigate, transformTemplateToReactFlow, setNodes, setEdges]);

    const getCategoryColor = (category: string) => {
        const colors: { [key: string]: string } = {
            automation: 'bg-blue-100 text-blue-800 border-blue-200',
            integration: 'bg-green-100 text-green-800 border-green-200',
            data_processing: 'bg-purple-100 text-purple-800 border-purple-200',
            notification: 'bg-orange-100 text-orange-800 border-orange-200',
            monitoring: 'bg-red-100 text-red-800 border-red-200',
            utility: 'bg-gray-100 text-gray-800 border-gray-200'
        };
        return colors[category] || colors.utility;
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-96">
                <LoadingSpinner size="lg" />
            </div>
        );
    }

    if (!template) {
        return (
            <div className="flex flex-col items-center justify-center min-h-96 text-center">
                <FileText className="w-16 h-16 text-muted-foreground mb-4" />
                <h2 className="text-xl font-semibold mb-2">Template Not Found</h2>
                <p className="text-muted-foreground mb-4">The template you're looking for doesn't exist.</p>
                <Button onClick={() => navigate('/templates')}>
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Templates
                </Button>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                    <Button variant="outline" onClick={() => navigate('/templates')}>
                        <ArrowLeft className="w-4 h-4 mr-2" />
                        Back to Templates
                    </Button>
                    <div>
                        <h1 className="text-2xl font-bold tracking-tight">{template.name}</h1>
                        <div className="flex items-center gap-2 mt-1">
                            <Badge className={getCategoryColor(template.category)}>
                                {template.category.replace('_', ' ')}
                            </Badge>
                            <span className="text-sm text-muted-foreground">
                                Used {template.usage_count} times
                            </span>
                        </div>
                    </div>
                </div>
                <Button onClick={handleUseTemplate}>
                    <Download className="w-4 h-4 mr-2" />
                    Use This Template
                </Button>
            </div>

            {/* Description */}
            {template.description && (
                <Card>
                    <CardContent className="pt-6">
                        <p className="text-muted-foreground">{template.description}</p>
                    </CardContent>
                </Card>
            )}

            {/* ReactFlow Visualization */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Eye className="w-5 h-5" />
                        Workflow Visualization
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div
                        style={{ width: '100%', height: '600px' }}
                        className="border rounded-lg bg-slate-50 dark:bg-slate-900"
                    >
                        {nodes.length > 0 ? (
                            <ReactFlow
                                nodes={nodes}
                                edges={edges}
                                onNodesChange={onNodesChange}
                                onEdgesChange={onEdgesChange}
                                onConnect={onConnect}
                                fitView
                                fitViewOptions={{ padding: 0.2 }}
                                attributionPosition="bottom-left"
                                proOptions={{ hideAttribution: true }}
                                defaultViewport={{ x: 0, y: 0, zoom: 1 }}
                                minZoom={0.1}
                                maxZoom={2}
                            >
                                <Controls
                                    position="top-left"
                                    showInteractive={false}
                                />
                                <Background
                                    variant={BackgroundVariant.Dots}
                                    gap={20}
                                    size={1}
                                    color="#94a3b8"
                                />
                                <MiniMap
                                    nodeColor="#0ea5e9"
                                    nodeStrokeWidth={3}
                                    position="bottom-right"
                                    style={{
                                        background: '#f8fafc',
                                        border: '1px solid #e2e8f0'
                                    }}
                                />
                            </ReactFlow>
                        ) : (
                            <div className="flex items-center justify-center h-full text-muted-foreground">
                                <div className="text-center">
                                    <FileText className="w-12 h-12 mx-auto mb-2 opacity-50" />
                                    <p>No workflow visualization available</p>
                                    <p className="text-sm">Template may not contain valid node data</p>
                                </div>
                            </div>
                        )}
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
