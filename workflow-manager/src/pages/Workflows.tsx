import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { invoke } from '@tauri-apps/api/core';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Plus, Edit, Rocket, Eye, Search, Filter } from 'lucide-react';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import toast from 'react-hot-toast';

interface Workflow {
    id: string;
    n8n_instance_id: string;
    n8n_workflow_id: string;
    name: string;
    is_active: boolean;
    tags?: string[] | null;
    nodes: any;
    connections: any;
    last_sync_at: string;
    n8n_instance?: {
        id: string;
        name: string;
        health_status: string;
        url: string;
    };
}

interface Template {
    id: string;
    name: string;
    description: string | null;
    category: string;
    tags: string | null; // JSON array as string
    nodes: string; // JSON as string
    connections: string; // JSON as string
    settings: string | null;
    static_data: string | null;
    variables: string | null;
    is_public: boolean;
    usage_count: number;
    created_at: string;
    updated_at: string;
    created_by: string | null;
}

// Unified item for display
interface WorkflowItem {
    id: string;
    name: string;
    description?: string;
    type: 'template' | 'deployed';
    tags: string[];
    category?: string;
    is_active?: boolean;
    usage_count?: number;
    instance_name?: string;
    created_at: string;
    updated_at: string;
    // Template data (if it's a template)
    template_id?: string;
    // Deployment data (if it's deployed)
    deployments?: Array<{
        id: string;
        instance_id: string;
        instance_name: string;
        is_active: boolean;
    }>;
}

interface N8nInstance {
    id: string;
    name: string;
    url: string;
    status: string;
    version?: string;
    description?: string;
    is_default: boolean;
    created_at: string;
    updated_at: string;
    last_ping?: string;
}

export default function Workflows() {
    const navigate = useNavigate();

    const [workflowItems, setWorkflowItems] = useState<WorkflowItem[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedCategory, setSelectedCategory] = useState<string>('all');

    // Deploy dialog state
    const [deployDialogOpen, setDeployDialogOpen] = useState(false);
    const [selectedTemplateId, setSelectedTemplateId] = useState<string | null>(null);
    const [selectedTemplateName, setSelectedTemplateName] = useState<string>('');
    const [instances, setInstances] = useState<N8nInstance[]>([]);
    const [selectedInstanceId, setSelectedInstanceId] = useState<string>('');
    const [deploying, setDeploying] = useState(false);

    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true);

                // Fetch both workflows and templates
                const [workflowData, templateData] = await Promise.all([
                    invoke<Workflow[]>('list_workflows', {
                        instance_id: null,
                        limit: null,
                        offset: null
                    }),
                    invoke<Template[]>('list_templates', {
                        category: null,
                        limit: null,
                        offset: null
                    })
                ]);



                // Create unified workflow items by grouping templates and their deployments
                const workflowMap = new Map<string, WorkflowItem>();

                // First, add all templates
                templateData.forEach(template => {
                    workflowMap.set(template.name, {
                        id: template.id,
                        name: template.name,
                        description: template.description || undefined,
                        type: 'template',
                        tags: template.tags ? JSON.parse(template.tags) : [],
                        category: template.category,
                        usage_count: template.usage_count,
                        created_at: template.created_at,
                        updated_at: template.updated_at,
                        template_id: template.id,
                        deployments: []
                    });
                });

                // Then, add deployments to existing templates or create new deployed items
                workflowData.forEach(workflow => {
                    const existingItem = workflowMap.get(workflow.name);

                    if (existingItem) {
                        // Add deployment to existing template
                        existingItem.deployments!.push({
                            id: workflow.id,
                            instance_id: workflow.n8n_instance_id,
                            instance_name: workflow.n8n_instance?.name || 'Unknown',
                            is_active: workflow.is_active
                        });
                        // Update type to deployed if it has deployments
                        existingItem.type = 'deployed';
                    } else {
                        // Create new item for deployed workflow without template
                        workflowMap.set(workflow.name, {
                            id: workflow.id,
                            name: workflow.name,
                            type: 'deployed',
                            tags: workflow.tags || [],
                            is_active: workflow.is_active,
                            instance_name: workflow.n8n_instance?.name,
                            created_at: workflow.last_sync_at,
                            updated_at: workflow.last_sync_at,
                            deployments: [{
                                id: workflow.id,
                                instance_id: workflow.n8n_instance_id,
                                instance_name: workflow.n8n_instance?.name || 'Unknown',
                                is_active: workflow.is_active
                            }]
                        });
                    }
                });

                const unifiedItems = Array.from(workflowMap.values());

                setWorkflowItems(unifiedItems);
                setError(null);
            } catch (err) {
                setError('Failed to load workflows and templates.');
                console.error(err);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []);



    // Load N8N instances for deploy dialog
    const loadInstances = async () => {
        try {
            const instanceData = await invoke<N8nInstance[]>('list_n8n_instances', {
                limit: null,
                offset: null
            });
            // Filter to show only active instances
            const activeInstances = instanceData.filter(instance =>
                instance.status === 'active' || instance.status === 'connected'
            );
            setInstances(activeInstances);
        } catch (err) {
            console.error('Failed to load instances:', err);
            toast.error('Failed to load N8N instances');
        }
    };

    // Handle deploy button click
    const handleDeployClick = async (templateId: string, templateName: string) => {
        setSelectedTemplateId(templateId);
        setSelectedTemplateName(templateName);
        await loadInstances();
        setDeployDialogOpen(true);
    };

    // Handle deploy confirmation
    const handleDeployConfirm = async () => {
        if (!selectedTemplateId || !selectedInstanceId) {
            toast.error('Please select an instance');
            return;
        }

        setDeploying(true);
        try {
            await invoke('deploy_template_to_instance', {
                template_id: selectedTemplateId,
                instance_id: selectedInstanceId
            });

            toast.success(`Template deployed successfully!`);
            setDeployDialogOpen(false);

            // Refresh the data to show the new deployed workflow
            const fetchData = async () => {
                try {
                    setLoading(true);

                    const [workflowData, templateData] = await Promise.all([
                        invoke<Workflow[]>('list_workflows', {
                            instance_id: null,
                            limit: null,
                            offset: null
                        }),
                        invoke<Template[]>('list_templates', {
                            category: null,
                            limit: null,
                            offset: null
                        })
                    ]);



                    // Create unified workflow items by grouping templates and their deployments
                    const workflowMap = new Map<string, WorkflowItem>();

                    // First, add all templates
                    templateData.forEach(template => {
                        workflowMap.set(template.name, {
                            id: template.id,
                            name: template.name,
                            description: template.description || undefined,
                            type: 'template',
                            tags: template.tags ? JSON.parse(template.tags) : [],
                            category: template.category,
                            usage_count: template.usage_count,
                            created_at: template.created_at,
                            updated_at: template.updated_at,
                            template_id: template.id,
                            deployments: []
                        });
                    });

                    // Then, add deployments to existing templates or create new deployed items
                    workflowData.forEach(workflow => {
                        const existingItem = workflowMap.get(workflow.name);

                        if (existingItem) {
                            // Add deployment to existing template
                            existingItem.deployments!.push({
                                id: workflow.id,
                                instance_id: workflow.n8n_instance_id,
                                instance_name: workflow.n8n_instance?.name || 'Unknown',
                                is_active: workflow.is_active
                            });
                            // Update type to deployed if it has deployments
                            existingItem.type = 'deployed';
                        } else {
                            // Create new item for deployed workflow without template
                            workflowMap.set(workflow.name, {
                                id: workflow.id,
                                name: workflow.name,
                                type: 'deployed',
                                tags: workflow.tags || [],
                                is_active: workflow.is_active,
                                instance_name: workflow.n8n_instance?.name,
                                created_at: workflow.last_sync_at,
                                updated_at: workflow.last_sync_at,
                                deployments: [{
                                    id: workflow.id,
                                    instance_id: workflow.n8n_instance_id,
                                    instance_name: workflow.n8n_instance?.name || 'Unknown',
                                    is_active: workflow.is_active
                                }]
                            });
                        }
                    });

                    const unifiedItems = Array.from(workflowMap.values());

                    setWorkflowItems(unifiedItems);
                } catch (err) {
                    console.error('Failed to refresh data:', err);
                } finally {
                    setLoading(false);
                }
            };

            await fetchData();
        } catch (err: any) {
            console.error('Failed to deploy template:', err);
            toast.error(`Failed to deploy template: ${err.toString()}`);
        } finally {
            setDeploying(false);
        }
    };

    // Filter items based on search and category
    const filteredItems = workflowItems.filter(item => {
        const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                            (item.description && item.description.toLowerCase().includes(searchQuery.toLowerCase()));
        const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;

        return matchesSearch && matchesCategory;
    });



    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-96">
                <LoadingSpinner size="lg" />
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold">Workflows</h1>
                    <p className="text-muted-foreground">
                        Create workflow templates and deploy them to your N8N instances
                    </p>
                </div>
                <Link to="/workflows/create">
                    <Button>
                        <Plus className="mr-2 h-4 w-4" />
                        Create Workflow
                    </Button>
                </Link>
            </div>

            {/* Search and Filters */}
            <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                    <Input
                        placeholder="Search workflows..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10"
                    />
                </div>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                    <SelectTrigger className="w-full sm:w-48">
                        <Filter className="mr-2 h-4 w-4" />
                        <SelectValue placeholder="Category" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="all">All Categories</SelectItem>
                        <SelectItem value="automation">Automation</SelectItem>
                        <SelectItem value="data-processing">Data Processing</SelectItem>
                        <SelectItem value="integration">Integration</SelectItem>
                        <SelectItem value="notification">Notification</SelectItem>
                        <SelectItem value="ai">AI & ML</SelectItem>
                    </SelectContent>
                </Select>
            </div>

            {/* Error State */}
            {error && (
                <Card className="border-red-200 bg-red-50">
                    <CardContent className="pt-6">
                        <p className="text-red-600">{error}</p>
                    </CardContent>
                </Card>
            )}

                    {/* Workflows Grid */}
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        {filteredItems.map((item) => (
                            <Card key={item.id} className="hover:shadow-lg transition-shadow">
                                <CardHeader className="pb-3">
                                    <div className="flex items-center justify-between">
                                        <CardTitle className="text-lg">{item.name}</CardTitle>
                                    </div>
                                    {item.description && (
                                        <p className="text-sm text-muted-foreground">
                                            {item.description}
                                        </p>
                                    )}
                                    {item.instance_name && (
                                        <p className="text-sm text-muted-foreground">
                                            Instance: {item.instance_name}
                                        </p>
                                    )}
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        {/* Tags */}
                                        {item.tags && item.tags.length > 0 && (
                                            <div className="flex flex-wrap gap-1">
                                                {item.tags.map((tag, index) => (
                                                    <Badge key={`${item.id}-tag-${index}-${tag}`} variant="outline" className="text-xs">
                                                        {tag}
                                                    </Badge>
                                                ))}
                                            </div>
                                        )}

                                        {/* Category for templates */}
                                        {item.category && (
                                            <div className="flex items-center gap-2">
                                                <span className="text-sm text-muted-foreground">Category:</span>
                                                <Badge variant="secondary" className="text-xs">{item.category}</Badge>
                                            </div>
                                        )}

                                        {/* Usage count for templates */}
                                        {item.usage_count !== undefined && (
                                            <div className="flex items-center gap-2">
                                                <span className="text-sm text-muted-foreground">Used {item.usage_count} times</span>
                                            </div>
                                        )}

                                        {/* Deployment status */}
                                        {item.deployments && item.deployments.length > 0 && (
                                            <div className="flex items-center gap-2">
                                                <span className="text-sm text-muted-foreground">Deployed to:</span>
                                                <div className="flex gap-1 flex-wrap">
                                                    {item.deployments.map((deployment, index) => (
                                                        <Badge
                                                            key={index}
                                                            variant={deployment.is_active ? "default" : "secondary"}
                                                            className="text-xs"
                                                        >
                                                            {deployment.instance_name} {deployment.is_active ? '(Active)' : '(Inactive)'}
                                                        </Badge>
                                                    ))}
                                                </div>
                                            </div>
                                        )}

                                        {/* Actions */}
                                        <div className="flex items-center gap-2 flex-wrap">
                                            {item.type === 'template' ? (
                                                // Template-only actions (not deployed yet)
                                                <div className="flex items-center gap-2">
                                                    <Button
                                                        size="sm"
                                                        variant="default"
                                                        onClick={() => navigate(`/templates/${item.template_id}/preview`)}
                                                    >
                                                        <Eye className="w-3 h-3 mr-1" />
                                                        Preview
                                                    </Button>
                                                    <Button
                                                        size="sm"
                                                        variant="outline"
                                                        onClick={() => handleDeployClick(item.template_id!, item.name)}
                                                    >
                                                        <Rocket className="w-3 h-3 mr-1" />
                                                        Deploy
                                                    </Button>
                                                    <Button
                                                        size="sm"
                                                        variant="outline"
                                                        onClick={() => navigate(`/templates/${item.template_id}/edit`)}
                                                    >
                                                        <Edit className="w-3 h-3" />
                                                    </Button>
                                                </div>
                                            ) : (
                                                // Deployed workflow actions
                                                <div className="flex items-center gap-2">
                                                    {/* Deploy more button if it has a template */}
                                                    {item.template_id && (
                                                        <Button
                                                            size="sm"
                                                            variant="outline"
                                                            onClick={() => handleDeployClick(item.template_id!, item.name)}
                                                        >
                                                            <Rocket className="w-3 h-3 mr-1" />
                                                            Deploy More
                                                        </Button>
                                                    )}

                                                    {/* View deployed workflow */}
                                                    {item.deployments && item.deployments.length === 1 && (
                                                        <Link to={`/workflows/${item.deployments[0].id}`}>
                                                            <Button size="sm" variant="outline">
                                                                <Eye className="w-3 h-3 mr-1" />
                                                                View
                                                            </Button>
                                                        </Link>
                                                    )}

                                                    {/* Edit template if available */}
                                                    {item.template_id && (
                                                        <Button
                                                            size="sm"
                                                            variant="outline"
                                                            onClick={() => navigate(`/templates/${item.template_id}/edit`)}
                                                        >
                                                            <Edit className="w-3 h-3" />
                                                        </Button>
                                                    )}

                                                    {/* Manage deployments button for multiple deployments */}
                                                    {item.deployments && item.deployments.length > 1 && (
                                                        <Button
                                                            size="sm"
                                                            variant="outline"
                                                            onClick={() => {
                                                                // Navigate to a deployments management page
                                                                console.log('Manage deployments for:', item.name);
                                                            }}
                                                        >
                                                            <Edit className="w-3 h-3 mr-1" />
                                                            Manage ({item.deployments.length})
                                                        </Button>
                                                    )}
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>

                    {/* Empty State */}
                    {filteredItems.length === 0 && !loading && !error && (
                        <Card>
                            <CardContent className="pt-6 text-center">
                                <p className="text-muted-foreground mb-4">No workflows found.</p>
                                <Link to="/workflows/create">
                                    <Button>
                                        <Plus className="w-4 h-4 mr-2" />
                                        Create Your First Workflow
                                    </Button>
                                </Link>
                            </CardContent>
                        </Card>
                    )}

            {/* Deploy Dialog */}
            <Dialog open={deployDialogOpen} onOpenChange={setDeployDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Deploy Template</DialogTitle>
                        <DialogDescription>
                            Deploy "{selectedTemplateName}" to an N8N instance
                        </DialogDescription>
                    </DialogHeader>

                    <div className="space-y-4">
                        <div>
                            <label className="text-sm font-medium">Select N8N Instance</label>
                            <Select value={selectedInstanceId} onValueChange={setSelectedInstanceId}>
                                <SelectTrigger className="mt-1">
                                    <SelectValue placeholder="Choose an instance..." />
                                </SelectTrigger>
                                <SelectContent>
                                    {instances.map((instance) => (
                                        <SelectItem key={instance.id} value={instance.id}>
                                            <div className="flex items-center justify-between w-full">
                                                <span>{instance.name}</span>
                                                <Badge
                                                    variant={instance.status === 'active' ? 'default' : 'secondary'}
                                                    className="ml-2"
                                                >
                                                    {instance.status}
                                                </Badge>
                                            </div>
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        {instances.length === 0 && (
                            <div className="text-center py-4">
                                <p className="text-muted-foreground mb-2">No active N8N instances available</p>
                                <p className="text-xs text-muted-foreground mb-3">Only active instances can be used for deployment</p>
                                <Link to="/instances">
                                    <Button variant="outline" size="sm">
                                        <Plus className="w-4 h-4 mr-2" />
                                        Manage Instances
                                    </Button>
                                </Link>
                            </div>
                        )}
                    </div>

                    <DialogFooter>
                        <Button
                            variant="outline"
                            onClick={() => setDeployDialogOpen(false)}
                            disabled={deploying}
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={handleDeployConfirm}
                            disabled={!selectedInstanceId || deploying || instances.length === 0}
                        >
                            {deploying ? (
                                <div className="flex items-center">
                                    <LoadingSpinner size="sm" className="mr-2" />
                                    Deploying...
                                </div>
                            ) : (
                                <div className="flex items-center">
                                    <Rocket className="w-4 h-4 mr-2" />
                                    Deploy
                                </div>
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
}
