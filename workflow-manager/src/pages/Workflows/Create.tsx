import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { invoke } from '@tauri-apps/api/core';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import toast from 'react-hot-toast';
import { Save, Sparkles, AlertCircle, Settings } from 'lucide-react';
import { LoadingSpinner } from '@/components/LoadingSpinner';



interface AiProvider {
    id: string;
    name: string;
    description: string;
    requires_api_key: boolean;
    models: string[];
}

interface GeneratedWorkflow {
    name: string;
    description: string;
    nodes: any;
    connections: any;
    settings?: any;
    tags: string[];
}

export default function WorkflowCreate() {
    const navigate = useNavigate();
    const [aiProviders, setAiProviders] = useState<AiProvider[]>([]);
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [generatedWorkflowData, setGeneratedWorkflowData] = useState<GeneratedWorkflow | null>(null);
    const [formData, setFormData] = useState({
        name: '',
        description: '',
        category: 'automation',
        tags: '',
        ai_prompt: '',
        ai_provider: '',
    });

    useEffect(() => {
        const fetchData = async () => {
            try {
                // Load AI providers
                const providerData = await invoke<AiProvider[]>('get_available_ai_providers');
                setAiProviders(providerData);
                if (providerData.length > 0) {
                    setFormData(prev => ({ ...prev, ai_provider: providerData[0].id }));
                }
            } catch (err) {
                console.error('Failed to load AI providers:', err);
                toast.error('Failed to load AI providers');
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setSaving(true);

        try {
            // Create template data object matching CreateTemplate struct
            const templateData = {
                name: formData.name,
                description: formData.description || null,
                category: formData.category,
                nodes: generatedWorkflowData?.nodes || {},
                connections: generatedWorkflowData?.connections || {},
                settings: generatedWorkflowData?.settings || null,
                static_data: null,
                variables: null,
                tags: formData.tags ? formData.tags.split(',').map(tag => tag.trim()).filter(Boolean) : [],
                is_public: true,
                created_by: formData.ai_provider ? `AI:${formData.ai_provider}` : null,
            };

            await invoke('create_template', templateData);

            toast.success('Workflow template created successfully! You can now deploy it to instances.');
            navigate('/templates');
        } catch (err: any) {
            console.error('Failed to create template:', err);
            toast.error(`Failed to create template: ${err.toString()}`);
        } finally {
            setSaving(false);
        }
    };

    const checkApiKeyExists = async (provider: string): Promise<boolean> => {
        try {
            // Use the correct credential ID format that matches the backend
            const keyName = `ai.${provider}`;
            const credential = await invoke<any>('get_credential_info', { id: keyName });
            return credential !== null;
        } catch {
            return false;
        }
    };

    const handleGenerateWithAI = async () => {
        if (!formData.ai_prompt.trim()) {
            toast.error('Please enter a workflow description');
            return;
        }

        if (!formData.ai_provider) {
            toast.error('Please select an AI provider');
            return;
        }

        // Check if API key exists for non-local providers
        if (formData.ai_provider !== 'local') {
            const hasApiKey = await checkApiKeyExists(formData.ai_provider);
            if (!hasApiKey) {
                toast.error(
                    `No API key found for ${formData.ai_provider}. Please configure your API key in Settings.`,
                    {
                        duration: 8000,
                        icon: '🔑',
                    }
                );
                return;
            }
        }

        setSaving(true);
        try {
            const generatedWorkflow = await invoke<GeneratedWorkflow>('generate_workflow_with_ai', {
                provider: formData.ai_provider,
                description: formData.ai_prompt,
                requirements: [],
                preferred_nodes: null,
                complexity: 'medium',
            });

            // Store the generated workflow data for later use
            setGeneratedWorkflowData(generatedWorkflow);

            // Update form with generated data
            setFormData(prev => ({
                ...prev,
                name: generatedWorkflow.name || prev.name,
                description: generatedWorkflow.description || prev.description,
                tags: generatedWorkflow.tags ? generatedWorkflow.tags.join(', ') : prev.tags,
            }));

            toast.success('Workflow generated successfully! Click "Create Workflow" to save it.');
        } catch (err: any) {
            console.error('Failed to generate workflow with AI:', err);

            // Check for specific error messages
            const errorMessage = err.toString();
            if (errorMessage.includes('Failed to get API key') || errorMessage.includes('No API key')) {
                toast.error(
                    `No API key configured for ${formData.ai_provider}. Please configure your API key in Settings.`,
                    { duration: 6000 }
                );
            } else if (errorMessage.includes('401 Unauthorized') || errorMessage.includes('status: 401')) {
                toast.error(
                    `Invalid or expired API key for ${formData.ai_provider}. Please check your API key in Settings.`,
                    { duration: 6000 }
                );
            } else if (errorMessage.includes('403 Forbidden') || errorMessage.includes('status: 403')) {
                toast.error(
                    `Access denied for ${formData.ai_provider}. Please check your API key permissions.`,
                    { duration: 6000 }
                );
            } else if (errorMessage.includes('429 Too Many Requests') || errorMessage.includes('status: 429')) {
                toast.error(
                    `Rate limit exceeded for ${formData.ai_provider}. Please try again later.`,
                    { duration: 6000 }
                );
            } else if (errorMessage.includes('Unsupported AI provider')) {
                toast.error('Selected AI provider is not supported');
            } else if (errorMessage.includes('timeout') || errorMessage.includes('network')) {
                toast.error('Network timeout. Please check your internet connection and try again.');
            } else {
                toast.error(`Failed to generate workflow: ${errorMessage}`);
            }
        } finally {
            setSaving(false);
        }
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-96">
                <LoadingSpinner size="lg" />
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="mb-6">
                <h1 className="text-3xl font-bold">Create Workflow Template</h1>
                <p className="text-muted-foreground mt-2">
                    Generate or create a reusable workflow template that can be deployed to multiple N8N instances.
                </p>
            </div>

            <div className="grid gap-6 md:grid-cols-2">
                {/* AI Generation */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Sparkles className="w-5 h-5" />
                            AI Workflow Generation
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div>
                            <Label htmlFor="ai_provider">AI Provider</Label>
                            <Select
                                value={formData.ai_provider}
                                onValueChange={(value) => setFormData(prev => ({ ...prev, ai_provider: value }))}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Select AI provider" />
                                </SelectTrigger>
                                <SelectContent>
                                    {aiProviders.map((provider) => (
                                        <SelectItem key={provider.id} value={provider.id}>
                                            <div className="flex items-center gap-2">
                                                <span>{provider.name}</span>
                                                {provider.requires_api_key && (
                                                    <span className="text-xs text-muted-foreground">(API key required)</span>
                                                )}
                                            </div>
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            {!formData.ai_provider && aiProviders.length === 0 && (
                                <div className="flex items-center justify-between text-sm text-amber-600 bg-amber-50 p-3 rounded-md">
                                    <div className="flex items-center gap-2">
                                        <AlertCircle className="w-4 h-4" />
                                        <span>No AI providers available. Configure API keys in Settings.</span>
                                    </div>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => navigate('/settings')}
                                        className="text-amber-700 border-amber-300 hover:bg-amber-100"
                                    >
                                        <Settings className="w-3 h-3 mr-1" />
                                        Settings
                                    </Button>
                                </div>
                            )}
                        </div>

                        <div>
                            <Label htmlFor="ai_prompt">Describe your workflow</Label>
                            <Textarea
                                id="ai_prompt"
                                placeholder="e.g., Create a workflow that monitors RSS feeds and sends notifications to Slack when new articles are published..."
                                value={formData.ai_prompt}
                                onChange={(e) => setFormData(prev => ({ ...prev, ai_prompt: e.target.value }))}
                                rows={4}
                            />
                        </div>

                        <Button
                            onClick={handleGenerateWithAI}
                            disabled={!formData.ai_prompt.trim() || !formData.ai_provider || saving}
                            className="w-full"
                        >
                            {saving ? (
                                <LoadingSpinner size="sm" className="mr-2" />
                            ) : (
                                <Sparkles className="w-4 h-4 mr-2" />
                            )}
                            Generate with AI
                        </Button>
                    </CardContent>
                </Card>

                {/* Manual Form */}
                <Card>
                    <CardHeader>
                        <CardTitle>Workflow Template Details</CardTitle>
                        <p className="text-sm text-muted-foreground">
                            Create a reusable workflow template that can be deployed to multiple N8N instances.
                        </p>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div>
                                <Label htmlFor="category">Template Category</Label>
                                <Select
                                    value={formData.category}
                                    onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select a category" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="automation">Automation</SelectItem>
                                        <SelectItem value="data_processing">Data Processing</SelectItem>
                                        <SelectItem value="notifications">Notifications</SelectItem>
                                        <SelectItem value="integrations">Integrations</SelectItem>
                                        <SelectItem value="monitoring">Monitoring</SelectItem>
                                        <SelectItem value="other">Other</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>

                            <div>
                                <Label htmlFor="name">Workflow Name</Label>
                                <Input
                                    id="name"
                                    value={formData.name}
                                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                                    placeholder="Enter workflow name"
                                    required
                                />
                            </div>

                            <div>
                                <Label htmlFor="description">Description</Label>
                                <Textarea
                                    id="description"
                                    value={formData.description}
                                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                                    placeholder="Describe what this workflow does"
                                    rows={3}
                                />
                            </div>

                            <div>
                                <Label htmlFor="tags">Tags (comma-separated)</Label>
                                <Input
                                    id="tags"
                                    value={formData.tags}
                                    onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}
                                    placeholder="automation, notifications, data-processing"
                                />
                            </div>

                            {generatedWorkflowData && (
                                <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                                    <div className="flex items-center">
                                        <Sparkles className="w-4 h-4 text-green-600 mr-2" />
                                        <span className="text-sm text-green-800">
                                            AI workflow generated! Ready to save as template with {generatedWorkflowData.nodes ? Object.keys(generatedWorkflowData.nodes).length : 0} nodes.
                                        </span>
                                    </div>
                                </div>
                            )}

                            <Button type="submit" disabled={saving || !formData.name.trim()} className="w-full">
                                {saving ? (
                                    <LoadingSpinner size="sm" className="mr-2" />
                                ) : (
                                    <Save className="w-4 h-4 mr-2" />
                                )}
                                {generatedWorkflowData ? 'Save AI-Generated Template' : 'Save Template'}
                            </Button>

                            <div className="text-center">
                                <p className="text-sm text-muted-foreground">
                                    After creating the template, you can deploy it to N8N instances from the Templates page.
                                </p>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}
