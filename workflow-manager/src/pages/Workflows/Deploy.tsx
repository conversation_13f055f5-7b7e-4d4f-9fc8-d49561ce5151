import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { invoke } from '@tauri-apps/api/core';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import toast from 'react-hot-toast';
import { Save, Rocket, AlertCircle, CheckCircle2, Clock } from 'lucide-react';
import { LoadingSpinner } from '@/components/LoadingSpinner';

interface Template {
    id: string;
    name: string;
    description: string | null;
    category: string;
    tags: string | null;
    nodes: string;
    connections: string;
    settings: string | null;
    static_data: string | null;
    variables: string | null;
    is_public: boolean;
    usage_count: number;
    created_at: string;
    updated_at: string;
    created_by: string | null;
}

interface N8nInstance {
    id: number;
    name: string;
    url: string;
    health_status: string;
}

interface DeploymentStatus {
    instance_id: number;
    instance_name: string;
    status: 'pending' | 'deploying' | 'deployed' | 'failed';
    workflow_id?: string;
    error?: string;
}

export default function WorkflowDeploy() {
    const navigate = useNavigate();
    const [templates, setTemplates] = useState<Template[]>([]);
    const [instances, setInstances] = useState<N8nInstance[]>([]);
    const [loading, setLoading] = useState(true);
    const [deploying, setDeploying] = useState(false);
    const [selectedTemplate, setSelectedTemplate] = useState<string>('');
    const [selectedInstances, setSelectedInstances] = useState<number[]>([]);
    const [deploymentStatuses, setDeploymentStatuses] = useState<DeploymentStatus[]>([]);

    useEffect(() => {
        const fetchData = async () => {
            try {
                // Load templates
                const templateData = await invoke<Template[]>('list_templates');
                setTemplates(templateData);

                // Load N8N instances
                const instanceData = await invoke<N8nInstance[]>('list_n8n_instances');
                setInstances(instanceData);
            } catch (err) {
                console.error('Failed to load data:', err);
                toast.error('Failed to load templates and instances');
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    const handleInstanceToggle = (instanceId: number, checked: boolean) => {
        if (checked) {
            setSelectedInstances(prev => [...prev, instanceId]);
        } else {
            setSelectedInstances(prev => prev.filter(id => id !== instanceId));
        }
    };

    const handleDeploy = async () => {
        if (!selectedTemplate || selectedInstances.length === 0) {
            toast.error('Please select a template and at least one instance');
            return;
        }

        setDeploying(true);
        
        // Initialize deployment statuses
        const initialStatuses: DeploymentStatus[] = selectedInstances.map(instanceId => {
            const instance = instances.find(i => i.id === instanceId);
            return {
                instance_id: instanceId,
                instance_name: instance?.name || `Instance ${instanceId}`,
                status: 'pending'
            };
        });
        setDeploymentStatuses(initialStatuses);

        try {
            // Deploy to each selected instance
            for (const instanceId of selectedInstances) {
                const instance = instances.find(i => i.id === instanceId);
                
                // Update status to deploying
                setDeploymentStatuses(prev => 
                    prev.map(status => 
                        status.instance_id === instanceId 
                            ? { ...status, status: 'deploying' }
                            : status
                    )
                );

                try {
                    const result = await invoke('deploy_template_to_instance', {
                        template_id: selectedTemplate,
                        instance_id: instanceId
                    });

                    // Update status to deployed
                    setDeploymentStatuses(prev => 
                        prev.map(status => 
                            status.instance_id === instanceId 
                                ? { ...status, status: 'deployed', workflow_id: result as string }
                                : status
                        )
                    );

                    toast.success(`Deployed to ${instance?.name || `Instance ${instanceId}`}`);
                } catch (err: any) {
                    // Update status to failed
                    setDeploymentStatuses(prev => 
                        prev.map(status => 
                            status.instance_id === instanceId 
                                ? { ...status, status: 'failed', error: err.toString() }
                                : status
                        )
                    );

                    toast.error(`Failed to deploy to ${instance?.name || `Instance ${instanceId}`}: ${err.toString()}`);
                }
            }

            const successCount = deploymentStatuses.filter(s => s.status === 'deployed').length;
            if (successCount > 0) {
                toast.success(`Successfully deployed to ${successCount} instance(s)`);
            }
        } catch (err: any) {
            console.error('Deployment failed:', err);
            toast.error(`Deployment failed: ${err.toString()}`);
        } finally {
            setDeploying(false);
        }
    };

    const getStatusIcon = (status: DeploymentStatus['status']) => {
        switch (status) {
            case 'pending':
                return <Clock className="w-4 h-4 text-gray-500" />;
            case 'deploying':
                return <LoadingSpinner size="sm" />;
            case 'deployed':
                return <CheckCircle2 className="w-4 h-4 text-green-500" />;
            case 'failed':
                return <AlertCircle className="w-4 h-4 text-red-500" />;
        }
    };

    const getStatusColor = (status: DeploymentStatus['status']) => {
        switch (status) {
            case 'pending':
                return 'text-gray-600';
            case 'deploying':
                return 'text-blue-600';
            case 'deployed':
                return 'text-green-600';
            case 'failed':
                return 'text-red-600';
        }
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-96">
                <LoadingSpinner size="lg" />
            </div>
        );
    }

    const selectedTemplateData = templates.find(t => t.id === selectedTemplate);

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="mb-6">
                <h1 className="text-3xl font-bold">Deploy Workflow Template</h1>
                <p className="text-muted-foreground mt-2">
                    Select a template and deploy it to one or more N8N instances.
                </p>
            </div>

            <div className="grid gap-6 md:grid-cols-2">
                {/* Template Selection */}
                <Card>
                    <CardHeader>
                        <CardTitle>Select Template</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div>
                            <Label htmlFor="template">Workflow Template</Label>
                            <Select
                                value={selectedTemplate}
                                onValueChange={setSelectedTemplate}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Choose a template to deploy" />
                                </SelectTrigger>
                                <SelectContent>
                                    {templates.map((template) => (
                                        <SelectItem key={template.id} value={template.id}>
                                            <div className="flex flex-col">
                                                <span className="font-medium">{template.name}</span>
                                                <span className="text-sm text-muted-foreground">
                                                    {template.category} • Used {template.usage_count} times
                                                </span>
                                            </div>
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        {selectedTemplateData && (
                            <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                                <h4 className="font-medium text-blue-900">{selectedTemplateData.name}</h4>
                                {selectedTemplateData.description && (
                                    <p className="text-sm text-blue-700 mt-1">{selectedTemplateData.description}</p>
                                )}
                                <div className="flex items-center gap-2 mt-2">
                                    <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                        {selectedTemplateData.category}
                                    </span>
                                    {selectedTemplateData.created_by && (
                                        <span className="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded">
                                            {selectedTemplateData.created_by}
                                        </span>
                                    )}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Instance Selection */}
                <Card>
                    <CardHeader>
                        <CardTitle>Target Instances</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="space-y-3">
                            {instances.map((instance) => (
                                <div key={instance.id} className="flex items-center space-x-3 p-3 border rounded-md">
                                    <Checkbox
                                        id={`instance-${instance.id}`}
                                        checked={selectedInstances.includes(instance.id)}
                                        onCheckedChange={(checked) => handleInstanceToggle(instance.id, checked as boolean)}
                                    />
                                    <div className="flex-1">
                                        <Label htmlFor={`instance-${instance.id}`} className="font-medium">
                                            {instance.name}
                                        </Label>
                                        <p className="text-sm text-muted-foreground">{instance.url}</p>
                                        <span className={`text-xs px-2 py-1 rounded ${
                                            instance.health_status === 'healthy' 
                                                ? 'bg-green-100 text-green-800' 
                                                : 'bg-red-100 text-red-800'
                                        }`}>
                                            {instance.health_status}
                                        </span>
                                    </div>
                                </div>
                            ))}
                        </div>

                        {instances.length === 0 && (
                            <div className="text-center py-8 text-muted-foreground">
                                <AlertCircle className="w-8 h-8 mx-auto mb-2" />
                                <p>No N8N instances configured</p>
                                <Button 
                                    variant="outline" 
                                    size="sm" 
                                    className="mt-2"
                                    onClick={() => navigate('/instances')}
                                >
                                    Add Instance
                                </Button>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>

            {/* Deployment Status */}
            {deploymentStatuses.length > 0 && (
                <Card>
                    <CardHeader>
                        <CardTitle>Deployment Status</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-3">
                            {deploymentStatuses.map((status) => (
                                <div key={status.instance_id} className="flex items-center justify-between p-3 border rounded-md">
                                    <div className="flex items-center space-x-3">
                                        {getStatusIcon(status.status)}
                                        <div>
                                            <p className="font-medium">{status.instance_name}</p>
                                            <p className={`text-sm capitalize ${getStatusColor(status.status)}`}>
                                                {status.status}
                                            </p>
                                        </div>
                                    </div>
                                    {status.error && (
                                        <p className="text-sm text-red-600 max-w-md truncate" title={status.error}>
                                            {status.error}
                                        </p>
                                    )}
                                    {status.workflow_id && (
                                        <p className="text-sm text-green-600">
                                            ID: {status.workflow_id}
                                        </p>
                                    )}
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            )}

            {/* Deploy Button */}
            <div className="flex justify-center">
                <Button 
                    onClick={handleDeploy}
                    disabled={deploying || !selectedTemplate || selectedInstances.length === 0}
                    size="lg"
                    className="min-w-48"
                >
                    {deploying ? (
                        <LoadingSpinner size="sm" className="mr-2" />
                    ) : (
                        <Rocket className="w-4 h-4 mr-2" />
                    )}
                    {deploying ? 'Deploying...' : `Deploy to ${selectedInstances.length} Instance(s)`}
                </Button>
            </div>
        </div>
    );
}
