import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { invoke } from '@tauri-apps/api/core';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { ArrowLeft, Save } from 'lucide-react';
import { LoadingSpinner } from '@/components/LoadingSpinner';

interface Workflow {
    id: string;
    name: string;
    description?: string;
    is_active: boolean;
    tags?: string; // JSON string from backend
}

export default function WorkflowEdit() {
    const { id } = useParams<{ id: string }>();
    const navigate = useNavigate();
    const [workflow, setWorkflow] = useState<Workflow | null>(null);
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [formData, setFormData] = useState({
        name: '',
        description: '',
        is_active: false,
        tags: '',
    });

    useEffect(() => {
        const fetchWorkflow = async () => {
            if (!id) return;
            
            try {
                const data = await invoke<Workflow | null>('get_workflow', { id: id });
                if (data) {
                    setWorkflow(data);
                    // Parse tags from JSON string
                    let tagsArray: string[] = [];
                    if (data.tags) {
                        try {
                            tagsArray = JSON.parse(data.tags);
                        } catch (e) {
                            console.warn('Failed to parse tags JSON:', e);
                            tagsArray = [];
                        }
                    }

                    setFormData({
                        name: data.name,
                        description: data.description || '',
                        is_active: data.is_active,
                        tags: tagsArray.join(', '),
                    });
                } else {
                    setWorkflow(null);
                }
            } catch (err) {
                console.error('Failed to load workflow:', err);
                setWorkflow(null);
            } finally {
                setLoading(false);
            }
        };

        fetchWorkflow();
    }, [id]);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!id) return;

        setSaving(true);
        try {
            await invoke('update_workflow', {
                id: id,
                update_data: {
                    name: formData.name,
                    description: formData.description,
                    is_active: formData.is_active,
                    tags: formData.tags.split(',').map(tag => tag.trim()).filter(Boolean),
                }
            });
            navigate(`/workflows/${id}`);
        } catch (err) {
            console.error('Failed to update workflow:', err);
        } finally {
            setSaving(false);
        }
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-96">
                <LoadingSpinner size="lg" />
            </div>
        );
    }

    if (!workflow) {
        return (
            <div className="text-center">
                <h1 className="text-2xl font-bold">Workflow not found</h1>
                <Button onClick={() => navigate('/workflows')} className="mt-4">
                    Back to Workflows
                </Button>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div className="flex items-center gap-4">
                <Button variant="outline" size="sm" onClick={() => navigate(`/workflows/${id}`)}>
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Workflow
                </Button>
                <div>
                    <p className="text-muted-foreground">Update workflow details and settings</p>
                </div>
            </div>

            <Card>
                <CardHeader>
                    <CardTitle>Workflow Details</CardTitle>
                </CardHeader>
                <CardContent>
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div>
                            <Label htmlFor="name">Workflow Name</Label>
                            <Input
                                id="name"
                                value={formData.name}
                                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                                placeholder="Enter workflow name"
                                required
                            />
                        </div>

                        <div>
                            <Label htmlFor="description">Description</Label>
                            <Textarea
                                id="description"
                                value={formData.description}
                                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                                placeholder="Describe what this workflow does"
                                rows={3}
                            />
                        </div>

                        <div>
                            <Label htmlFor="tags">Tags (comma-separated)</Label>
                            <Input
                                id="tags"
                                value={formData.tags}
                                onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}
                                placeholder="automation, notifications, data-processing"
                            />
                        </div>

                        <div className="flex items-center space-x-2">
                            <Switch
                                id="is_active"
                                checked={formData.is_active}
                                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
                            />
                            <Label htmlFor="is_active">Workflow is active</Label>
                        </div>

                        <div className="flex gap-2">
                            <Button type="submit" disabled={saving || !formData.name.trim()}>
                                {saving ? (
                                    <LoadingSpinner size="sm" className="mr-2" />
                                ) : (
                                    <Save className="w-4 h-4 mr-2" />
                                )}
                                Save Changes
                            </Button>
                            <Button type="button" variant="outline" onClick={() => navigate(`/workflows/${id}`)}>
                                Cancel
                            </Button>
                        </div>
                    </form>
                </CardContent>
            </Card>
        </div>
    );
}
