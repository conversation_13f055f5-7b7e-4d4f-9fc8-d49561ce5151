import { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { invoke } from '@tauri-apps/api/core';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Edit, Play, Pause, FileText } from 'lucide-react';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import ReactFlow, {
    Controls,
    Background,
    useNodesState,
    useEdgesState,
    addEdge,
    Connection,
    Edge,
    Node,
    BackgroundVariant,
    MiniMap,
    Position
} from 'reactflow';

import 'reactflow/dist/style.css';

interface Workflow {
    id: number;
    name: string;
    description?: string;
    is_active: boolean;
    tags: string[];
    nodes?: string;
    connections?: string;
    settings?: string;
    static_data?: string;
    n8n_instance?: {
        name: string;
        url: string;
    };
}

export default function WorkflowShow() {
    const { id } = useParams<{ id: string }>();
    const navigate = useNavigate();
    const [workflow, setWorkflow] = useState<Workflow | null>(null);
    const [loading, setLoading] = useState(true);

    // ReactFlow state for workflow visualization
    const [nodes, setNodes, onNodesChange] = useNodesState([]);
    const [edges, setEdges, onEdgesChange] = useEdgesState([]);

    const onConnect = useCallback(
        (params: Connection) => setEdges((eds) => addEdge(params, eds)),
        [setEdges]
    );

    // Transform workflow data to ReactFlow format
    const transformWorkflowToReactFlow = useCallback((workflow: Workflow) => {
        let initialNodes: Node[] = [];
        let initialEdges: Edge[] = [];

        try {
            // Parse nodes from workflow
            const workflowNodes = workflow.nodes ? JSON.parse(workflow.nodes) : [];
            const nodeNameToId = new Map<string, string>();

            // Transform nodes
            if (Array.isArray(workflowNodes)) {
                initialNodes = workflowNodes.map((node: any) => {
                    const nodeId = node.id || node.name || `node-${Math.random()}`;
                    nodeNameToId.set(node.name || nodeId, nodeId);

                    return {
                        id: nodeId,
                        type: 'default',
                        position: {
                            x: node.position?.[0] || node.position?.x || Math.random() * 400,
                            y: node.position?.[1] || node.position?.y || Math.random() * 400
                        },
                        data: {
                            label: node.name || node.type || 'Unnamed Node',
                            nodeType: node.type,
                            parameters: node.parameters
                        },
                        style: {
                            background: workflow.is_active ? '#dcfce7' : '#fff',
                            border: workflow.is_active ? '2px solid #16a34a' : '2px solid #0ea5e9',
                            borderRadius: '8px',
                            fontSize: '12px',
                            fontWeight: '500',
                            width: 150,
                            height: 40
                        },
                    };
                });
            }

            // Parse and transform connections
            const workflowConnections = workflow.connections ? JSON.parse(workflow.connections) : {};

            if (typeof workflowConnections === 'object' && workflowConnections !== null) {
                // Handle N8N-style connections
                Object.entries(workflowConnections).forEach(([sourceNodeName, outputs]: [string, any]) => {
                    if (outputs && typeof outputs === 'object') {
                        Object.entries(outputs).forEach(([, connections]: [string, any]) => {
                            if (Array.isArray(connections)) {
                                connections.forEach((connectionGroup: any, outputIndex: number) => {
                                    if (Array.isArray(connectionGroup)) {
                                        connectionGroup.forEach((connection: any, connectionIndex: number) => {
                                            const sourceId = nodeNameToId.get(sourceNodeName) || sourceNodeName;
                                            const targetId = nodeNameToId.get(connection.node) || connection.node;

                                            if (sourceId && targetId && nodeNameToId.has(sourceNodeName) && nodeNameToId.has(connection.node)) {
                                                initialEdges.push({
                                                    id: `e${sourceId}-${targetId}-${outputIndex}-${connectionIndex}`,
                                                    source: sourceId,
                                                    target: targetId,
                                                    type: 'smoothstep',
                                                    style: {
                                                        stroke: workflow.is_active ? '#16a34a' : '#0ea5e9',
                                                        strokeWidth: 2
                                                    },
                                                    animated: workflow.is_active
                                                });
                                            }
                                        });
                                    }
                                });
                            }
                        });
                    }
                });
            }
        } catch (error) {
            console.error('Error transforming workflow data:', error);
        }

        return { nodes: initialNodes, edges: initialEdges };
    }, []);

    useEffect(() => {
        const fetchWorkflow = async () => {
            if (!id) return;
            
            try {
                const data = await invoke<Workflow>('get_workflow', { id: parseInt(id) });
                setWorkflow(data);
            } catch (err) {
                console.error('Failed to load workflow:', err);
            } finally {
                setLoading(false);
            }
        };

        fetchWorkflow();
    }, [id]);

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-96">
                <LoadingSpinner size="lg" />
            </div>
        );
    }

    if (!workflow) {
        return (
            <div className="text-center">
                <h1 className="text-2xl font-bold">Workflow not found</h1>
                <Button onClick={() => navigate('/workflows')} className="mt-4">
                    Back to Workflows
                </Button>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div className="flex items-center gap-4">
                <div className="flex-1">
                    <h1 className="text-3xl font-bold tracking-tight">{workflow.name}</h1>
                    <p className="text-muted-foreground">{workflow.description}</p>
                </div>
                <div className="flex gap-2">
                    <Button variant="outline" onClick={() => navigate(`/workflows/${id}/edit`)}>
                        <Edit className="w-4 h-4 mr-2" />
                        Edit
                    </Button>
                    <Button>
                        {workflow.is_active ? <Pause className="w-4 h-4 mr-2" /> : <Play className="w-4 h-4 mr-2" />}
                        {workflow.is_active ? 'Pause' : 'Start'}
                    </Button>
                </div>
            </div>

            <Card>
                <CardHeader>
                    <CardTitle>Workflow Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="flex items-center gap-2">
                        <span className="font-medium">Status:</span>
                        <Badge variant={workflow.is_active ? 'default' : 'secondary'}>
                            {workflow.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                    </div>
                    
                    {workflow.n8n_instance && (
                        <div>
                            <span className="font-medium">Instance:</span>
                            <span className="ml-2">{workflow.n8n_instance.name}</span>
                        </div>
                    )}
                    
                    {workflow.tags.length > 0 && (
                        <div>
                            <span className="font-medium">Tags:</span>
                            <div className="flex flex-wrap gap-1 mt-1">
                                {workflow.tags.map((tag, index) => (
                                    <Badge key={index} variant="outline">
                                        {tag}
                                    </Badge>
                                ))}
                            </div>
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
}
