export type PageProps<T extends Record<string, unknown> = Record<string, unknown>> = T & {
    auth: {
        user: User;
    };
    filters: Filters; // Make filters always present
};

interface Filters {
    search: string | null;
    status: 'active' | 'inactive' | null;
    tag: string | null;
    min_executions: number | null;
    max_executions: number | null;
    last_executed_before: string | null;
    last_executed_after: string | null;
}

export interface User {
    id: number;
    name: string;
    email: string;
    email_verified_at: string;
}

export interface N8nInstance {
    id: string;
    name: string;
    url: string;
    status: string;
    version?: string;
    description?: string;
    is_default: boolean;
    created_at: string;
    updated_at: string;
    last_ping?: string;
}

export interface Workflow {
    id: number;
    n8n_instance_id: number;
    n8n_workflow_id: string;
    name: string;
    is_active: boolean;
    tags: string[];
    nodes: any[];
    connections: any[];
    last_sync_at: string;
    created_at: string;
    updated_at: string;
    n8nInstance?: N8nInstance;
    workflow_executions_count?: number;
}

export interface WorkflowExecution {
    id: number;
    workflow_id: number;
    n8n_execution_id: string;
    status: 'success' | 'failed' | 'running' | 'cancelled' | 'retrying';
    started_at: string;
    finished_at: string | null;
    execution_time: number | null;
    data: any;
    created_at: string;
    updated_at: string;
    workflow?: Workflow;
}

export interface Template {
    id: number;
    name: string;
    description: string;
    tags: string[];
    schema: {
        nodes: Array<{
            id: string;
            name: string;
            type: string;
            position: [number, number];
        }>;
        connections: Record<string, any>;
    };
    created_at: string;
    updated_at: string;
}
