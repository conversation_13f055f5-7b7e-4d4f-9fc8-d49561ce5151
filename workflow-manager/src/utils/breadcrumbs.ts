import { BreadcrumbItem } from '@/components/Breadcrumb';
import {
    Server,
    Workflow,
    FileText,
    Bot,
    Library,
    Settings,
    Users,
    Shield,
    User,
    Plus,
    Edit,
    Eye,
    BarChart3,
    Bell,
    MessageSquare
} from 'lucide-react';

interface RouteMapping {
    pattern: RegExp;
    generate: (matches: RegExpMatchArray, params?: any) => BreadcrumbItem[];
}

const routeMappings: RouteMapping[] = [
    // Dashboard
    {
        pattern: /^\/dashboard?$/,
        generate: () => []
    },

    // Instances
    {
        pattern: /^\/instances$/,
        generate: () => [
            { label: 'Instances', icon: Server }
        ]
    },
    {
        pattern: /^\/instances\/create$/,
        generate: () => [
            { label: 'Instances', href: route('instances.index'), icon: Server },
            { label: 'Create Instance', icon: Plus }
        ]
    },
    {
        pattern: /^\/instances\/(\d+)$/,
        generate: (matches, params) => [
            { label: 'Instances', href: route('instances.index'), icon: Server },
            { label: params?.instanceName || `Instance #${matches[1]}`, icon: Eye }
        ]
    },
    {
        pattern: /^\/instances\/(\d+)\/edit$/,
        generate: (matches, params) => [
            { label: 'Instances', href: route('instances.index'), icon: Server },
            { label: params?.instanceName || `Instance #${matches[1]}`, href: route('instances.show', matches[1]), icon: Server },
            { label: 'Edit', icon: Edit }
        ]
    },
    {
        pattern: /^\/instances\/(\d+)\/analytics$/,
        generate: (matches, params) => [
            { label: 'Instances', href: route('instances.index'), icon: Server },
            { label: params?.instanceName || `Instance #${matches[1]}`, href: route('instances.show', matches[1]), icon: Server },
            { label: 'Analytics', icon: BarChart3 }
        ]
    },

    // Workflows
    {
        pattern: /^\/workflows$/,
        generate: () => [
            { label: 'Workflows', icon: Workflow }
        ]
    },
    {
        pattern: /^\/workflows\/create$/,
        generate: () => [
            { label: 'Workflows', href: route('workflows.index'), icon: Workflow },
            { label: 'Create Workflow', icon: Plus }
        ]
    },
    {
        pattern: /^\/workflows\/(\d+)$/,
        generate: (matches, params) => [
            { label: 'Workflows', href: route('workflows.index'), icon: Workflow },
            { label: params?.workflowName || `Workflow #${matches[1]}`, icon: Eye }
        ]
    },
    {
        pattern: /^\/workflows\/(\d+)\/analytics$/,
        generate: (matches, params) => [
            { label: 'Workflows', href: route('workflows.index'), icon: Workflow },
            { label: params?.workflowName || `Workflow #${matches[1]}`, href: route('workflows.show', matches[1]), icon: Workflow },
            { label: 'Analytics', icon: BarChart3 }
        ]
    },

    // Templates
    {
        pattern: /^\/templates$/,
        generate: () => [
            { label: 'Templates', icon: FileText }
        ]
    },
    {
        pattern: /^\/templates\/create$/,
        generate: () => [
            { label: 'Templates', href: route('templates.index'), icon: FileText },
            { label: 'Create Template', icon: Plus }
        ]
    },
    {
        pattern: /^\/templates\/(\d+)\/edit$/,
        generate: (matches, params) => [
            { label: 'Templates', href: route('templates.index'), icon: FileText },
            { label: params?.templateName || `Template #${matches[1]}`, href: route('templates.show', matches[1]), icon: FileText },
            { label: 'Edit', icon: Edit }
        ]
    },
    {
        pattern: /^\/templates\/(\d+)$/,
        generate: (matches, params) => [
            { label: 'Templates', href: route('templates.index'), icon: FileText },
            { label: params?.templateName || `Template #${matches[1]}`, icon: Eye }
        ]
    },

    // AI Workflows
    {
        pattern: /^\/ai\/workflows$/,
        generate: () => [
            { label: 'AI Automation', icon: Bot },
            { label: 'Generator', icon: Bot }
        ]
    },
    {
        pattern: /^\/ai\/workflows\/(\d+)$/,
        generate: (matches) => [
            { label: 'AI Automation', icon: Bot },
            { label: 'Generator', href: route('ai.workflows.index'), icon: Bot },
            { label: 'Generation Status', icon: Settings }
        ]
    },
    {
        pattern: /^\/ai\/history\/browse$/,
        generate: () => [
            { label: 'AI Automation', icon: Bot },
            { label: 'Library', icon: Library }
        ]
    },

    // Users
    {
        pattern: /^\/users$/,
        generate: () => [
            { label: 'Users', icon: Users }
        ]
    },
    {
        pattern: /^\/users\/create$/,
        generate: () => [
            { label: 'Users', href: route('users.index'), icon: Users },
            { label: 'Create User', icon: Plus }
        ]
    },
    {
        pattern: /^\/users\/(\d+)$/,
        generate: (matches, params) => [
            { label: 'Users', href: route('users.index'), icon: Users },
            { label: params?.userName || `User #${matches[1]}`, icon: User }
        ]
    },
    {
        pattern: /^\/users\/(\d+)\/edit$/,
        generate: (matches, params) => [
            { label: 'Users', href: route('users.index'), icon: Users },
            { label: params?.userName || `User #${matches[1]}`, href: route('users.show', matches[1]), icon: User },
            { label: 'Edit', icon: Edit }
        ]
    },

    // Admin
    {
        pattern: /^\/admin\/ai-providers$/,
        generate: () => [
            { label: 'Admin', icon: Shield },
            { label: 'AI Providers', icon: Bot }
        ]
    },

    // Alerts
    {
        pattern: /^\/alerts\/channels$/,
        generate: () => [
            { label: 'Alerts', icon: Bell },
            { label: 'Channels', icon: MessageSquare }
        ]
    }
];

export function generateBreadcrumbs(pathname: string, params?: any): BreadcrumbItem[] {
    for (const mapping of routeMappings) {
        const matches = pathname.match(mapping.pattern);
        if (matches) {
            return mapping.generate(matches, params);
        }
    }

    // Fallback: generate breadcrumbs from path segments
    const segments = pathname.split('/').filter(Boolean);
    if (segments.length === 0) return [];

    return segments.map((segment, index) => ({
        label: segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, ' '),
        href: index < segments.length - 1 ? '/' + segments.slice(0, index + 1).join('/') : undefined
    }));
}

export function getPageTitle(pathname: string, params?: any): string {
    const breadcrumbs = generateBreadcrumbs(pathname, params);
    if (breadcrumbs.length === 0) return 'Dashboard';

    const lastBreadcrumb = breadcrumbs[breadcrumbs.length - 1];
    return lastBreadcrumb.label;
}
