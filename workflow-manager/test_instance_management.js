// Test script for N8N Instance Management functionality
// This script tests the basic CRUD operations for N8N instances

const { invoke } = require('@tauri-apps/api/core');

async function testInstanceManagement() {
    console.log('🧪 Testing N8N Instance Management...\n');

    try {
        // Test 1: List instances (should be empty initially)
        console.log('1. Testing list_n8n_instances...');
        const initialInstances = await invoke('list_n8n_instances');
        console.log(`   ✅ Found ${initialInstances.length} existing instances`);

        // Test 2: Create a new instance
        console.log('\n2. Testing create_n8n_instance...');
        const createData = {
            name: 'Test Instance',
            url: 'https://test.n8n.example.com',
            api_key: 'test_api_key_12345',
            is_default: true
        };
        
        const createdInstance = await invoke('create_n8n_instance', { createData });
        console.log(`   ✅ Created instance with ID: ${createdInstance.id}`);
        console.log(`   📝 Name: ${createdInstance.name}`);
        console.log(`   🔗 URL: ${createdInstance.url}`);
        console.log(`   ⭐ Default: ${createdInstance.is_default}`);

        // Test 3: Get the created instance
        console.log('\n3. Testing get_n8n_instance...');
        const retrievedInstance = await invoke('get_n8n_instance', { 
            id: createdInstance.id.toString() 
        });
        console.log(`   ✅ Retrieved instance: ${retrievedInstance.name}`);

        // Test 4: Update the instance
        console.log('\n4. Testing update_n8n_instance...');
        const updateData = {
            name: 'Updated Test Instance',
            url: 'https://updated.n8n.example.com',
            api_key: 'updated_api_key_67890',
            is_default: false
        };
        
        const updatedInstance = await invoke('update_n8n_instance', {
            id: createdInstance.id,
            update_data: updateData
        });
        console.log(`   ✅ Updated instance name: ${updatedInstance.name}`);
        console.log(`   🔗 Updated URL: ${updatedInstance.url}`);

        // Test 5: Set as default
        console.log('\n5. Testing set_default_n8n_instance...');
        await invoke('set_default_n8n_instance', { 
            id: createdInstance.id.toString() 
        });
        console.log(`   ✅ Set instance as default`);

        // Test 6: Get default instance
        console.log('\n6. Testing get_default_n8n_instance...');
        const defaultInstance = await invoke('get_default_n8n_instance');
        console.log(`   ✅ Default instance: ${defaultInstance.name}`);

        // Test 7: Test connection (will likely fail with test data)
        console.log('\n7. Testing ping_n8n_instance...');
        try {
            const pingResult = await invoke('ping_n8n_instance', { 
                id: createdInstance.id.toString() 
            });
            console.log(`   ✅ Ping result: ${pingResult}`);
        } catch (pingError) {
            console.log(`   ⚠️  Ping failed (expected with test data): ${pingError}`);
        }

        // Test 8: List instances again (should show our created instance)
        console.log('\n8. Testing list_n8n_instances again...');
        const finalInstances = await invoke('list_n8n_instances');
        console.log(`   ✅ Found ${finalInstances.length} instances total`);
        finalInstances.forEach((instance, index) => {
            console.log(`   ${index + 1}. ${instance.name} (${instance.url}) - Default: ${instance.is_default}`);
        });

        // Test 9: Delete the instance
        console.log('\n9. Testing delete_n8n_instance...');
        await invoke('delete_n8n_instance', { 
            id: createdInstance.id.toString() 
        });
        console.log(`   ✅ Deleted instance`);

        // Test 10: Verify deletion
        console.log('\n10. Verifying deletion...');
        const finalCheck = await invoke('list_n8n_instances');
        console.log(`   ✅ Instances after deletion: ${finalCheck.length}`);

        console.log('\n🎉 All tests completed successfully!');

    } catch (error) {
        console.error('\n❌ Test failed:', error);
        process.exit(1);
    }
}

// Run the tests
testInstanceManagement();
