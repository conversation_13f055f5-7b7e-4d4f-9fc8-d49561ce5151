<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>N8N Instance Management Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        input {
            width: 100%;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button.danger {
            background-color: #dc3545;
        }
        button.danger:hover {
            background-color: #c82333;
        }
        .instance-card {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 10px;
            background-color: #f8f9fa;
        }
        .instance-card h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.default {
            background-color: #28a745;
            color: white;
        }
        .status.healthy {
            background-color: #17a2b8;
            color: white;
        }
        .status.unhealthy {
            background-color: #dc3545;
            color: white;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin-top: 20px;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 N8N Instance Management Test</h1>
        
        <!-- Create Instance Form -->
        <div class="form-section">
            <h2>Create New Instance</h2>
            <div class="form-group">
                <label for="instanceName">Instance Name:</label>
                <input type="text" id="instanceName" placeholder="My N8N Instance" value="Test Instance">
            </div>
            <div class="form-group">
                <label for="instanceUrl">N8N URL:</label>
                <input type="text" id="instanceUrl" placeholder="https://n8n.example.com" value="https://test.n8n.example.com">
            </div>
            <div class="form-group">
                <label for="instanceApiKey">API Key:</label>
                <input type="password" id="instanceApiKey" placeholder="n8n_api_key_..." value="test_api_key_12345">
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="isDefault"> Set as default instance
                </label>
            </div>
            <button onclick="createInstance()">Create Instance</button>
        </div>

        <!-- Actions -->
        <div class="actions">
            <h2>Actions</h2>
            <button onclick="loadInstances()">🔄 Refresh Instances</button>
            <button onclick="testAllConnections()">🧪 Test All Connections</button>
            <button onclick="clearLog()">🗑️ Clear Log</button>
        </div>

        <!-- Instances List -->
        <div class="instances-section">
            <h2>N8N Instances</h2>
            <div id="instancesList"></div>
        </div>

        <!-- Log -->
        <div class="log" id="log"></div>
    </div>

    <script type="module">
        import { invoke } from '@tauri-apps/api/core';
        
        let instances = [];

        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        window.clearLog = function() {
            document.getElementById('log').innerHTML = '';
        }

        window.loadInstances = async function() {
            try {
                log('📋 Loading instances...');
                instances = await invoke('list_n8n_instances', {
                    limit: null,
                    offset: null
                });
                log(`✅ Loaded ${instances.length} instances`);
                renderInstances();
            } catch (error) {
                log(`❌ Failed to load instances: ${error}`);
            }
        }

        window.createInstance = async function() {
            const name = document.getElementById('instanceName').value;
            const url = document.getElementById('instanceUrl').value;
            const apiKey = document.getElementById('instanceApiKey').value;
            const isDefault = document.getElementById('isDefault').checked;

            if (!name || !url || !apiKey) {
                log('❌ Please fill in all fields');
                return;
            }

            try {
                log('🔨 Creating instance...');
                const createData = { name, url, api_key: apiKey, is_default: isDefault };
                const result = await invoke('create_n8n_instance', { createData });
                log(`✅ Created instance: ${result.name} (ID: ${result.id})`);
                
                // Clear form
                document.getElementById('instanceName').value = '';
                document.getElementById('instanceUrl').value = '';
                document.getElementById('instanceApiKey').value = '';
                document.getElementById('isDefault').checked = false;
                
                loadInstances();
            } catch (error) {
                log(`❌ Failed to create instance: ${error}`);
            }
        }

        window.deleteInstance = async function(id, name) {
            if (!confirm(`Are you sure you want to delete "${name}"?`)) return;

            try {
                log(`🗑️ Deleting instance: ${name}`);
                await invoke('delete_n8n_instance', { id: id });
                log(`✅ Deleted instance: ${name}`);
                loadInstances();
            } catch (error) {
                log(`❌ Failed to delete instance: ${error}`);
            }
        }

        window.testConnection = async function(id, name) {
            try {
                log(`🧪 Testing connection for: ${name}`);
                const result = await invoke('ping_n8n_instance', { id: id });
                log(`${result ? '✅' : '❌'} Connection test for ${name}: ${result ? 'SUCCESS' : 'FAILED'}`);
                loadInstances(); // Refresh to update health status
            } catch (error) {
                log(`❌ Connection test failed for ${name}: ${error}`);
            }
        }

        window.setDefault = async function(id, name) {
            try {
                log(`⭐ Setting ${name} as default...`);
                await invoke('set_default_n8n_instance', { id: id });
                log(`✅ Set ${name} as default instance`);
                loadInstances();
            } catch (error) {
                log(`❌ Failed to set default: ${error}`);
            }
        }

        window.testAllConnections = async function() {
            log('🧪 Testing all connections...');
            for (const instance of instances) {
                await testConnection(instance.id, instance.name);
            }
        }

        function renderInstances() {
            const container = document.getElementById('instancesList');
            
            if (instances.length === 0) {
                container.innerHTML = '<p>No instances found. Create your first instance above.</p>';
                return;
            }

            container.innerHTML = instances.map(instance => `
                <div class="instance-card">
                    <h3>
                        ${instance.name}
                        ${instance.is_default ? '<span class="status default">DEFAULT</span>' : ''}
                        <span class="status ${instance.health_status}">${instance.health_status.toUpperCase()}</span>
                    </h3>
                    <p><strong>URL:</strong> ${instance.url}</p>
                    <p><strong>Created:</strong> ${new Date(instance.created_at).toLocaleString()}</p>
                    ${instance.stats ? `
                        <p><strong>Stats:</strong> ${instance.stats.total_workflows} workflows, ${instance.stats.total_executions} executions</p>
                    ` : ''}
                    <div>
                        <button onclick="testConnection(${instance.id}, '${instance.name}')">🧪 Test</button>
                        ${!instance.is_default ? `<button onclick="setDefault(${instance.id}, '${instance.name}')">⭐ Set Default</button>` : ''}
                        <button class="danger" onclick="deleteInstance(${instance.id}, '${instance.name}')">🗑️ Delete</button>
                    </div>
                </div>
            `).join('');
        }

        // Load instances on page load
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 N8N Instance Management Test loaded');
            loadInstances();
        });
    </script>
</body>
</html>
